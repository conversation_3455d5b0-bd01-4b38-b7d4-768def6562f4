import { useRequest } from 'ahooks';

import { ApiResponse, PoiSelectorConfig } from '../types/PoiSelector';

import useCallerRequest from '@/hooks/useCallerRequest';

/**
 * 自定义 Hook：获取 POI 选择器配置
 * @returns 配置数据
 */
export const usePoiSelectorConfig = (poiSelectorOpen: boolean) => {
    const callerRequest = useCallerRequest();

    // 获取组件配置信息
    const getComponentConfig = async (): Promise<PoiSelectorConfig> => {
        const res: ApiResponse = await callerRequest.get(
            '/bee/v2/bdaiassistant/common/getComponentConfig',
            {},
            { host: 'https://yapi.sankuai.com/mock/37154' },
        );

        if (res.code === 0) {
            const poiSelector = res.data?.poiSelector;
            return {
                isMultiSelect: poiSelector?.type === 'MultiSelect',
                defaultList: poiSelector?.defaultList || [],
            };
        }

        return {
            isMultiSelect: false,
            defaultList: [],
        };
    };

    // 获取组件配置
    const { data: poiSelectorConfig } = useRequest(
        async () => {
            if (!poiSelectorOpen) {
                return poiSelectorConfig;
            }
            return await getComponentConfig();
        },
        { refreshDeps: [poiSelectorOpen] },
    );

    return {
        poiSelectorConfig,
        isMultiSelect: poiSelectorConfig?.isMultiSelect || false,
        defaultList: poiSelectorConfig?.defaultList || [],
    };
};
