import { View, Text, TouchableOpacity } from '@mrn/react-native';
import React from 'react';

interface ConfirmButtonProps {
    selectedCount: number;
    onConfirm: () => void;
}

/**
 * 确认按钮组件
 */
export const ConfirmButton = ({
    selectedCount,
    onConfirm,
}: ConfirmButtonProps) => (
    <View style={{ marginBottom: 12 }}>
        <TouchableOpacity
            onPress={onConfirm}
            disabled={selectedCount === 0}
            style={[
                {
                    backgroundColor: selectedCount > 0 ? '#FFD100' : '#E7E8E9',
                    borderRadius: 8,
                    paddingVertical: 12,
                    alignItems: 'center',
                },
            ]}
        >
            <Text
                style={{
                    color: selectedCount > 0 ? '#333' : '#999',
                    fontSize: 16,
                    fontWeight: '500',
                }}
            >
                确定选择 {selectedCount > 0 ? `(${selectedCount}/${50})` : ''}
            </Text>
        </TouchableOpacity>
    </View>
);
