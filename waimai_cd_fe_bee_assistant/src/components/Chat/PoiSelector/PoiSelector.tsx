// 商家选择器
import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    Dimensions,
    Platform,
    TouchableWithoutFeedback,
    FlatList,
    RefreshControl,
} from '@mrn/react-native';
import { useDebounceEffect } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';

import { ConfirmButton, ListEmptyComponent, PoiItem, Spin } from './components';
import { usePoiData, usePoiSelectorConfig, useValidSelection } from './hooks';
import { PoiItemData } from './types/PoiSelector';
import {
    createSinglePoiMessage,
    createMultiPoiMessage,
} from './utils/messageUtils';
import closeBlackImg from '../../../assets/images/poiSelector/closeBlack.png';
import useKeyboard from '../../../hooks/useKeyboard';
import { useSendMessage } from '../../../hooks/useSendMessage';
import { useUiState } from '../../../store/uiState';
import TWS from '../../../TWS';
import { EntryPoint, EntryPointType } from '../../../types';
import Condition from '../../Condition/Condition';
import SearchBar from '../../SearchBar';

const PoiSelector = () => {
    const [searchValue, setSearchValue] = useState('');

    const {
        poiSelectorOpen,
        setPoiSelectorOpen,
        poiSelectorSelectedList,
        setPoiSelectorSelectedList,
    } = useUiState();

    // 使用拆分出来的 hooks
    const { pagination, poiList, loading } = usePoiData(searchValue);
    const {
        isMultiSelect: poiSelectorMultiSelect,
        defaultList: poiSelectorDefaultList,
    } = usePoiSelectorConfig(poiSelectorOpen);

    useDebounceEffect(
        () => {
            pagination.onChange(1, 10);
        },
        [searchValue],
        { wait: 200 },
    );

    // 如果没数据，进弹窗重新请求数据
    useEffect(() => {
        setSearchValue('');
        if (poiSelectorOpen && !poiList?.length) {
            pagination.onChange(1, 10);
        }
    }, [poiSelectorOpen]);

    // 当通过接口获取的 defaultList 加载完成后，在多选模式下自动设置为选中状态
    useEffect(() => {
        if (
            poiSelectorMultiSelect &&
            poiSelectorDefaultList?.length > 0 &&
            poiSelectorSelectedList.length === 0 // 只在没有已选项时自动选中
        ) {
            setPoiSelectorSelectedList([...poiSelectorDefaultList]);
        }
    }, [poiSelectorDefaultList]);

    // 合并默认列表和搜索结果
    const combinedPoiList = React.useMemo(() => {
        const defaultList = searchValue ? [] : poiSelectorDefaultList;
        const searchList = poiList || [];

        // 合并并去重，默认列表优先
        const combined = [...defaultList];
        const defaultIds = new Set(
            defaultList.map((item: PoiItemData) => item.id),
        );

        // 只添加不在默认列表中的搜索结果
        searchList.forEach((item: PoiItemData) => {
            if (!defaultIds.has(item.id)) {
                combined.push(item);
            }
        });

        return combined;
    }, [poiSelectorDefaultList, poiList, searchValue]);

    // 使用自定义 Hook 管理有效的选中列表（combinedPoiList 和 poiSelectorSelectedList 的交集）
    const validSelectedList = useValidSelection(
        combinedPoiList,
        poiSelectorSelectedList,
        setPoiSelectorSelectedList,
    );

    // 处理选择逻辑
    const handleToggleSelect = (poi: PoiItemData) => {
        const isSelected = poiSelectorSelectedList.some(
            (item) => item.id === poi.id,
        );
        if (isSelected) {
            setPoiSelectorSelectedList(
                poiSelectorSelectedList.filter((item) => item.id !== poi.id),
            );
        } else {
            setPoiSelectorSelectedList([...poiSelectorSelectedList, poi]);
        }
    };

    // 发送多选结果
    const handleConfirmMultiSelect = () => {
        if (validSelectedList.length === 0) {
            return;
        }

        const message =
            validSelectedList.length === 1
                ? createSinglePoiMessage(validSelectedList[0])
                : createMultiPoiMessage(validSelectedList);

        send(
            JSON.stringify(message),
            EntryPointType.POI_SELECTOR,
            EntryPoint.poi_select,
        );
        setPoiSelectorOpen(false);
    };

    const { send } = useSendMessage();
    const insets = useSafeAreaInsets();
    const { keyboardOffset } = useKeyboard();
    const wrapper = useRef();

    if (!poiSelectorOpen) {
        return null;
    }
    return (
        <TouchableWithoutFeedback
            onPress={(e) => {
                if (e.target === wrapper.current) {
                    setPoiSelectorOpen(false);
                }
            }}
        >
            <View
                ref={wrapper}
                style={[
                    {
                        backgroundColor: 'rgba(34,34,34,0.6)',
                        width: '100%',
                        flex: 1,
                        position: 'absolute',
                        top: -insets.top, // 向上扩展到StatusBar区域
                        paddingTop: insets.top, // 添加顶部安全区域内边距
                        marginBottom: -insets.bottom, // 覆盖底部安全区域底色
                        paddingBottom: insets.bottom,
                        bottom: 0,
                        left: 0,
                    },
                ]}
            >
                <View
                    style={{
                        maxHeight: Platform.select({
                            ios:
                                Dimensions.get('window').height -
                                keyboardOffset -
                                insets.top,
                            android:
                                Dimensions.get('window').height -
                                keyboardOffset,
                        }),
                        height: 478,
                        position: 'absolute',
                        bottom: Platform.select({
                            ios: keyboardOffset,
                            android: 0,
                        }),
                        left: 0,
                        width: '100%',
                        backgroundColor: '#fff',
                        borderTopLeftRadius: 20,
                        borderTopRightRadius: 20,
                        padding: 10,
                    }}
                >
                    <View
                        style={[
                            TWS.row(),
                            TWS.center(),
                            { height: 50, justifyContent: 'space-between' },
                        ]}
                    >
                        {/* 占位 */}
                        <Text />
                        <Text
                            style={{
                                color: '#222',
                                fontSize: 16,
                                fontWeight: '500',
                            }}
                        >
                            请选择你想查询的商家
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                setPoiSelectorOpen(false);
                            }}
                        >
                            <Image
                                source={closeBlackImg}
                                style={[TWS.square(16)]}
                            />
                        </TouchableOpacity>
                    </View>
                    <View
                        style={[
                            TWS.line({
                                width: Dimensions.get('window').width,
                            }),
                            { marginLeft: -10, marginBottom: 12 },
                        ]}
                    />
                    <SearchBar onChange={setSearchValue} />
                    <Condition condition={[poiSelectorMultiSelect]}>
                        <ConfirmButton
                            selectedCount={validSelectedList.length}
                            onConfirm={handleConfirmMultiSelect}
                        />
                    </Condition>
                    {/* 使用 Spin 组件包装列表，提供 loading 遮罩 */}
                    <Spin
                        loading={loading && !!searchValue}
                        tip="搜索中..."
                        color="#FFD100"
                    >
                        <FlatList
                            refreshControl={
                                <RefreshControl
                                    refreshing={false}
                                    onRefresh={() => pagination.onChange(1, 10)}
                                />
                            }
                            showsVerticalScrollIndicator={false}
                            style={{ flex: 1 }}
                            keyExtractor={(v) => String(v.id)}
                            onEndReached={() => {
                                if (
                                    pagination.current >=
                                    Math.ceil(
                                        pagination.total / pagination.pageSize,
                                    )
                                ) {
                                    return;
                                }
                                pagination.onChange(pagination.current + 1, 10);
                            }}
                            onEndReachedThreshold={0.1}
                            data={combinedPoiList}
                            ListEmptyComponent={ListEmptyComponent}
                            renderItem={({ item, index }) => {
                                const { id, name, url, online, labels, tags } =
                                    item;
                                const isSelected = poiSelectorSelectedList.some(
                                    (selectedItem) => selectedItem.id === id,
                                );

                                return (
                                    <>
                                        <PoiItem
                                            isLast={
                                                index ===
                                                combinedPoiList.length - 1
                                            }
                                            id={id}
                                            name={name}
                                            url={url}
                                            online={online}
                                            labels={labels}
                                            tags={tags}
                                            isMultiSelect={
                                                poiSelectorMultiSelect
                                            }
                                            isSelected={isSelected}
                                            onToggleSelect={() =>
                                                handleToggleSelect(item)
                                            }
                                            onPress={() => {
                                                if (!poiSelectorMultiSelect) {
                                                    const message =
                                                        createSinglePoiMessage(
                                                            item,
                                                        );
                                                    send(
                                                        JSON.stringify(message),
                                                        EntryPointType.POI_SELECTOR,
                                                    );
                                                    setPoiSelectorOpen(false);
                                                }
                                            }}
                                        />
                                    </>
                                );
                            }}
                        />
                    </Spin>
                </View>
            </View>
        </TouchableWithoutFeedback>
    );
};
export default PoiSelector;
