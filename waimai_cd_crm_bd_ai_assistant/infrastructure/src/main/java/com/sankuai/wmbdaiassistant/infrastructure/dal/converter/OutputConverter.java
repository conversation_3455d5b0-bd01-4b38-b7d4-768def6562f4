package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.domain.enums.OutputTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Output;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * Output
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 16:00
 */
public class OutputConverter {
    private static final String SEPARATOR = "\n";

    public static OutputModel of(Output output) {
        if (output == null) {
            return null;
        }
        OutputModel outputModel = new OutputModel();

        outputModel.setId(output.getId());
        outputModel.setContent(output.getContent());
        outputModel.setType(OutputTypeEnum.getByCode(output.getType()));
        outputModel.setTtUrl(output.getTtUrl());
        if (StringUtils.isNotBlank(output.getPicUrlList())) {
            outputModel.setPicUrlList(new ArrayList<>(Arrays.asList(output.getPicUrlList().split(SEPARATOR))));
        }
        if (StringUtils.isNotBlank(output.getTags())) {
            outputModel.setTags(new ArrayList<>(Arrays.asList(output.getTags().substring(1
                    , output.getTags().length() - 1).split(";"))));
        }
        outputModel.setCreateTime(output.getCtime());
        outputModel.setModifyTime(output.getUtime());
        outputModel.setValid(ValidEnum.getByCode(output.getValid()));

        return outputModel;
    }

    public static Output to(OutputModel outputModel) {
        if (outputModel == null) {
            return null;
        }

        Output output = new Output();

        output.setId(outputModel.getId());
        output.setContent(outputModel.getContent());
        output.setType(outputModel.getType() == null ? null : outputModel.getType().getCode());
        output.setTtUrl(outputModel.getTtUrl());
        if (CollectionUtils.isNotEmpty(outputModel.getPicUrlList())) {
            output.setPicUrlList(String.join(SEPARATOR, outputModel.getPicUrlList()));
        }
        if (CollectionUtils.isNotEmpty(outputModel.getTags())) {
            output.setTags(String.format(";%s;", String.join(";", outputModel.getTags())));
        }
        output.setCtime(outputModel.getCreateTime());
        output.setUtime(outputModel.getModifyTime());
        output.setValid(outputModel.getValid() == null ? null  : outputModel.getValid().getCode());

        return output;
    }
}
