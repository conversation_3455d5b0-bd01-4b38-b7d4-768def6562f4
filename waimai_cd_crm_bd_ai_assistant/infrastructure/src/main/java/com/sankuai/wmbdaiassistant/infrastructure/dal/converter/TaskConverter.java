package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.dianping.zebra.util.StringUtils;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import com.sankuai.wmbdaiassistant.domain.model.TaskModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Task;

/**
 * task
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 16:10
 */
public class TaskConverter {
    public static TaskModel of(Task task) {
        if(task == null) {
            return null;
        }
        TaskModel taskModel = new TaskModel();
        taskModel.setId(task.getId());
        taskModel.setName(task.getName());
        taskModel.setDescription(task.getDescription());
        taskModel.setPrompt(task.getPrompt());
        taskModel.setModel(task.getModel());
        taskModel.setResources(task.getResources());
        taskModel.setConfig(StringUtils.isNotBlank(task.getConfig())
                ? JsonUtil.fromJson(task.getConfig(), TaskModel.TaskConfigModel.class)
                : TaskModel.TaskConfigModel.builder().build());
        taskModel.setCreateTime(task.getCtime());
        taskModel.setModifyTime(task.getUtime());
        taskModel.setValid(ValidEnum.getByCode(task.getValid()));
        return taskModel;
    }

    public static Task to(TaskModel taskModel) {
        if(taskModel == null) {
            return null;
        }
        Task task = new Task();
        task.setId(taskModel.getId());
        task.setName(taskModel.getName());
        task.setDescription(taskModel.getDescription());
        task.setPrompt(taskModel.getPrompt());
        task.setModel(taskModel.getModel());
        task.setResources(taskModel.getResources());
        task.setConfig(JsonUtil.toJson(taskModel.getConfig()));
        task.setCtime(taskModel.getCreateTime());
        task.setUtime(taskModel.getModifyTime());
        task.setValid(taskModel.getValid() == null ? null : taskModel.getValid().getCode());
        return task;
    }
}
