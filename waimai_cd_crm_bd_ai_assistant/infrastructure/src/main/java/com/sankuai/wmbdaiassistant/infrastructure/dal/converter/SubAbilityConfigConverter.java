package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.SubAbilityConfig;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;

/**
 * SubAbilityConfig
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 15:06
 */
public class SubAbilityConfigConverter {

    public static SubAbilityConfigModel of(SubAbilityConfig subAbilityConfig) {
        if (subAbilityConfig == null) {
            return null;
        }
        SubAbilityConfigModel model = new SubAbilityConfigModel();
        model.setId(subAbilityConfig.getId());
        model.setName(subAbilityConfig.getName());
        model.setConfig(subAbilityConfig.getConfig());
        model.setAbilityType(AbilityTypeEnum.findByCode(subAbilityConfig.getAbilityType()));
        model.setValid(ValidEnum.getByCode(subAbilityConfig.getValid().byteValue()));

        return model;
    }

    public static SubAbilityConfig to(SubAbilityConfigModel subAbilityConfigModel) {
        if (subAbilityConfigModel == null) {
            return null;
        }
        SubAbilityConfig dataObj = new SubAbilityConfig();
        dataObj.setId(subAbilityConfigModel.getId());
        dataObj.setName(subAbilityConfigModel.getName());
        dataObj.setConfig(subAbilityConfigModel.getConfig());
        dataObj.setAbilityType(subAbilityConfigModel.getAbilityType() == null ? null : subAbilityConfigModel.getAbilityType().getCode());
        dataObj.setValid(subAbilityConfigModel.getValid() == null ? null : subAbilityConfigModel.getValid().getCode().intValue());

        return dataObj;
    }

}
