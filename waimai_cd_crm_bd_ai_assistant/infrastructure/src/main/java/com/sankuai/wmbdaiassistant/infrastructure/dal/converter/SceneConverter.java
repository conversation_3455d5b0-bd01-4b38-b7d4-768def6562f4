package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.model.SceneModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Phrase;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Scene;

/**
 * scene
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 16:05
 */
public class SceneConverter {

    public static SceneModel of(Scene scene) {
        if (scene == null) {
            return null;
        }

        return SceneModel.builder()
                .id(scene.getId())
                .name(scene.getName())
                .phraseId(scene.getPhraseId())
                .questionId(scene.getQuestionId())
                .ctime(scene.getCtime())
                .utime(scene.getUtime())
                .build();
    }

    public static Scene to(SceneModel sceneModel) {
        if (sceneModel == null) {
            return null;
        }

        return Scene.builder().id(sceneModel.getId()).name(sceneModel.getName()).phraseId(sceneModel.getPhraseId())
                .questionId(sceneModel.getQuestionId()).ctime(sceneModel.getCtime()).utime(sceneModel.getUtime())
                .build();
    }

}
