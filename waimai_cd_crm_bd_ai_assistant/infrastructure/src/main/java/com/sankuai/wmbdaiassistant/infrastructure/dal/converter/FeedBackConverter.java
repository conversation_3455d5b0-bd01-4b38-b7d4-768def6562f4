package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.FeedBack;
import com.sankuai.wmbdaiassistant.domain.enums.FeedBackTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.FeedBackModel;

/**
 * FeedBack
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 15:39
 */
public class FeedBackConverter {

    public static FeedBackModel of(FeedBack feedBack) {
        if (feedBack == null) {
            return null;
        }
        FeedBackModel feedbackModel = new FeedBackModel();
        feedbackModel.setId(feedBack.getId());
        feedbackModel.setChatRecordId(feedBack.getChatRecordId());
        feedbackModel.setUid(feedBack.getUid());
        feedbackModel.setMis(feedBack.getMis());
        feedbackModel.setType(FeedBackTypeEnum.getFeedBackType(feedBack.getType()));
        feedbackModel.setSuggestionContent(feedBack.getSuggestionContent());
        feedbackModel.setFeedbackTime(feedBack.getCtime());
        feedbackModel.setTtId(feedBack.getTtId());
        feedbackModel.setTtCreateTime(feedBack.getTtCreateTime());
        feedbackModel.setTtSolvedTime(feedBack.getTtSolvedTime());
        return feedbackModel;
    }

    public static FeedBack to(FeedBackModel feedbackModel) {
        if (feedbackModel == null) {
            return null;
        }
        FeedBack feedBack = new FeedBack();

        feedBack.setId(feedbackModel.getId());
        feedBack.setChatRecordId(feedbackModel.getChatRecordId());
        feedBack.setUid(feedbackModel.getUid());
        feedBack.setMis(feedbackModel.getMis());
        feedBack.setType(feedbackModel.getType() == null ? null : feedbackModel.getType().getCode());
        feedBack.setSuggestionContent(feedbackModel.getSuggestionContent());
        feedBack.setCtime(feedbackModel.getFeedbackTime());
        feedBack.setTtId(feedbackModel.getTtId());
        feedBack.setTtCreateTime(feedbackModel.getTtCreateTime());
        feedBack.setTtSolvedTime(feedbackModel.getTtSolvedTime());

        return feedBack;
    }
}
