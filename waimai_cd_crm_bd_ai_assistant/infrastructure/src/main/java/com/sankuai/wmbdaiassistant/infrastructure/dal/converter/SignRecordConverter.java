package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.SignRecord;
import com.sankuai.wmbdaiassistant.domain.model.SignRecordModel;

/**
 * SignRecord
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 15:16
 */
public class SignRecordConverter {

    public static SignRecordModel of(SignRecord signRecord) {
        if (signRecord == null) {
            return null;
        }
        SignRecordModel model = new SignRecordModel();
        model.setId(signRecord.getId());
        model.setUid(signRecord.getUid());
        model.setMis(signRecord.getMis());
        model.setSignTime(signRecord.getCtime());
        return model;
    }

    public static SignRecord to(SignRecordModel signRecordModel) {
        if (signRecordModel == null) {
            return null;
        }
        SignRecord signRecord = new SignRecord();
        signRecord.setId(signRecordModel.getId());
        signRecord.setUid(signRecordModel.getUid());
        signRecord.setMis(signRecordModel.getMis());
        signRecord.setCtime(signRecordModel.getSignTime());
        return signRecord;
    }

}
