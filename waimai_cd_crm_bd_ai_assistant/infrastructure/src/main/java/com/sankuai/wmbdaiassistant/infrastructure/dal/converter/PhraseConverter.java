package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.domain.enums.PhraseStateEnum;
import com.sankuai.wmbdaiassistant.domain.enums.TriggerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Phrase;

/**
 * phrase
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 16:03
 */
public class PhraseConverter {

    public static PhraseModel of(Phrase phrase) {
        if (phrase == null) {
            return null;
        }

        return PhraseModel.builder()
                .id(phrase.getId())
                .phrase(phrase.getPhrase())
                .standardizedPhrase(phrase.getStandardizedPhrase())
                .triggerId(phrase.getTriggerId())
                .triggerType(TriggerTypeEnum.getByCode(phrase.getTriggerType()))
                .modifierMis(phrase.getModifierMis())
                .modifierName(phrase.getModifierName())
                .state(PhraseStateEnum.getByCode(phrase.getState()))
                .domainId(phrase.getDomainId())
                .createTime(phrase.getCtime())
                .modifyTime(phrase.getUtime())
                .vexId(phrase.getVexId())
                .categoryId(phrase.getCategoryId())
                .sortOrder(phrase.getSortOrder())
                .build();
    }

    public static Phrase to(PhraseModel phraseModel) {
        if (phraseModel == null) {
            return null;
        }

        return Phrase.builder()
                .id(phraseModel.getId())
                .phrase(phraseModel.getPhrase())
                .standardizedPhrase(phraseModel.getStandardizedPhrase())
                .triggerId(phraseModel.getTriggerId())
                .triggerType(phraseModel.getTriggerType() == null ? null : phraseModel.getTriggerType().getCode())
                .modifierMis(phraseModel.getModifierMis())
                .modifierName(phraseModel.getModifierName())
                .state(phraseModel.getState() == null ? null : phraseModel.getState().getCode())
                .domainId(phraseModel.getDomainId())
                .ctime(phraseModel.getCreateTime())
                .utime(phraseModel.getModifyTime())
                .vexId(phraseModel.getVexId())
                .categoryId(phraseModel.getCategoryId())
                .sortOrder(phraseModel.getSortOrder())
                .build();
    }

}
