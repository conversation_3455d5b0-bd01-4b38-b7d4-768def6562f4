package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.SessionRemark;
import com.sankuai.wmbdaiassistant.domain.model.SessionRemarkModel;

/**
 * SessionRemark
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 16:08
 */
public class SessionRemarkConverter {

    public static SessionRemarkModel of(SessionRemark sessionRemark) {
        if (sessionRemark == null) {
            return null;
        }

        SessionRemarkModel model = new SessionRemarkModel();

        model.setId(sessionRemark.getId());
        model.setSessionId(sessionRemark.getSessionId());
        model.setMis(sessionRemark.getMis());
        model.setRemark(sessionRemark.getRemark());
        model.setCreateTime(sessionRemark.getCtime());
        model.setModifyTime(sessionRemark.getUtime());

        return model;
    }

    public static SessionRemark to(SessionRemarkModel model) {
        if(model == null) {
            return null;
        }
        SessionRemark sessionRemark = new SessionRemark();

        sessionRemark.setId(model.getId());
        sessionRemark.setMis(model.getMis());
        sessionRemark.setRemark(model.getRemark());
        sessionRemark.setSessionId(model.getSessionId());
        sessionRemark.setCtime(model.getCreateTime());
        sessionRemark.setUtime(model.getModifyTime());

        return sessionRemark;
    }
}
