package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.enums.SessionStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.SessionModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Session;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * session
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 16:06
 */
public class SessionConverter {

    public static SessionModel of(Session session) {
        if (session == null) {
            return null;
        }

        SessionModel sessionModel = new SessionModel();

        sessionModel.setId(session.getId());
        sessionModel.setUid(session.getUid());
        sessionModel.setMis(session.getMis());
        sessionModel.setStatus(SessionStatusEnum.getByCode(session.getStatus()));
        sessionModel.setCreateTime(session.getCtime());
        sessionModel.setModifyTime(session.getUtime());
        sessionModel.setSource(session.getSource());
        sessionModel.setTags(new HashSet<>(parseFromTags(session.getTags())));
        sessionModel.setExtra(session.getExtra());

        return sessionModel;
    }

    public static Session to(SessionModel sessionModel) {
        if (sessionModel == null) {
            return null;
        }
        Session session = new Session();

        session.setId(sessionModel.getId());
        session.setUid(sessionModel.getUid());
        session.setMis(sessionModel.getMis());
        session.setStatus(sessionModel.getStatus() == null ? null : sessionModel.getStatus().getCode());
        session.setSource(sessionModel.getSource());
        session.setCtime(sessionModel.getCreateTime());
        session.setUtime(sessionModel.getModifyTime());
        session.setTags(generateSessionTags(sessionModel.getTags()));
        session.setExtra(JsonUtil.toJson(sessionModel.getExtra()));
        return session;
    }

    private static String generateSessionTags(Set<String> tags) {
        return String.format(";%s;", StringUtils.join(DefaultUtil.defaultSet(tags)
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet()), ";"));
    }

    private static Set<String> parseFromTags(String tags) {
        if (StringUtils.isBlank(tags) || tags.length() < 2) {
            return Collections.emptySet();
        }
        return new HashSet<>(Arrays.stream(tags.substring(1, tags.length() - 1).split(";")).filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet()));
    }
}
