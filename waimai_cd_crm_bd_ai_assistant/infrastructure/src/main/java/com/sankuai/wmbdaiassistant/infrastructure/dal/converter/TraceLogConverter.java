package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.TraceLog;

/**
 * TraceLog 转换器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-28 17:18
 */
public class TraceLogConverter {

    public static TraceLogModel from(TraceLog traceLog) {
        if (traceLog == null) {
            return null;
        }

        TraceLogModel model = new TraceLogModel();
        model.setId(traceLog.getId());
        model.setSessionId(traceLog.getSessionId());
        model.setEntryPoint(traceLog.getEntryPoint());
        model.setEventType(traceLog.getEventType());
        model.setContent(traceLog.getContent());
        model.setDate(traceLog.getCtime());
        return model;
    }

    public static TraceLog to(TraceLogModel model) {
        if (model == null) {
            return null;
        }
        TraceLog traceLog = new TraceLog();

        traceLog.setId(model.getId());
        traceLog.setSessionId(model.getSessionId());
        traceLog.setEntryPoint(model.getEntryPoint());
        traceLog.setEventType(model.getEventType());
        traceLog.setContent(model.getContent());
        traceLog.setCtime(model.getDate());

        return traceLog;
    }
}
