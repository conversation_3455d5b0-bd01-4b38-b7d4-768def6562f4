package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.enums.WikiSourceTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.WikiStateEnum;
import com.sankuai.wmbdaiassistant.domain.model.WikiModel;
import org.apache.commons.collections4.MapUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;

/**
 * wiki converter
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 17:29
 */
public class WikiConverter {

    public static WikiModel of(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return WikiModel.builder()
                .id(String.valueOf(map.get(WikiModel.ES_ID)))
                .wikiId(Long.valueOf(map.get(WikiModel.WIKI_ID)))
                .title(map.get(WikiModel.TITLE))
                .url(map.get(WikiModel.URL))
                .sourceType(WikiSourceTypeEnum.getByCode(map.get(WikiModel.SOURCE_TYPE)))
                .datasetId(Long.valueOf(map.get(WikiModel.DATA_SET_ID)))
                .tags(Arrays.asList(DefaultUtil.defaultString(map.get(WikiModel.TAGS)).split(",")))
                .mis(map.get(WikiModel.MIS))
                .state(WikiStateEnum.getByCode(map.get(WikiModel.STATE)))
                .autoUpdate(Boolean.valueOf(map.get(WikiModel.AUTO_UPDATE)))
                .batchId(map.get(WikiModel.BATCH_ID))
                .needSubWiki(Boolean.valueOf(map.get(WikiModel.NEED_SUB_WIKI)))
                .needReferWiki(Boolean.valueOf(map.get(WikiModel.NEED_REFER_WIKI)))
                .hasFormatProblem(Boolean.valueOf(map.get(WikiModel.HAS_FORMAT_PROBLEM)))
                .syncWikiAuth(Boolean.valueOf(map.get(WikiModel.SYNC_WIKI_AUTH)))
                .createTime(new Date(Long.parseLong(map.get(WikiModel.CREATE_TIME))))
                .updateTime(new Date(Long.parseLong(map.get(WikiModel.UPDATE_TIME))))
                .build();
    }
}
