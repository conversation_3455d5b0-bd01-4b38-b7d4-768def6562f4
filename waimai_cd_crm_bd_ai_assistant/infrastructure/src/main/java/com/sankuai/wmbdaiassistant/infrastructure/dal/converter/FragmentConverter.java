package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.enums.FragmentStateEnum;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import org.apache.commons.collections4.MapUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;

/**
 * 分片转换器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 18:59
 */
public class FragmentConverter {

    public static FragmentModel of(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return FragmentModel.builder()
                .id(map.get(FragmentModel.ID))
                .wikiId(Long.valueOf(map.get(FragmentModel.WIKI_ID)))
                .title(map.get(FragmentModel.TITLE))
                .content(map.get(FragmentModel.CONTENT))
                .chunkId(Integer.parseInt(map.get(FragmentModel.CHUNK_ID)))
                .state(FragmentStateEnum.getByCode(map.get(FragmentModel.STATE)))
                .datasetId(Long.valueOf(map.get(FragmentModel.DATA_SET_ID)))
                .tags(Arrays.asList(DefaultUtil.defaultString(map.get(FragmentModel.TAGS)).split(",")))
                .batchId(map.get(FragmentModel.BATCH_ID))
                .createTime(new Date(Long.parseLong(map.get(FragmentModel.CREATE_TIME))))
                .updateTime(new Date(Long.parseLong(map.get(FragmentModel.UPDATE_TIME))))
                .build();
    }
}
