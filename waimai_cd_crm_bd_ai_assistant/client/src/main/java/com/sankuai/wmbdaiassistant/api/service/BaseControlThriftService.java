package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.wmbdaiassistant.api.request.*;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * <AUTHOR>
 * @date 2023-12-04
 */
@InterfaceDoc(
        displayName = "BD智能助手基础控制接口",
        type = "octo.thrift.annotation",
        description = "BD智能助手基础控制接口",
        scenarios = ""
)
@ThriftService
public interface BaseControlThriftService {

    @MethodDoc(
            displayName = "",
            description = "BD智能助手灰度开关",
            parameters = {@ParamDoc(
                    name = "graySwitchRequestDto",
                    description = "BD智能助手灰度请求参数"
            )},
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto getGraySwitch(GraySwitchRequestDto graySwitchRequestDto);

    @MethodDoc(
            displayName = "",
            description = "BD智能助手支持技能",
            parameters = {@ParamDoc(
                    name = "beeRequestDto",
                    description = "BD智能助手支持技能请求参数"
            )},
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto getWmBdAbilityType(BeeRequestDto beeRequestDto);

    @MethodDoc(
            displayName = "",
            description = "获取BD智能助手热门问题",
            parameters = {@ParamDoc(
                    name = "beeRequestDto",
                    description = "获取BD智能助手热门问题"
            )},
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto getHotQuestions(BeeRequestDto requestDto);

    @MethodDoc(
        displayName = "",
        description = "获取BD智能助手工具栏配置",
        parameters = {@ParamDoc(
            name = "beeRequestDto",
            description = "获取BD智能助手工具栏配置请求参数"
        )},
        returnValueDescription = "",
        example = "无"
    )
    @ThriftMethod
    BeeResponseDto getToolbarConfigs(BeeRequestDto beeRequestDto);

    @MethodDoc(
            displayName = "",
            description = "BD智能助手工具栏顺序调整",
            parameters = {@ParamDoc(
                    name = "toolbarOrderRequestDto",
                    description = "BD智能助手工具栏顺序调整"
            )},
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto toolbarOrder(ToolbarOrderRequestDto requestDto);

    @MethodDoc(
            displayName = "",
            description = "打点",
            parameters = {@ParamDoc(
                    name = "beeRequestDto",
                    description = "打点参数"
            )},
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto traceLog(TraceLogRequestDto request);

    @MethodDoc(
            displayName = "",
            description = "获取用户信息",
            parameters = {@ParamDoc(
                    name = "beeRequestDto",
                    description = "获取用户信息请求参数"
            )},
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto fetchCurrentUserInfo(BeeRequestDto beeRequestDto);

    /**
     * 用于判断当前用户是否可以展示机器人
     *
     * @param beeRequestDto
     * @return
     */
    @ThriftMethod
    BeeResponseDto showRobot(BeeRequestDto beeRequestDto);

    /**
     * 获取机器人配置
     *
     * @param requestDto 请求
     * @return 配置
     */
    @ThriftMethod
    BeeResponseDto fetchRobotConfig(RobotConfigRequestDto requestDto);

    /**
     * 统一的灰度逻辑
     *
     * @param requestDto 请求
     * @return 配置
     */
    @ThriftMethod
    BeeResponseDto gray(BeeRequestDto requestDto);

    /**
     * 获取场景提示语
     *
     * @return 提示语
     */
    @ThriftMethod
    BeeResponseDto getSceneTips(BeeRequestDto requestDto);

    /**
     * 获取公告
     *
     * @param requestDto
     * @return
     */
    @ThriftMethod
    BeeResponseDto getAnnouncement(BeeRequestDto requestDto);

    /**
     * 关闭公告
     *
     * @param requestDto
     * @return
     */
    @ThriftMethod
    BeeResponseDto closeAnnouncement(AnnouncementCloseRequestDto requestDto);
}
