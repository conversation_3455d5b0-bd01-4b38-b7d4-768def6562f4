package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.BaseSessionRequestDto;
import com.sankuai.wmbdaiassistant.api.request.BeeRequestDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
@ThriftService
public interface ChatSessionThriftService {

    @ThriftMethod
    BeeResponseDto open(BaseSessionRequestDto requestDto);

    @ThriftMethod
    BeeResponseDto close(BeeRequestDto requestDto);

    @ThriftMethod
    BeeResponseDto refresh(BaseSessionRequestDto requestDto);
}
