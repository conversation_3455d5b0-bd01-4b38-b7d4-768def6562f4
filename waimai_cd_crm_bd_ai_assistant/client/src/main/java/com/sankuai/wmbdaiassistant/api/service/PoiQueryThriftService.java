package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.BeeRequestDto;
import com.sankuai.wmbdaiassistant.api.request.PoiQueryRequestDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * 商家信息查询服务
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@ThriftService
public interface PoiQueryThriftService {
    @ThriftMethod
    BeeResponseDto queryPoiByCurrentUser(BeeRequestDto beeRequestDto);

    @ThriftMethod
    BeeResponseDto queryPoiByPage(PoiQueryRequestDto poiQueryRequestDto);
}
