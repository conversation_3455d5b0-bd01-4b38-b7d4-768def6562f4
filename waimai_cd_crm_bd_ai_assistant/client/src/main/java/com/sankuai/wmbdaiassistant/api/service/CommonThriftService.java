package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.common.WeatherGreetingRequestDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * 通用服务接口
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@ThriftService
public interface CommonThriftService {

    /**
     * 根据经纬度获取天气问候信息
     *
     * @Param requestDto 经纬度
     * @return 天气问候响应
     */
    @ThriftMethod
    BeeResponseDto getWeatherGreeting(WeatherGreetingRequestDto requestDto);
}