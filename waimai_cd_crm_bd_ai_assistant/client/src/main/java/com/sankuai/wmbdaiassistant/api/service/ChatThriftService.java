package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.*;
import com.sankuai.wmbdaiassistant.api.response.BeeListResponseDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/5
 **/
@ThriftService
public interface ChatThriftService {

    /**
     * 提交问题
     *
     * @return
     */
    @ThriftMethod
    BeeResponseDto submitQuery(ChatSubmitQueryRequestDto requestDto);

    /**
     * 获取问题回答
     */
    @ThriftMethod
    BeeResponseDto fetchAnswer(ChatFetchAnswerRequestDto requestDto);

    /**
     * 反馈
     */
    @ThriftMethod
    BeeResponseDto feedBackForChat(FeedBackRequestDto requestDto);

    /**
     * 获取下一组回答
     */
    @ThriftMethod
    BeeResponseDto fetchNextGroupAnswer(ChatFetchAnswerRequestDto requestDto);

    /**
     * 获取历史聊天记录
     */
    @ThriftMethod
    BeeResponseDto getHistoryMsg(ChatFetchHistoryMsgRequestDto requestDto);

    @ThriftMethod
    BeeResponseDto getRelatedQuestion(RelateQuestionRequestDto requestDto);

    /**
     * 通过其他场景调起BD智能助手
     *
     * @return
     */
    @ThriftMethod
    BeeListResponseDto triggerScene(TriggerSceneRequestDto requestDto);

    /**
     * 获取目录挂载节点信息数据(换一换目录信息)
     */
    @ThriftMethod
    BeeResponseDto fetchCategoryData(FetchCategoryDataRequestDto requestDto);

    /**
     * 获取粘贴板内容
     */
    @ThriftMethod
    BeeResponseDto fetchClipboardContent(ClipboardContentRequestDto requestDto);
}
