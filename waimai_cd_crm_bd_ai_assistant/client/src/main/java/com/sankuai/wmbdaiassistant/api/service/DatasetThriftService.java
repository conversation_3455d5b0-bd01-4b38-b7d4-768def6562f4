package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.dataset.FragmentRecallRequestDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * 数据集相关对外服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 4/14/25 2:38 PM
 */
@ThriftService
public interface DatasetThriftService {

    /**
     * 召回有权限的分片
     *
     * @param request
     * @return
     */
    @ThriftMethod
    BeeResponseDto checkFragmentPermission(FragmentRecallRequestDto request);
}
