<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.2</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.wmbdaiassistant</groupId>
    <artifactId>client</artifactId>
    <version>${bdaiassistant.client.version}</version>
    <packaging>jar</packaging>
    <name>client</name>

    <properties>
        <bdaiassistant.client>1.0.1</bdaiassistant.client>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan</groupId>
                    <artifactId>mtconfig-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.lion</groupId>
                    <artifactId>lion-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-tcnative-boringssl-static</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.38</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}-SNAPSHOT</bdaiassistant.client.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <package.environment>test</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}-SNAPSHOT</bdaiassistant.client.version>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <package.environment>staging</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}-SNAPSHOT</bdaiassistant.client.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <package.environment>prod</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}</bdaiassistant.client.version>
            </properties>
        </profile>
    </profiles>

</project>
