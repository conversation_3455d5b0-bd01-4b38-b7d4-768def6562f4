package com.sankuai.wmbdaiassistant.common;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * URL工具
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/10 16:16
 */
public class UrlUtil {
    static private final String urlRegex = "(https?|ftp|file|meituanwaimaibee)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";
    static private final String pcUrlRegex = "(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";
    static private final String beeUrlRegex = "meituanwaimaibee://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";
    static private final String beeTtUrlPrefix = "meituanwaimaibee://beewaimai.meituan.com/mrn?mrn_biz=bfe&mrn_entry=tt&mrn_component=ttcreate&new_bundle=1&originURL=";
    static private final String pcTtUrlPrefix = "https://tt.sankuai.com/ticket/custom/create/";

    public static List<String> findAllUrl(String content) {
        return RegexUtil.findAll(urlRegex, content);
    }

    public static List<String> findAllPcUrl(String content) {
        return RegexUtil.findAll(pcUrlRegex, content);
    }

    public static List<String> findAllBeeUrl(String content) {
        return RegexUtil.findAll(beeUrlRegex, content);
    }

    public static boolean isTTUrl(String url, boolean isBeeUrl) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        return isBeeUrl ? url.startsWith(beeTtUrlPrefix) : url.startsWith(pcTtUrlPrefix);
    }

    public static String ttUrlToBeeFormat(String ttUrl) {
        if (StringUtils.isBlank(ttUrl)) {
            return "";
        }
        return String.format("%s%s", beeTtUrlPrefix, encodeUrl(ttUrl, "UTF-8"));
    }

    public static String ttUrlToPcFormat(String ttUrl) {
        if (StringUtils.isBlank(ttUrl)) {
            return "";
        }
        return decodeUrl(ttUrl.substring(beeTtUrlPrefix.length()), "UTF-8");
    }

    public static String encodeUrl(String url, String enc) {
        try {
            return URLEncoder.encode(url, enc);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String decodeUrl(String url, String enc) {
        try {
            return URLDecoder.decode(url, enc);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

}
