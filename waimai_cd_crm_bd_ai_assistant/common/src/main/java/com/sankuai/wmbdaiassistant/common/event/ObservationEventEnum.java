package com.sankuai.wmbdaiassistant.common.event;
/**
 * 业务事件枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/21 10:50
 */
public enum ObservationEventEnum {

    TT_CREATE("tt_create", "TT工单创建事件"),
    TT_MULTI_CREATE("tt_multi_create", "单个TT链接创建多次工单"),
    TT_NO_MATCH_FEEDBACK("tt_no_match_feedback", "TT创建事件没有匹配的feedback"),
    ;

    /**
     * 事件类型
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    ObservationEventEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ObservationEventEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ObservationEventEnum bizEventEnum : values()) {
            if (code.equals(bizEventEnum.getCode())) {
                return bizEventEnum;
            }
        }
        return null;
    }
}
