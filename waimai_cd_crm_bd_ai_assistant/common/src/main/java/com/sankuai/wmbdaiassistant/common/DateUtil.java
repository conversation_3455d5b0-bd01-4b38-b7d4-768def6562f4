package com.sankuai.wmbdaiassistant.common;


import java.text.ParseException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


/**
 * 日期工具
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class DateUtil {
    public static final String DEFAULT_LONG_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 转换成yyyy-MM-dd HH:mm:ss格式
     *
     * @param date Date
     * @return java.lang.String
     */
    public static String longDateFormat(Date date) {
        return dateFormat(date, DEFAULT_LONG_FORMAT);
    }

    /**
     * 将yyyy-MM-dd HH:mm:ss格式的字符串转换为Date对象
     *
     * @param dateStr 日期字符串
     * @return java.util.Date
     */
    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_LONG_FORMAT);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("日期解析失败，dateStr={}", dateStr);
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期格式化
     *
     * @param date Date
     * @return java.lang.String
     */
    public static String dateFormat(Date date, String format) {
        if (date == null) {
            return null;
        }

        return new SimpleDateFormat(format).format(date);
    }

    public static String yesterday() {
        return LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 输入日的开始时间
     *
     * @return
     */
    public static Long dayStartTime(LocalDate day) {
        return LocalDateTime.of(day, LocalTime.MIN).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
    }

    /**
     * 输入日的结束时间
     *
     * @return
     */
    public static Long dayEndTime(LocalDate day) {
        // 这里使用LocalTime.of(23, 59, 59) 而不使用 LocalTime.MAX
        // 因为java中LocalTime.MAX默认精确到纳秒，LocalTime.MAX=23:59:59.999999999
        // 但是mysql中datetime默认只精确到毫秒，当用2022-01-01 23:59:59.999999999作为参数传给mysql时，会隐式转为2022-01-02 00:00:00
        // 这样可能造成查询结果不准确
        return LocalDateTime.of(day, LocalTime.of(23, 59, 59)).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
    }

    /**
     * 获取起始时间范围内的日号
     *
     * @param startLocalDate
     * @param endLocalDate
     * @return
     */
    public static List<LocalDate> getRangeDays(LocalDate startLocalDate, LocalDate endLocalDate) {
        ParamCheckUtil.notNull(startLocalDate, "startLocalDate require not null");
        ParamCheckUtil.notNull(endLocalDate, "endLocalDate require not null");
        ParamCheckUtil.isTrue(!startLocalDate.isAfter(endLocalDate), String.format("开始时间:%s 不能晚于结束时间:%s", startLocalDate, endLocalDate));
        List<LocalDate> result = new ArrayList<>();
        while (!startLocalDate.isAfter(endLocalDate)) {
            result.add(startLocalDate);
            startLocalDate = startLocalDate.plusDays(1);
        }
        return result;
    }

    public static Date max(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.after(date2) ? date1 : date2;
    }

    public static Date fromLocalDateTime(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getLastWeekMonday() {
        LocalDate lastMonday = LocalDate.now().minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
        return Date.from(lastMonday.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date getLastWeekSundayEnd() {
        LocalDate lastSunday = LocalDate.now().minusWeeks(1).with(java.time.DayOfWeek.SUNDAY);
        LocalDateTime lastSundayEnd = lastSunday.atTime(23, 59, 59);
        return Date.from(lastSundayEnd.atZone(ZoneId.systemDefault()).toInstant());
    }
}
