package com.sankuai.wmbdaiassistant.common;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.io.IOException;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.TextNode;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.common.exception.BizException;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * JSON序列化工具
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JsonUtil {

    /**
     * mapper
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    static {
        OBJECT_MAPPER.setSerializationInclusion(Include.NON_NULL);
        OBJECT_MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * 对象序列化成字符串
     *
     * @param o 待序列化目标对象
     * @return java.lang.String
     */
    public static String toJson(Object o) {
        if (o == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new BizException(BizErrorEnum.JSON_FAIL, e);
        }
    }

    /**
     * 对象反序列化
     *
     * @param s      String
     * @param tClass 类型
     * @return T
     */
    public static <T> T fromJson(String s, Class<T> tClass) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(s, tClass);
        } catch (IOException e) {
            throw new BizException(BizErrorEnum.JSON_FAIL, "反序列化对象失败,对象内容:" + s, e);
        }
    }

    /**
     * 对象反序列化
     *
     * @param s             String
     * @param typeReference 类型
     * @return T
     */
    public static <T> T fromJson(String s, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(s, typeReference);
        } catch (IOException e) {
            throw new BizException(BizErrorEnum.JSON_FAIL, "反序列化对象失败,对象内容:" + s, e);
        }
    }

    /**
     * 对象反序列化
     * @param jsonNode
     * @param tClass
     * @return
     * @param <T>
     */
    public static <T> T fromJsonNode(JsonNode jsonNode, Class<T> tClass) {
        if (jsonNode == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.treeToValue(jsonNode, tClass);
        } catch (IOException e) {
            throw new BizException(BizErrorEnum.JSON_FAIL, "反序列化对象失败,对象内容:" + jsonNode, e);
        }
    }

    /**
     * 对象反序列化
     * @param jsonNode
     * @param typeReference
     * @return
     * @param <T>
     */
    public static <T> T fromJsonNode(JsonNode jsonNode, TypeReference<T> typeReference) {
        if (jsonNode == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(jsonNode, typeReference);
        } catch (IllegalArgumentException e) {
            throw new BizException(BizErrorEnum.JSON_FAIL, "反序列化对象失败,对象内容:" + jsonNode, e);
        }
    }

    public static JsonNode fromJson(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(s);
        } catch (IOException e) {
            throw new BizException(BizErrorEnum.JSON_FAIL, "反序列化对象失败,对象内容:" + s, e);
        }
    }

    /**
     * 判断一个文本是否符合 json 格式
     * @param s
     * @return
     */
    public static boolean isJsonText(String s) {
        try {
            JsonNode jsonNode = JsonUtil.fromJson(s);
            if (jsonNode instanceof TextNode) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 爹贵过滤json对象不需要的字段,仅保留指定字段
     * @param obj 待过滤对象
     * @param fieldsToKeep 需要保留的字段
     * @return JsonNode
     */
    public static JsonNode filterJson(JsonNode obj, List<String> fieldsToKeep) {
        Set<String> fieldSet = new HashSet<>(fieldsToKeep);
        return processNode(obj, fieldSet);
    }

    private static JsonNode processNode(JsonNode node, Set<String> fieldsToKeep) {
        if(CollectionUtils.isEmpty(fieldsToKeep)) {
            return node;
        }
        if (node.isObject()) {
            ObjectNode filteredNode = JsonNodeFactory.instance.objectNode();
            Iterator<String> fieldNames = node.fieldNames();

            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                if (fieldsToKeep.contains(fieldName)) {
                    JsonNode processedValue = processNode(node.get(fieldName), fieldsToKeep);
                    filteredNode.set(fieldName, processedValue);
                }
            }
            return filteredNode;

        } else if (node.isArray()) {
            ArrayNode filteredArray = JsonNodeFactory.instance.arrayNode();
            for (JsonNode element : node) {
                filteredArray.add(processNode(element, fieldsToKeep));
            }
            return filteredArray;

        } else {
            return node;
        }
    }
}
