package com.sankuai.wmbdaiassistant.common.exception;

/**
 * 业务异常枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-07 17:32
 */
public enum BizErrorEnum {

    /**
     * 通用的失败结果（目前用于Openapi对外返回结果）
     */
    ERROR(-1, "失败"),

    /**
     * 会话过期，要求前端刷新会话
     */
    SESSION_NEED_REFRESH(1, "会话需要刷新"),

    /**
     * 系统内部错误
     */
    FAIL(100001, "系统内部错误"),

    /**
     * 入参非法
     */
    ILLEGAL_PARAM_ERROR(100002, "入参非法"),

    /**
     * JSON异常
     */
    JSON_FAIL(100003, "JSON异常"),

    /**
     * 文件下载异常
     */
    FILE_DOWNLOAD_FAIL(100004, "文件下载异常"),

    /**
     * 无权限用户
     */
    NO_PERMISSION_USER(100005, "无权限用户"),

    /**
     * 无服务分信息
     */
    NO_SERVICE_SCORE_INDEX(100006, "无服务分信息"),

    /**
     * 问法不存在
     */
    PHRASE_NOT_EXIST(100007, "问法不存在"),

    /**
     * 枚举值不存在
     */
    ENUM_NOT_EXIST(100008, "枚举值不存在"),

    /**
     * LongCat异常
     */
    LONG_CAT_ERROR(100009, "LongCat模型异常"),

    /**
     * chatglm 模型异常
     */
    CHATGLM_ERROR(100010, "chatglm模型异常"),

    /**
     * 查询TT信息异常
     */
    TT_ERROR(100011, "查询TT信息异常"),

    /**
     * S3加密异常
     */
    S3_ENCRYPT_ERROR(10012, "S3加密异常"),

    /**
     * 查询商家信息异常
     */
    POI_QUERY_ERROR(100013, "查询商家信息异常"),

    /**
     * 查询短信链接异常
     */
    SHORT_LINK_QUERY_ERROR(100014, "查询短信链接异常"),

    /**
     * S3 失败
     */
    S3_UPLOAD_FAIL(100015, "s3上传失败"),

    /**
     * 分发平台异常
     */
    DISTRIBUTED_PLATFORM_FAIL(100016, "分发平台异常"),

    /**
     * uac异常
     */
    UAC_FAIL(100016, "uac异常"),

    /**
     * uac异常
     */
    INFRA_FAIL(100016, "infra异常"),

    /**
     * 发送mafka MQ消息异常
     */
    SEND_MQ_ERROR(100017, "发送mafka MQ消息异常"),

    /**
     * 海盗模型执行失败
     */
    PIRATE_FAIL(100018, "海盗模型执行失败"),

    /**
     * 搜索商家信息异常
     */
    POI_SEARCH_ERROR(100019, "搜索商家信息异常"),

    /**
     * 知识域异常
     */
    DOMAIN_ERROR(100020, "知识域异常"),

    /**
     * 问题域识别异常
     */
    DOMAIN_RECOGNIZE_ERROR(100021, "问题域识别异常"),

    /**
     * 意图识别异常
     */
    INTENT_RECOGNIZE_ERROR(100022, "意图识别异常"),

    /**
     * CRM平台异常
     */
    CRM_PLATFORM_ERROR(100023, "CRM平台异常"),

    /**
     * 学城查询WIKI权限异常
     */
    KM_PERMISSION_ERROR(100024, "学城查询WIKI权限异常"),

    /**
     * 算法侧聊天接口异常
     */
    ALGO_CHAT_API_ERROR(100025, "算法侧聊天接口异常"),

    /**
     * FridayV2接口异常
     */
    FRIDAY_V2_API_ERROR(100026,"FridayV2接口异常");

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String desc;

    BizErrorEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static BizErrorEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BizErrorEnum bizErrorEnum : values()) {
            if (code.equals(bizErrorEnum.getCode())) {
                return bizErrorEnum;
            }
        }
        return null;
    }

}
