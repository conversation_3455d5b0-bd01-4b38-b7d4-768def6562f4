package com.sankuai.wmbdaiassistant.common.exception;

import lombok.Getter;

/**
 * 业务异常
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-07 17:31
 */
public class BizException extends RuntimeException {

    @Getter
    private final int code;

    public BizException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BizException() {
        this(BizErrorEnum.FAIL);
    }

    public BizException(String message) {
        this(BizErrorEnum.FAIL, message);
    }

    public BizException(BizErrorEnum errorEnum) {
        this(errorEnum.getCode(), errorEnum.getDesc());
    }

    public BizException(BizErrorEnum errorEnum, String message) {
        this(errorEnum.getCode(), message);
    }

    public BizException(int code, String message, Throwable throwable) {
        super(message, throwable);
        this.code = code;
    }

    public BizException(Throwable throwable) {
        this(BizErrorEnum.FAIL, throwable);
    }

    public BizException(BizErrorEnum bizErrorEnum, Throwable throwable) {
        this(bizErrorEnum.getCode(), bizErrorEnum.getDesc(), throwable);
    }

    public BizException(String message, Throwable throwable) {
        this(BizErrorEnum.FAIL.getCode(), message, throwable);
    }

    public BizException(BizErrorEnum bizErrorEnum, String message, Throwable throwable) {
        this(bizErrorEnum.getCode(), message, throwable);
    }


}
