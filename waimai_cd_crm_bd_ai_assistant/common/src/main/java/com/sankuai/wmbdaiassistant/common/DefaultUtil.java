package com.sankuai.wmbdaiassistant.common;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 默认工具类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-07 11:16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DefaultUtil {

    /**
     * 如果集合为null，返回不可编辑集合；如果集合不为null，返回原始值
     *
     * @param list 集合
     * @return java.util.List<T>
     */
    public static <T> List<T> defaultList(List<T> list) {
        if (list == null) {
            return Collections.emptyList();
        }
        return list;
    }


    /**
     * 如果集合为null，返回不可编辑集合；如果集合不为null，返回原始值
     *
     * @param set 集合
     * @return java.util.List<T>
     */
    public static <T> Set<T> defaultSet(Set<T> set) {
        if (set == null) {
            return Collections.emptySet();
        }
        return set;
    }

    /**
     * 默认布尔值
     *
     * @param b Boolean
     * @return java.lang.Boolean
     */
    public static Boolean defaultBoolean(Boolean b) {
        if (b == null) {
            return Boolean.FALSE;
        }
        return b;
    }

    /**
     * 转为默认非null字符串
     * 
     * @param str 字符串
     * @return
     */
    public static String defaultString(Object str) {
        if (str == null) {
            return "";
        }
        return str.toString();
    }

    /**
     * 默认map
     *
     * @param map Map<K, V>
     * @return java.util.Map<K, V>
     */
    public static <K, V> Map<K, V> defaultMap(Map<K, V> map) {
        if (map == null) {
            return Collections.emptyMap();
        }
        return map;
    }


    /**
     * 如果t不为空，返回t；如果t为空，返回第一个不为空的值
     *
     * @param t  原值
     * @param t1 默认值数组
     * @return T
     */
    public static <T> T defaultValue(T t, T... t1) {
        if (t != null) {
            return t;
        }
        for (T t2 : t1) {
            if (t2 != null) {
                return t2;
            }
        }
        return null;
    }

    /**
     * 如果t不为空，返回t；如果t为空，返回第一个不为空的值
     *
     * @param t  原值
     * @param t1 默认值数组
     * @return T
     */
    public static String defaultValue(String t, String... t1) {
        if (t != null) {
            return t;
        }
        for (String t2 : t1) {
            if (t2 != null) {
                return t2;
            }
        }
        return null;
    }
}
