package com.sankuai.wmbdaiassistant.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 执行器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-15 16:04
 */
@Slf4j
public class ExecutorUtil {

    /**
     * 如果t不为空，执行consumer
     *
     * @param t        T
     * @param consumer Consumer
     */
    public static <T> void executeIfNotNull(T t, Consumer<T> consumer) {
        if (t != null) {
            consumer.accept(t);
        }
    }

    /**
     * 如果为true，执行consumer
     *
     * @param isOk     判断条件
     * @param runnable Consumer
     */
    public static <T> void executeIfTrue(Boolean isOk, Runnable runnable) {
        if (DefaultUtil.defaultBoolean(isOk)) {
            runnable.run();
        }
    }

    /**
     * 如果t不为空，执行consumer
     *
     * @param t        T
     * @param consumer Consumer
     */
    public static void executeIfNotBlank(String t, Consumer<String> consumer) {
        if (StringUtils.isNotBlank(t)) {
            consumer.accept(t);
        }
    }

    /**
     * 如果集合不为空，执行consumer
     *
     * @param collection Collection
     * @param consumer   Consumer
     */
    public static <T> void executeIfNotEmpty(List<T> collection, Consumer<List<T>> consumer) {
        if (CollectionUtils.isNotEmpty(collection)) {
            consumer.accept(collection);
        }
    }

    /**
     * 如果集合不为空，执行consumer
     *
     * @param collection Collection
     * @param consumer   Consumer
     */
    public static <T> void executeEachIfNotEmpty(Collection<T> collection, Consumer<T> consumer) {
        if (CollectionUtils.isNotEmpty(collection)) {
            collection.forEach(consumer);
        }
    }

    /**
     * 异步批量执行
     *
     * @param threadPool 线程池
     * @param values     待处理的数据
     * @param function   处理逻辑
     * @param <T>        待处理数据的类型
     * @param <R>        逻辑逻辑返回的数据类型
     * @return 批量处理后的数据
     */
    public static <T, R> List<R> asynchronousBatchExecute(ExecutorService threadPool, List<T> values, Function<T, R> function) {
        return DefaultUtil.defaultList(values).stream().map(value -> threadPool.submit(() -> {
            try {
                return function.apply(value);
            } catch (Exception e) {
                log.error("ExecutorUtil asynchronousBatchExecute fail", e);
                return null;
            }
        })).map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("ExecutorUtil Interrupt", e);
            }
            return null;
        }).collect(Collectors.toList());
    }

    /**
     * 异步批量执行
     *
     * @param threadPool 线程池
     * @param values     待处理的数据
     * @param function   处理逻辑
     * @param <T>        待处理数据的类型
     * @return 批量处理后的数据
     */
    public static <T> void asynchronousBatchExecute(ExecutorService threadPool, List<T> values, Consumer<T> function) {
        ParamCheckUtil.notEmpty(values);

        CountDownLatch countDownLatch = new CountDownLatch(values.size());
        values.forEach(value -> threadPool.submit(() -> {
            try {
                function.accept(value);
            } catch (Exception e) {
                log.error("ExecutorUtil asynchronousBatchExecute without result fail", e);
            } finally {
                countDownLatch.countDown();
            }
        }));

        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("ExecutorUtil asynchronousBatchExecute countDownLatch.await fail", e);
        }
    }

    /**
     * 安全执行
     */
    public static <T> T safeExecute(Callable<T> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            log.error("ExecutorUtil safeExecutor fail, msg = {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 安全执行
     */
    public static void safeExecute(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("ExecutorUtil safeExecutor fail, msg = {}", e.getMessage(), e);
        }
    }

    public static <T> T waitFutureFinish(Future<T> future) {
        try {
            return future.get();
        } catch (Exception e) {
            log.error("ExecutorUtil waitFutureFinish fail, msg = {}", e.getMessage(), e);
            return null;
        }
    }
}
