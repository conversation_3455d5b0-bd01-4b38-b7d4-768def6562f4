package com.sankuai.wmbdaiassistant.common;

import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang3.StringUtils;

/**
 * 拼音工具类
 */
public class PinyinUtil {
    /**
     * 获取汉字首字母（优化版）
     * @param str 需要转换的汉字字符串
     * @return 返回汉字首字母
     */
    public static String getFirstLetterOptimized(String str) {
        if(StringUtils.isBlank(str)){
            return "";
        }
        StringBuilder firstLetter = new StringBuilder();
        int length = str.length();
        for (int i = 0; i < length; i++) {
            char ch = str.charAt(i);
            if (ch >= 0x4e00 && ch <= 0x9fa5) { // 判断是否为汉字
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    String pinyin = pinyinArray[0]; // 获取汉字拼音的第一个读音
                    firstLetter.append(StringUtils.isNotBlank(pinyin) ? pinyin.charAt(0) : '#');
                } else {
                    firstLetter.append('#'); // 如果没有拼音，则添加'#'
                }
            } else {
                firstLetter.append(ch);
            }
        }
        if (StringUtils.isBlank(firstLetter.toString())) {
            return "";
        }
        return firstLetter.toString().toUpperCase();
    }
}
