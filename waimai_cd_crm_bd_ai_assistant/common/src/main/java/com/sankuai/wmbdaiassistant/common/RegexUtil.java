package com.sankuai.wmbdaiassistant.common;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexUtil {

    /**
     * 检查文本是否完全匹配正则表达式。
     *
     * @param regex 正则表达式
     * @param text 要匹配的文本
     * @return 如果文本完全匹配正则表达式，返回true；否则返回false。
     */
    public static boolean matches(String regex, String text) {
        return Pattern.matches(regex, text);
    }

    /**
     * 查找文本中所有匹配正则表达式的子串。
     *
     * @param regex 正则表达式
     * @param text 要查找的文本
     * @return 匹配的子串列表
     */
    public static List<String> findAll(String regex, String text) {
        if (Objects.equals(text, "")) {
            return new ArrayList<>();
        }
        if (Objects.equals(regex, "")) {
            return Collections.singletonList(text);
        }
        List<String> matches = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            matches.add(matcher.group());
        }
        return matches;
    }

    /**
     * 使用正则表达式替换文本中的内容。
     *
     * @param regex 正则表达式
     * @param replacement 替换内容
     * @param text 原始文本
     * @return 替换后的文本
     */
    public static String replace(String regex, String replacement, String text) {
        return text.replaceAll(regex, replacement);
    }

    /**
     * 提取文本中第一次匹配正则表达式的组。
     *
     * @param regex 正则表达式
     * @param text 要提取的文本
     * @return 匹配的组列表，如果没有匹配返回空列表
     */
    public static List<String> extractGroups(String regex, String text) {
        List<String> groups = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                groups.add(matcher.group(i));
            }
        }
        return groups;
    }

    /**
     * 提取第一次匹配到的索引。
     *
     * @param regex 正则表达式
     * @param text 要搜索的文本
     * @return 包含URL索引的List，每个元素是一个int数组，
     *         数组的第一个元素是起始索引，第二个元素是结束索引
     */
    public static int[] findFirstMatchIndices(String regex, String text) {
        List<int[]> indices = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return new int[] {matcher.start(), matcher.end()};
        }

        return null;
    }

    /**
     * 判断文本是否包含匹配正则表达式的内容。
     *
     * @param regex 正则表达式
     * @param text 要匹配的文本
     * @return 如果文本包含匹配正则表达式的内容，返回true；否则返回false。
     */
    public static boolean containsMatch(String regex, String text) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }
}
