package com.sankuai.wmbdaiassistant.common.enums;

import org.apache.commons.lang3.ArrayUtils;

/**
 * 带 code 的枚举类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-21 13:33
 */
public interface CodeBaseEnum {

    /**
     * 获取code
     *
     * @return java.lang.String
     */
    String getCode();

    /**
     * 根据code获取对应的枚举
     *
     * @param enumClass 枚举类
     * @param code      枚举类code
     * @return T
     */
    static <T extends CodeBaseEnum> T getEnumIgnoreCase(final Class<T> enumClass, final String code) {
        if (enumClass == null || code == null || !enumClass.isEnum()
                || ArrayUtils.getLength(enumClass.getEnumConstants()) == 0) {
            return null;
        }
        for (final T each : enumClass.getEnumConstants()) {
            if (each.getCode().equalsIgnoreCase(code)) {
                return each;
            }
        }
        return null;
    }
}
