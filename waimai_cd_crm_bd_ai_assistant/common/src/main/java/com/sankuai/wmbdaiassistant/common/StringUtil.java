package com.sankuai.wmbdaiassistant.common;

import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

/**
 * 字符串工具类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-11 15:17
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StringUtil {

    public static String trim(String data) {
        return StringUtils.stripToEmpty(data);
    }

    public static String unescape(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        return StringEscapeUtils.unescapeJava(data);
    }
    
    public static String escape(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        return StringEscapeUtils.escapeJava(data);
    }

    public static String onlyEscapeLineSeparator(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        return data.replaceAll("\n", "\\\\n");
    }

    public static String trimQuotation(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        if (data.charAt(0) == '"') {
            data = data.substring(1);
        }
        if (data.charAt(data.length() - 1) == '"') {
            data = data.substring(0, data.length() - 1);
        }
        return data;
    }

    public static String replaceByMap(String data, Map<String, String> replaceMap) {
        if (StringUtils.isBlank(data) || MapUtils.isEmpty(replaceMap)) {
            return data;
        }
        for (Map.Entry<String, String> entry : replaceMap.entrySet()) {
            data = data.replace(entry.getKey(), entry.getValue());
        }
        return data;
    }
}
