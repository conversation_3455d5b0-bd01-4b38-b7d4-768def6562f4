package com.sankuai.wmbdaiassistant.common;

import java.util.HashMap;
import java.util.Map;

/**
 * 合并工具
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/7 11:24
 */
public class MergeUtil {

    /**
     *合并两个Map
     *
     * @param map1 Map 1
     * @param map2 Map 2
     * @return
     * @param <K> Key 类型
     * @param <V> Value 类型
     */
    public static <K, V> Map<K, V> mergeMaps(Map<K, V> map1, Map<K, V> map2) {
        Map<K, V> result = new HashMap<>();
        if (map1 != null) {
            result.putAll(map1);
        }
        if (map2 != null) {
            result.putAll(map2);
        }
        return result;
    }

}
