package com.sankuai.wmbdaiassistant.common;

import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.common.exception.BizException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * 参数校验工具类
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ParamCheckUtil {

    /**
     * 不能为null
     */
    public static void notNull(Object target) {
        notNull(target, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 不能为null
     */
    public static void notNull(Object target, String msg) {
        notNull(target, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 不能为null
     */
    public static void notNull(Object target, BizErrorEnum bizErrorEnum) {
        notNull(target, bizErrorEnum.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 不能为null
     */
    public static void notNull(Object target, BizErrorEnum bizErrorEnum, String msg) {
        notNull(target, bizErrorEnum.getCode(), msg);
    }

    /**
     * 不能为null
     */
    public static void notNull(Object target, int code, String msg) {
        if (target == null) {
            throw new BizException(code, msg);
        }
    }

    /**
     * 不能为空字符串
     */
    public static void notBlank(String target) {
        notBlank(target, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 不能为空字符串
     */
    public static void notBlank(String target, String msg) {
        notBlank(target, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 不能为空字符串
     */
    public static void notBlank(String target, BizErrorEnum bizErrorEnum) {
        notBlank(target, bizErrorEnum.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 不能为空字符串
     */
    public static void notBlank(String target, BizErrorEnum bizErrorEnum, String msg) {
        notBlank(target, bizErrorEnum.getCode(), msg);
    }

    /**
     * 不能为空字符串
     */
    public static void notBlank(String target, int code, String msg) {
        if (StringUtils.isBlank(target)) {
            throw new BizException(code, msg);
        }
    }

    /**
     * 必须为空字符串
     */
    public static boolean isBlank(String target) {
        return isBlank(target, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 必须为空字符串
     */
    public static boolean isBlank(String target, String msg) {
        return isBlank(target, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 必须为空字符串
     */
    public static boolean isBlank(String target, BizErrorEnum bizErrorEnum) {
        return isBlank(target, bizErrorEnum.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 必须为空字符串
     */
    public static boolean isBlank(String target, BizErrorEnum bizErrorEnum, String msg) {
        return isBlank(target, bizErrorEnum.getCode(), msg);
    }

    /**
     * 必须为空字符串
     */
    public static boolean isBlank(String target, int code, String msg) {
        if (StringUtils.isNotBlank(target)) {
            throw new BizException(code, msg);
        }
        return true;
    }

    /**
     * 不能为空集合
     */
    public static void notEmpty(Collection<?> collection) {
        notEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 不能为空集合
     */
    public static void notEmpty(Collection<?> collection, String msg) {
        notEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 不能为空集合
     */
    public static void notEmpty(Collection<?> collection, BizErrorEnum bizErrorEnum) {
        notEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 不能为空集合
     */
    public static void notEmpty(Collection<?> collection, BizErrorEnum bizErrorEnum, String msg) {
        notEmpty(collection, bizErrorEnum.getCode(), msg);
    }

    /**
     * 不能为空集合
     */
    public static void notEmpty(Collection<?> collection, int code, String msg) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BizException(code, msg);
        }
    }

    /**
     * 不能为空集合
     */
    public static <K, V> void notEmpty(Map<K, V> collection) {
        notEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 不能为空集合
     */
    public static <K, V> void notEmpty(Map<K, V> collection, String msg) {
        notEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 不能为空集合
     */
    public static <K, V> void notEmpty(Map<K, V> collection, BizErrorEnum bizErrorEnum) {
        notEmpty(collection, bizErrorEnum.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 不能为空集合
     */
    public static <K, V> void notEmpty(Map<K, V> collection, BizErrorEnum bizErrorEnum, String msg) {
        notEmpty(collection, bizErrorEnum.getCode(), msg);
    }

    /**
     * 不能为空集合
     */
    public static <K, V> void notEmpty(Map<K,V> collection, int code, String msg) {
        if (MapUtils.isEmpty(collection)) {
            throw new BizException(code, msg);
        }
    }
    
    /**
     * 必须为空集合
     */
    public static void isEmpty(Collection<?> collection) {
        isEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 必须为空集合
     */
    public static void isEmpty(Collection<?> collection, String msg) {
        isEmpty(collection, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 必须为空集合
     */
    public static void isEmpty(Collection<?> collection, BizErrorEnum bizErrorEnum) {
        isEmpty(collection, bizErrorEnum.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 必须为空集合
     */
    public static void isEmpty(Collection<?> collection, BizErrorEnum bizErrorEnum, String msg) {
        isEmpty(collection, bizErrorEnum.getCode(), msg);
    }

    /**
     * 必须为空集合
     */
    public static void isEmpty(Collection<?> collection, int code, String msg) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw new BizException(code, msg);
        }
    }

    /**
     * 是否为true结果校验
     */
    public static void isTrue(Boolean b) {
        isTrue(b, BizErrorEnum.ILLEGAL_PARAM_ERROR);
    }

    /**
     * 是否为true结果校验
     */
    public static void isTrue(Boolean b, String msg) {
        isTrue(b, BizErrorEnum.ILLEGAL_PARAM_ERROR.getCode(), msg);
    }

    /**
     * 是否为true结果校验
     */
    public static void isTrue(Boolean b, BizErrorEnum bizErrorEnum) {
        isTrue(b, bizErrorEnum.getCode(), bizErrorEnum.getDesc());
    }

    /**
     * 是否为true结果校验
     */
    public static void isTrue(Boolean b, BizErrorEnum bizErrorEnum, String msg) {
        isTrue(b, bizErrorEnum.getCode(), msg);
    }

    /**
     * 是否为true结果校验
     */
    public static void isTrue(Boolean b, int code, String msg) {
        if (b != null && b) {
            return;
        }
        throw new BizException(code, msg);
    }

}
