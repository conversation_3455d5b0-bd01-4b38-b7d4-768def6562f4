package com.sankuai.wmbdaiassistant.common;

import java.util.List;

/**
 * 向量相似度计算工具类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-22 19:24
 */
public class VectorSimilarityUtil {

    /**
     * 计算两个数组的相似度（余弦相似）
     *
     * @param vectorA 向量数组A
     * @param vectorB 向量数组B
     * @return
     */
    public static double cosineSimilarity(List<Double> vectorA, List<Double> vectorB) {
        ParamCheckUtil.isTrue(vectorA.size() == vectorB.size(), "向量的长度必须相同");

        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;

        for (int i = 0; i < vectorA.size(); i++) {
            dotProduct += vectorA.get(i) * vectorB.get(i);
            normA += Math.pow(vectorA.get(i), 2);
            normB += Math.pow(vectorB.get(i), 2);
        }

        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

}
