package com.sankuai.wmbdaiassistant.domain.service.chat.content;

import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 回复内容的构建器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-10 19:14
 */
public class ChatContentBuilder {

    private List<Object> componentList = new ArrayList<>();

    public ChatContentBuilder add(Object component) {
        ExecutorUtil.executeIfNotNull(component, componentList::add);
        return this;
    }

    public ChatContentBuilder addAll(List<?> list) {
        ExecutorUtil.executeIfNotEmpty(list, componentList::addAll);
        return this;
    }

    public String build() {
        return JsonUtil.toJson(componentList);
    }

}
