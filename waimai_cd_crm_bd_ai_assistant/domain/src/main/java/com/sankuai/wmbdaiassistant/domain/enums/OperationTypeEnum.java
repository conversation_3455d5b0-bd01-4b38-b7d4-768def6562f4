package com.sankuai.wmbdaiassistant.domain.enums;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/11
 **/
public enum OperationTypeEnum {
    JUMP(1, "url跳转"),
    CONTINUE_QUERY(2, "继续提问");
    private int code;
    private String desc;

    OperationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OperationTypeEnum findByCode(int code) {
        for (OperationTypeEnum type : OperationTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
