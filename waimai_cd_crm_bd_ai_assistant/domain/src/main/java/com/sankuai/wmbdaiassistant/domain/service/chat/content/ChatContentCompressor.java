package com.sankuai.wmbdaiassistant.domain.service.chat.content;

import com.sankuai.wmbdaiassistant.common.UrlUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 回复内容压缩器
 * 将非逻辑语义内容压缩，保存到Map中，返回前端时恢复
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/11 13:37
 */
@Data
public class ChatContentCompressor {
    private Map<String, String> decompressionMap;

    private int index = 0;

    private String URL_FORMAT = "https://sub/%d";

    public ChatContentCompressor() {
        index = 0;
        decompressionMap = new HashMap<>();
    }
    public String compress(String content) {
        content = compressUrl(content);

        return content;
    }

     public String decompress(String content) {
        if(StringUtils.isBlank(content)) {
            return content;
        }
//         执行解压缩
        for (Map.Entry<String, String> entry : decompressionMap.entrySet()) {
            content = content.replace(entry.getKey(), entry.getValue());
        }

        return content;
    }

    public void putAll(Map<String, String> map) {
        if (MapUtils.isNotEmpty(map)) {
            decompressionMap.putAll(map);
        }
    }

    private String compressUrl(String content) {
        List<String> urlList = UrlUtil.findAllUrl(content).stream().distinct().collect(Collectors.toList());
        Map<String, String> urlMap = new HashMap<>();
        for (int i = 0; i < urlList.size(); i++) {
            urlMap.put(urlList.get(i), String.format(URL_FORMAT, index++));
        }

        for (Map.Entry<String, String> entry : urlMap.entrySet()) {
            content = content.replace(entry.getKey(), entry.getValue());
        }

        decompressionMap.putAll(MapUtils.invertMap(urlMap));
        return content;
    }
}
