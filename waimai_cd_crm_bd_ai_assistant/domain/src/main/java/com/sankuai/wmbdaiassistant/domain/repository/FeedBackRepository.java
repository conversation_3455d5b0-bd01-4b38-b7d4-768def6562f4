package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.FeedBackModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface FeedBackRepository {

    /**
     * 新增反馈
     *
     * @param feedBack 反馈
     * @return 新增是否成功
     */
    boolean insert(FeedBackModel feedBack);

    /**
     * 修改
     *
     * @param feedBack 反馈
     * @return 是否修改成功
     */
    boolean update(FeedBackModel feedBack);

    /**
     * 根据会话消息ID列表查询反馈
     *
     * @param chatMsgIdList 会话消息ID列表
     * @return 反馈列表
     */
    List<FeedBackModel> findByMsgIdList(List<Long> chatMsgIdList);

    /**
     * 根据TT ID 查询反馈
     *
     * @param ttId TT ID
     * @return 反馈
     */
    FeedBackModel findByTtId(Long ttId);
}
