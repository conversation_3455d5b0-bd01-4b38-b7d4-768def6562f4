package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 分片状态枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 10:10
 */
@Getter
public enum FragmentStateEnum {
    INIT("init", "初始化"),
    ENABLE("enable", "已生效"),
    DELETED("deleted", "已删除"),
    ;

    private String code;
    private String remark;

    FragmentStateEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public static FragmentStateEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (FragmentStateEnum fragmentStateEnum : FragmentStateEnum.values()) {
            if (fragmentStateEnum.getCode().equals(code)) {
                return fragmentStateEnum;
            }
        }
        return null;
    }
}
