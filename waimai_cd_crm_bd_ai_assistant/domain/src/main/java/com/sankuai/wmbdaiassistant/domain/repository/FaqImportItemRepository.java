package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.FaqImportItemModel;

import java.util.List;

/**
 * Faq批量插入条目
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
public interface FaqImportItemRepository {
	/**
	 * 插入Faq批量导入条目
	 *
	 * @param itemModel Faq批量导入条目
	 * @return 主键ID
	 */
	Long insert(FaqImportItemModel itemModel);

	/**
	 * 批量插入Faq批量导入条目
	 *
	 * @param modelList Faq批量导入条目list
	 * @return 主键ID
	 */
    List<Long> batchInsert(List<FaqImportItemModel> modelList);

	/**
	 * 修改
	 *
	 * @param faqImportItemModel 导入项
	 * @return 是否成功
	 */
	boolean update(FaqImportItemModel faqImportItemModel);

    /**
     * 批量修改
     * 
     * @param faqImportItemModelList 导入项 List
     * @return 修改数量
     */
    int batchUpdateByIdSelective(List<FaqImportItemModel> faqImportItemModelList);

	/**
	 * 根据主键查询
	 *
	 * @param id 主键
	 * @return 导入item
	 */
	FaqImportItemModel findById(Long id);

    /**
     * 根据过程ID查询
     * 
     * @param processId 批量过程主键
     * @return 相关itemList
     */
    List<FaqImportItemModel> findByProcessId(Long processId);

	/**
	 * 根据过程ID分页查询
	 *
	 * @param processId 批量过程主键
	 * @param pageNum   分页参数，页号
	 * @param pageSize  分页参数，每页个数
	 * @return 相关itemList
	 */
	List<FaqImportItemModel> findByProcessIdPage(Long processId, Integer pageNum, Integer pageSize);

	/**
	 * 根据进程ID统计总数
	 *
	 * @param processId 批量过程主键
	 * @return item 个数
	 */
	Long countByProcessId(Long processId);

	/**
	 * 查找一次导入中的最后一次需要替换的，并且相似问为指定 similarPhraseId 的 item
	 *
	 * @param processId
	 * @param similarPhraseId
	 * @return
	 */
	FaqImportItemModel findLatestReplaceItemBySimilarPhraseId(Long processId, Long similarPhraseId);
}
