package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Video implements ContentElement {
    private final String type = ContentElementTypeEnum.VIDEO.getCode();

    @JsonProperty("insert")
    private VideoInfo info;

    public static Video build(String url) {
        return Video.builder().info(VideoInfo.builder().url(url).build()).build();
    }

    @Override
    public String toString() {
        return info == null ? "" : info.getUrl();
    }

    @Override
    public String toMarkdownText() {
        return String.format("![视频](%s)", info == null ? "" : DefaultUtil.defaultValue(info.getUrl(), ""));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoInfo {
        @JsonProperty("video")
        private String url;
    }

    @Override
    public Map<String, String> toTtTransferContent() {
        Map<String, String> map = new HashMap<>();
        map.put("type", "video");
        map.put("url", info.getUrl());
        return map;
    }

}
