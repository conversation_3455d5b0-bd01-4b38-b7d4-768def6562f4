package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

import lombok.Getter;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
@Getter
public enum ChatContentTypeEnum {
    QUESTION(1, "question", "用户问题"),
    ANSWER(2, "answer", "回答");

    private int code;
    private String value;
    private String desc;


    ChatContentTypeEnum(int code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public static boolean isQuestion(int code) {
        return Objects.equals(QUESTION.getCode(), code);
    }

    public static boolean isAnswer(int code) {
        return Objects.equals(ANSWER.getCode(), code);
    }

    public static ChatContentTypeEnum of(int code) {
        for (ChatContentTypeEnum chatContentTypeEnum : values()) {
            if (chatContentTypeEnum.code == code) {
                return chatContentTypeEnum;
            }
        }
        return null;
    }
}
