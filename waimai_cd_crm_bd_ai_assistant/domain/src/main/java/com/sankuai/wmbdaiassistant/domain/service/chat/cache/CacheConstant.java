package com.sankuai.wmbdaiassistant.domain.service.chat.cache;

/**
 * 缓存常量
 *
 * @author: hexiang03
 * @create: 2023/12/27
 **/
public class CacheConstant {

    /**
     * session redis key 模板，变量是 uid 和 session 的 source
     */
    public static final String BD_AI_ASSISTANT_SESSION_SOURCE_KEY_TEMPLATE = "bd_ai_assistant_%d_%s_session";

    /**
     * 问题回答状态redis key 模板，变量是questionId
     */
    public final static String CHAT_MSG_ANSWER_STATUS_KEY_FORMAT = "chat_msg_%d_status";

    /**
     * 问题回答队列redis key 模板，变量是questionId
     */
    public final static String CHAT_MSG_ANSWER_LIST_KEY_FORMAT = "msg_answer_%d_list";

    /**
     * 问题回答前序回答内容 redis key 模板，变量是questionId
     */
    public final static String CHAT_ANSWER_MSG_PREVIOUS_CONTENT_KEY_FORMAT = "chat_answer_msg_previous_content_%d";

    /**
     * 会话上下文的redis key 模板，变量是sessionId
     */
    public static final String SESSION_CONTEXT_FORMAT = "session_context_%d";

    /**
     * 会话锁（只允许同时有一个提问）
     */
    public static final String SESSION_LOCK = "session_lock_%d";
}
