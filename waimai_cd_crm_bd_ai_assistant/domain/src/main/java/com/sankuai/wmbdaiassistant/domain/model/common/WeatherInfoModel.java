package com.sankuai.wmbdaiassistant.domain.model.common;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 天气数据领域模型
 *
 * <AUTHOR>
 * @date 2024/09/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeatherInfoModel {

    public static final String TYPE_RAIN = "rain";
    public static final String TYPE_HOT = "hot";
    public static final String TYPE_COLD = "cold";
    public static final String TYPE_DEFAULT = "default";

    public static final String KEYWORD_RAIN = "雨";
    public static final String KEYWORD_HOT = "晴";
    public static final String KEYWORD_COLD = "雪";
    /**
     * 天气类型
     */
    String weatherType;

    /**
     * 天气数据
     */
    Map<String, String> data;
}