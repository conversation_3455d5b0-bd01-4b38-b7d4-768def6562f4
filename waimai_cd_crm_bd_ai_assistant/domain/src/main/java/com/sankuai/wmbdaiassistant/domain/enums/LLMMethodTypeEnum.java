package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 大模型接口
 *
 * <AUTHOR>
 * @date 2025-01-02 13:35
 */
@Getter
public enum LLMMethodTypeEnum {

    CUSTOM_MODEL_CLIENT("customModelClient", "customModelClient接口"),
    FRIDAY_CLIENT("fridayClient", "fridayClient接口"),
    LONG_CAT_CLIENT("longCatClient", "longCatClient接口"),
    ;

    private String code;
    private String desc;

    LLMMethodTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LLMMethodTypeEnum getByCode(String code) {
        for (LLMMethodTypeEnum llmMethodTypeEnum : values()) {
            if (llmMethodTypeEnum.getCode().equals(code)) {
                return llmMethodTypeEnum;
            }
        }
        return null;
    }

}
