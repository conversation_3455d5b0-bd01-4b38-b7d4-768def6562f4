package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy;

import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;

/**
 * 文本块模式替换策略接口
 * 定义了文本替换的基本行为
 */
public interface ChunkPatternReplaceStrategy {
    /**
     * 判断是否匹配该策略
     * 
     * @param mode 模式
     * @return 是否匹配
     */
    boolean match(String mode);

    /**
     * 替换文本
     * 
     * @param mode       模式
     * @param content    待处理的文本
     * @param sessionSourceEntity 来源配置
     * @return 替换后的文本
     */
    String replace(String mode, String content, SessionSourceEntity sessionSourceEntity);
}