package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.OperationTypeEnum;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import java.util.Collections;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 选项列表
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptionList implements ContentElement {
    private final String type = ContentElementTypeEnum.OPTIONS.getCode();

    @JsonProperty("insert")
    private OptionInfo info;

    public static OptionList build(AbilityTypeEnum abilityType, OperationTypeEnum operationType
            , List<String> questionList) {
        ParamCheckUtil.notNull(abilityType, "OptionList build, abilityType is null");
        ParamCheckUtil.notNull(operationType, "OptionList build, operationType is null");
        ParamCheckUtil.notEmpty(questionList, "OptionList build, questionList empty");

        return build(questionList.stream()
                .map(question -> OptionItem.builder()
                        .content(question)
                        .abilityType(abilityType.getCode())
                        .operationType(operationType.getCode()).build()
                ).collect(Collectors.toList()));
    }

    public static OptionList build(List<OptionItem> itemList) {
        ParamCheckUtil.notEmpty(itemList, "OptionList build, itemList empty");
        return OptionList.builder()
                .info(OptionInfo.builder().tabOptionInfo(TabOptionInfo.builder().optionItems(itemList).build()).build())
                .build();
    }

    public static OptionList build(List<OptionItem> itemList, List<TabItem> tabs) {
        ParamCheckUtil.notEmpty(itemList, "OptionList build, itemList empty");
        OptionList optionList = OptionList.build(itemList);
        optionList.getInfo().getTabOptionInfo().setTabs(tabs);
        return optionList;
    }

    public static List<OptionItem> getOptionItems(OptionList optionList) {
        if (optionList == null || optionList.getInfo() == null || optionList.getInfo().getTabOptionInfo() == null) {
            return Collections.emptyList();
        }
        return DefaultUtil.defaultList(optionList.getInfo().getTabOptionInfo().getOptionItems());
    }

    public static List<TabItem> getTabs(OptionList optionList) {
        if (optionList == null || optionList.getInfo() == null || optionList.getInfo().getTabOptionInfo() == null) {
            return Collections.emptyList();
        }
        return DefaultUtil.defaultList(optionList.getInfo().getTabOptionInfo().getTabs());
    }

    @Override
    public String toMarkdownText() {
        if (info == null || info.getTabOptionInfo() == null
                || CollectionUtils.isEmpty(info.getTabOptionInfo().getOptionItems())) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder("选项如下:");
        info.getTabOptionInfo().getOptionItems().forEach(stringBuilder::append);
        return stringBuilder.toString();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize(using = OptionInfoSerializer.class)
    @JsonDeserialize(using = OptionInfoDeserializer.class)
    public static class OptionInfo {
        @JsonProperty("options")
        private TabOptionInfo tabOptionInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TabOptionInfo {
        @JsonProperty("options")
        private List<OptionItem> optionItems;

        @JsonProperty("tabs")
        private List<TabItem> tabs;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TabItem {
        private String label;
        private String value;
        private Boolean isNew;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionItem {
        private Integer abilityType;
        private Integer subAbilityType;
        private Integer operationType;
        private String content;
        private String url;
        private Boolean isNew;

        @Override
        public String toString() {
            return String.format("%s;", content);
        }
    }

    /**
     * 自定义Json序列化
     */
    public static class OptionInfoSerializer extends JsonSerializer<OptionInfo> {
        @Override
        public void serialize(OptionInfo value, JsonGenerator gen, SerializerProvider serializers)
                throws IOException {
            gen.writeStartObject();
            if (value.getTabOptionInfo() != null) {
                TabOptionInfo tabOptionInfo = value.getTabOptionInfo();
                if (CollectionUtils.isEmpty(tabOptionInfo.getTabs())) {
                    tabOptionInfo.setTabs(Collections.emptyList());
                }
                gen.writeObjectField("options", tabOptionInfo);
            }
            gen.writeEndObject();
        }
    }

    public static class OptionInfoDeserializer extends JsonDeserializer<OptionInfo> {
        @Override
        public OptionInfo deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            OptionInfo.OptionInfoBuilder builder = OptionInfo.builder();

            if (node.has("options")) {
                JsonNode optionsNode = node.get("options");
                if (!optionsNode.isArray()) {
                    TabOptionInfo tabOptionInfo = JsonUtil.fromJsonNode(optionsNode, TabOptionInfo.class);
                    builder.tabOptionInfo(tabOptionInfo);
                } else {
                    List<OptionItem> optionItems = JsonUtil.fromJsonNode(optionsNode,
                            new TypeReference<List<OptionItem>>() {});
                    TabOptionInfo tabOptionInfo = TabOptionInfo.builder().optionItems(optionItems).build();
                    builder.tabOptionInfo(tabOptionInfo);
                }
            }
            return builder.build();
        }
    }
}
