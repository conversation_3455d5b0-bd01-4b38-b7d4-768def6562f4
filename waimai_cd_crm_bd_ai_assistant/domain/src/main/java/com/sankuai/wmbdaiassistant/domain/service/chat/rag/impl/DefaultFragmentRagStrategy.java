package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.LlmRag;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;

/**
 * 默认的基于片段的RAG策略（PC和APP端）
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-28 10:46
 */
@Slf4j
@Component("defaultFragmentRagStrategy")
public class DefaultFragmentRagStrategy extends AbsRagStrategy {

    @Lazy
    @Resource
    private ChatService chatService;

    @Resource
    private Workflow defaultWorkflow;

    @Resource
    private LlmRag defaultFragmentLlmRag;

    private ExecutorService executorService = new ThreadPoolExecutor(20, 50, 5
            , TimeUnit.MINUTES, new ArrayBlockingQueue<>(300), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void triggerQuery(SessionBo sessionBo, long id, String input, String entryPoint, GeneralCallback callback) {
        Future<List<String>> recommendedQuestions = executorService.submit(() -> ExecutorUtil.safeExecute(() ->
                chatService.fetchRecommendedQuestion(input, sessionBo.getUid())));
        GeneralCallback generalCallback = buildGeneralCallback(sessionBo, input, callback, recommendedQuestions);
        defaultFragmentLlmRag.query(sessionBo, id, input, entryPoint, generalCallback);
    }

    @Override
    public void triggerTask(SessionBo sessionBo, long id, Long bizId, String input
            , String entryPoint, GeneralCallback callback, String version) {
        boolean needQueryAgain = defaultWorkflow.trigger(sessionBo, id, bizId, input, entryPoint, callback, version);
        if (needQueryAgain) {
            query(sessionBo, sessionBo.getUid(), bizId, id, input, entryPoint, callback, version);
        }
    }

    private GeneralCallback buildGeneralCallback(SessionBo sessionBo
            , String input, GeneralCallback generalCallback, Future<List<String>> recommendedQuestions) {
        return answerBo -> {
            if (answerBo == null) {
                return;
            }
            // 追加推荐问题
            if (answerBo.getStatus().equals(ChatAnswerStatusEnum.FINISH.getCode())
                    && CollectionUtils.isNotEmpty(sessionBo.fetchFragmentIdList())) {
                List<String> recommendedQuestionList = ExecutorUtil.waitFutureFinish(recommendedQuestions);
                if (CollectionUtils.isNotEmpty(recommendedQuestionList)) {
                    Object suffix = chatContentConverter.buildSuffixOptions(aiChatConfig.answerModelSuffixOptionPrefix
                            , recommendedQuestionList);
                    ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel
                            .buildRecommendedQuestionDisplayTraceLog(sessionBo.getSessionId(), input, recommendedQuestionList)));
                    answerBo.setAnswer(chatContentConverter.merge(answerBo.getAnswer()
                            , JsonUtil.toJson(Collections.singletonList(suffix))));
                }
            }
            generalCallback.answerCallback(answerBo);
        };
    }
}
