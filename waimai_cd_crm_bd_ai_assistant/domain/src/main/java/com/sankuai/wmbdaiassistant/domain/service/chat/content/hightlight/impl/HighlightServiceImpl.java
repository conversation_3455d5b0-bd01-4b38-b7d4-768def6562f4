package com.sankuai.wmbdaiassistant.domain.service.chat.content.hightlight.impl;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.domain.model.CategoryConfigModel;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.repository.TraceLogRepository;
import com.sankuai.wmbdaiassistant.domain.repository.query.TraceLogQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.hightlight.HighlightService;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

/**
 * 高亮服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/27 15:07
 */
@Service
@Slf4j
public class HighlightServiceImpl implements HighlightService {

    @Resource
    private TraceLogRepository traceLogRepository;

    @MdpConfig("highlight.max.day:3")
    private Integer highlightMaxDay;

    @MdpConfig("highlight.switch:true")
    private Boolean highlightSwitch;

    @Override
    public boolean isHighlight(Integer uid, CategoryConfigModel categoryConfigModel, Integer tenantId) {
        if (uid == null || categoryConfigModel == null || StringUtils.isBlank(categoryConfigModel.getContent())) {
            return false;
        }
        if (!highlightSwitch) {
            return false;
        }
        Date startDate = getHighlightTime(categoryConfigModel, tenantId);
        if (startDate == null) {
            return false;
        }
        Date endDate = DateUtils.addDays(startDate, highlightMaxDay);
        Date currentDate = new Date();

        // 当前日期大于结束日期，不高亮
        if (currentDate.after(endDate)) {
            return false;
        }

        // 检索选项更新时间到+N天内是否有点击事件
        TraceLogQuery traceLogQuery = TraceLogQuery.builder().uid(uid)
                .eventTypes(Collections.singletonList(TraceLogModel.EVENT_TRIGGER))
                .contentLikeFormat(String.format("%%%s%%", categoryConfigModel.getContent())).startDate(startDate)
                .endDate(endDate).entryPointLikeFormat("tab%").build();
        long triggerTimes = traceLogRepository.count(traceLogQuery);
        return triggerTimes == 0;
    }

    private Date getHighlightTime(CategoryConfigModel categoryConfigModel, Integer tenantId) {
        Map<String, String> HIGHLIGHT_TIME_MAP = Lion.getConfigRepository().getMap("category.highlight.time.map_"+tenantId, String.class);

        if (categoryConfigModel == null || categoryConfigModel.getAbilityType() == null
                || MapUtils.isEmpty(HIGHLIGHT_TIME_MAP)) {
            return null;
        }
        if (HIGHLIGHT_TIME_MAP.containsKey(categoryConfigModel.Signature())) {
            String timeStr = HIGHLIGHT_TIME_MAP.get(categoryConfigModel.Signature());
            return DateUtil.parseDate(timeStr);
        }
        return null;
    }
}
