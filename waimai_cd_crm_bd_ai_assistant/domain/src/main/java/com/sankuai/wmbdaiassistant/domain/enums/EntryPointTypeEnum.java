package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 请求的入口点类型
 * @create 2024/4/8 19:25
 */

@Getter
public enum EntryPointTypeEnum {
    INPUT(1, "自己输入"),

    VOICE_INPUT(2, "语音输入"),

    INPUT_PREDICTION(3, "联想输入"),

    TOOLBAR(4, "工具栏"),

    QUESTION_GUIDE(5, "问题引导"),

    OPTION_LIST(6, "选项列表"),

    POI_SELECTOR(7, "商家选择器"),

    POI_REJECT_SELECTOR(8, "驳回原因选择器")
    ;

    private int code;
    private String desc;

    EntryPointTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EntryPointTypeEnum findByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (EntryPointTypeEnum type : EntryPointTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
