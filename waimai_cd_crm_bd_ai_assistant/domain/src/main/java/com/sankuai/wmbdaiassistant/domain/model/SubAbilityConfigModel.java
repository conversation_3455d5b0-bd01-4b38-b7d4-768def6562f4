package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.Data;

/**
 * 首页及跳转链接的配置项
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 15:03
 */
@Data
public class SubAbilityConfigModel {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 超链接名称或者跳转名称
     */
    private String name;

    /**
     * 能力类型
     */
    private AbilityTypeEnum abilityType;

    /**
     * 配置内容(json)
     */
    private String config;

    /**
     * 有效标识
     */
    private ValidEnum valid;

}
