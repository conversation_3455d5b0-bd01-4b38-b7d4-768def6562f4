package com.sankuai.wmbdaiassistant.domain.repository.query;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2025/2/18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ArchiveQuery {

    /**
     *   说明: 主键
     */
    private Long id;

    /**
     *   说明: 业务线
     */
    private String bizLine;

    /**
     *   说明: 上级目录
     */
    private Long parentId;

    /**
     *   说明: 是否有效 0--无效 1--有效
     */
    private ValidEnum valid;

    /**
     * 分页参数，默认1
     */
    private Integer pageNum;

    /**
     * 分页参数，默认20
     */
    private Integer pageSize;
}
