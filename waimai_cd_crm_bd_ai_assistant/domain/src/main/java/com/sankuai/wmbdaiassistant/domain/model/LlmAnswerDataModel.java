package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.llm.LLMContentTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大模型流式响应结果业务对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmAnswerDataModel {

    /**
     * 事件类型
     */
    private LLMContentTypeEnum eventType;

    /**
     * chunk内容
     */
    private String chunkContent;

    /**
     * 事件是否结束
     */
    private boolean finish;

    /**
     * 返回一个响应结束模型
     * @return
     */
    public static LlmAnswerDataModel done() {
        return LlmAnswerDataModel.builder()
                .eventType(LLMContentTypeEnum.ANSWER)
                .finish(true)
                .build();
    }
}
