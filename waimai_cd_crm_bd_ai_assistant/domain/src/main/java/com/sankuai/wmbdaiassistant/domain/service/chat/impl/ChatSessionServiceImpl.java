package com.sankuai.wmbdaiassistant.domain.service.chat.impl;

import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.enums.SessionStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.SessionModel;
import com.sankuai.wmbdaiassistant.domain.repository.Page;
import com.sankuai.wmbdaiassistant.domain.repository.SessionRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatSessionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.cache.SessionCacheService;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricConstant;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricService;
import com.sankuai.wmbdaiassistant.domain.transaction.TransactionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
@Service
@Slf4j
public class ChatSessionServiceImpl implements ChatSessionService {

    @Resource
    private TransactionHelper transactionHelper;

    @Resource
    private SessionRepository sessionRepository;

    @Resource
    private SessionCacheService sessionCacheService;

    @Resource
    private MetricService metricService;

    @Override
    public SessionModel createSession(UserBo user) {
        //TODO 未知来源暂时用这个
        metricService.reportEvent(MetricConstant.BIZ_EVENT, MetricConstant.SESSION_SOURCE_IS_NULL);
        return createSession(user, SessionSourceConfigManager.BD_AI_ASSISTANT_BEE_CODE, "");
    }

    @Override
    public SessionModel createSession(UserBo user, String source, String extra) {
        return transactionHelper.doInTransaction(() -> {
            SessionModel session = insert(user, source, extra);

            SessionBo sessionBo = SessionBo.from(session.getUid(), user.getMis(), session.getId(), source, extra);
            sessionCacheService.saveOrUpdate(sessionBo);
            return session;
        });
    }

    @Override
    public void closeSession(SessionBo sessionBo, SessionStatusEnum sessionStatus) {
        transactionHelper.doInTransactionWithOutResult(() -> {
            SessionModel sessionModel = sessionRepository.findById(sessionBo.getSessionId());
            ExecutorUtil.executeIfNotNull(sessionModel, session -> {
                session.setUid(sessionBo.getUid());
                session.setMis(sessionBo.getMis());
                session.setId(sessionBo.getSessionId());
                session.setModifyTime(new Date());
                session.setStatus(sessionStatus);
                sessionRepository.update(session);
            });
            sessionCacheService.delete(sessionBo);
        });
    }

    @Override
    public SessionModel insert(UserBo user) {
        return insert(user, SessionSourceConfigManager.BD_AI_ASSISTANT_BEE_CODE, "");
    }

    public SessionModel insert(UserBo user, String source, String extraStr) {
        SessionModel session = new SessionModel();
        session.setStatus(SessionStatusEnum.ACTIVE);
        session.setUid(user.getUid());
        session.setCreateTime(new Date());
        session.setModifyTime(session.getCreateTime());
        session.setMis(user.getMis());
        session.setSource(source);
        session.setExtra(extraStr);
        sessionRepository.insert(session);
        return session;
    }

    @Override
    public List<SessionModel> pageValidSession(Long minId, Integer pageSize) {
        if (Objects.isNull(minId) || Objects.isNull(pageSize)) {
            return Collections.emptyList();
        }
        Page page = new Page();
        page.setStart(0);
        page.setPageSize(pageSize);
        return sessionRepository.findActiveSessionByPage(minId, page);
    }

    @Override
    public void updateSessionStatus(SessionModel session, Integer status) {
        session.setModifyTime(new Date());
        session.setStatus(SessionStatusEnum.getByCode(status));
        sessionRepository.update(session);
    }

    @Override
    public void updateSessionBo(SessionBo sessionBo, Runnable runnable) {
        if (runnable != null) {
            runnable.run();
        }
        sessionCacheService.saveOrUpdate(sessionBo);
    }
}
