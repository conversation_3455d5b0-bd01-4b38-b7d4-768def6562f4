package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.MergeUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.*;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.MsgTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.cache.AnswerCacheService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentCompressor;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.lock.DistributeLockService;
import com.sankuai.wmbdaiassistant.domain.service.chat.riskcontrol.RiskControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
@Component
@Slf4j
public class GeneralAbility implements BaseAbility {

    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private ChatService chatService;

    @Resource
    private GeneralCallback generalCallback;

    @Resource
    private ChatRecordService chatRecordService;

    @Resource
    private AbilityConfig abilityConfig;

    @Resource
    private AnswerCacheService answerCacheService;

    @Resource
    private RiskControlService riskControlService;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private DistributeLockService distributeLockService;

    @MdpConfig("session.lock.timeout:60000")
    private long sessionLockTimeout;

    @MdpConfig("merge.switch:false")
    private boolean mergeSwitch;

    private ExecutorService executorService = new ThreadPoolExecutor(20, 50, 5
            , TimeUnit.MINUTES, new ArrayBlockingQueue<>(300), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public AbilityTypeEnum getAbilityType() {
        return AbilityTypeEnum.GENERAL;
    }

    @Override
    public Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo) {
        SessionBo sessionBo = chatSubmitQueryBo.getSessionBo();

        // 1. 获取会话锁（释放锁在 Callback 里面）
        boolean acquireSuccess = distributeLockService.acquireSessionLock(sessionBo.getSessionId(), sessionLockTimeout);
        ParamCheckUtil.isTrue(acquireSuccess, aiChatConfig.aiChatConcurrentErrorMsg);

        // 2. 保存问题
        ChatMsgModel msg = chatRecordService.createQueryMsg(chatSubmitQueryBo);
        ParamCheckUtil.notNull(msg, "提问时，创建消息失败");

        // 3. 提问
        Long questionMsgId = msg.getId();
        String input = chatSubmitQueryBo.getContent();
        answerCacheService.cacheStatus(questionMsgId, ChatAnswerStatusEnum.ANSWERING);
        executorService.submit(() -> {
            ExecutorUtil.safeExecute(() -> {
                chatService.chat(sessionBo, sessionBo.getUid(), chatSubmitQueryBo.getBizId(), questionMsgId
                        , input, chatSubmitQueryBo.getEntryPointType(), generalCallback, chatSubmitQueryBo.getVersion());
            });
        });
        return questionMsgId;
    }

    @Override
    public ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo) {

        // 1. 获取回复
        GeneralAnswerBo answerBo = fetchAnswerFromQueue(requestBo);

        // 2. 构造返回
        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setQuestionMsgId(requestBo.getQuestionMsgId());
        chatFetchAnswerBo.setMsgId(requestBo.getMsgId());
        chatFetchAnswerBo.setAbilityType(requestBo.getAbilityType());
        chatFetchAnswerBo.setSubAbilityType(requestBo.getSubAbilityType());

        if (answerBo == null) {
            chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.ANSWERING.getCode());
            chatFetchAnswerBo.setCurrentContent("");
            return chatFetchAnswerBo;
        }

        // 3. 替换关键词
        ChatContentCompressor chatContentCompressor = new ChatContentCompressor();
        chatContentCompressor.putAll(answerBo.getOutputVariableMap());
        answerBo.setAnswer(chatContentCompressor.decompress(answerBo.getAnswer()));

        chatFetchAnswerBo.setSensitive(false);
        chatFetchAnswerBo.setStatus(answerBo.getStatus());
        chatFetchAnswerBo.setAnswerType(answerBo.getAnswerType());
        chatFetchAnswerBo.setMsgType(MsgTypeEnum.TEXT.getCode());
        chatFetchAnswerBo.setCurrentContent(answerBo.getAnswer());
        chatFetchAnswerBo.setTop1FQScore(answerBo.getTop1FQScore());
        chatFetchAnswerBo.setTop1QuestionId(answerBo.getTop1QuestionId());
        chatFetchAnswerBo.setTags(answerBo.getTags());

        String previousContent = answerCacheService.fetchPreviousContent(requestBo.getQuestionMsgId());
        String fullContent = chatContentConverter.merge(previousContent, answerBo.getAnswer());
        if (!ChatAnswerStatusEnum.isFinished(answerBo.getStatus())) {
            answerCacheService.cachePreviousContent(requestBo.getQuestionMsgId(), fullContent);
        }
        if (riskControlService.isRisk(requestBo.getQuestionMsgId(), chatContentConverter.toMarkdownTextFromJson(fullContent))) {
            answerCacheService.cacheStatus(requestBo.getQuestionMsgId(), ChatAnswerStatusEnum.FINISH);
            chatFetchAnswerBo.setSensitive(true);
            chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
            chatFetchAnswerBo.setCurrentContent(abilityConfig.aiSensitiveReplaceContent);
            return chatFetchAnswerBo;
        }

        return chatFetchAnswerBo;
    }

    private GeneralAnswerBo fetchAnswerFromQueue(ChatFetchAnswerRequestBo requestBo) {
        int loopCount = 1;
        int maxCount = abilityConfig.fetchAiAnswerMaxLoopCount;

        String answerContent = null;
        GeneralAnswerBo mergedAnswerBo = null;
        Map<String, String> outputVairbaleMap = null;
        while (true) {
            loopCount++;

            GeneralAnswerBo answerBo = answerCacheService.popFromQueue(requestBo.getQuestionMsgId(), 100L);
            if (answerBo != null) {
                answerContent = mergeSwitch ? chatContentConverter.mergeList(answerContent, answerBo.getAnswer())
                        : chatContentConverter.merge(answerContent, answerBo.getAnswer());
                outputVairbaleMap = MergeUtil.mergeMaps(outputVairbaleMap, answerBo.getOutputVariableMap());

                mergedAnswerBo = answerBo;
                mergedAnswerBo.setAnswer(answerContent);
                mergedAnswerBo.setOutputVariableMap(outputVairbaleMap);
                if (ChatAnswerStatusEnum.isFinished(mergedAnswerBo.getStatus())) {
                    break;
                }
            }

            if (loopCount > maxCount) {
                String markdownText = chatContentConverter.toMarkdownTextFromJson(answerContent);
                if (markdownText != null && chatContentConverter.containsNotCompleteMarkdownLinkOrImage(markdownText)) {
                    if (loopCount > abilityConfig.fetchAiAnswerLatestMaxLoopCount) {
                        break;
                    }
                    maxCount = maxCount + 1;
                    continue;
                }
                break;
            }
        }
        return mergedAnswerBo;
    }
}
