package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Wiki 状态枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 10:07
 */
@Getter
public enum WikiStateEnum {
    INIT("init", "初始化"),
    FINISH("finish", "导入完成"),
    FAIL("fail", "导入失败"),
    PRE_ENABLE("pre_enable", "预生效"),
    PRE_UPDATE("pre_update", "预更新"),
    ENABLE("enable", "已生效"),
    DELETED("deleted", "已删除"),
    ;

    private String code;
    private String remark;

    WikiStateEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public static WikiStateEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WikiStateEnum value : WikiStateEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
