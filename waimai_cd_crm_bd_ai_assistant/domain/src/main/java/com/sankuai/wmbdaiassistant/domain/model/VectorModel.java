package com.sankuai.wmbdaiassistant.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 向量对象
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-16 15:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorModel {

    /**
     * 向量库主键
     */
    private Long id;

    /**
     * 向量值
     */
    private List<Double> vector;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 领域
     */
    private List<Long> domainIdList;

    /**
     * 相似度，范围 [-1,1]，值越大，相似度越高
     */
    private Double similarity;
}
