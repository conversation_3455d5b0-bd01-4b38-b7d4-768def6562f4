package com.sankuai.wmbdaiassistant.domain.enums.llm;

import lombok.Getter;

@Getter
public enum LLMContentTypeEnum {
    THINKING("thinking", "思考"),
    ANSWER("answer", "回答"),
    ;

    private String code;
    private String desc;

    LLMContentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LLMContentTypeEnum findByCode(String code) {
        for (LLMContentTypeEnum type : LLMContentTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
