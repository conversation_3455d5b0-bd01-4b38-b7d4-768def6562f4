package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl.DefaultFragmentLlmRag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 多轮中触发rag
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-24 10:39
 */
@Slf4j
@Component
public class TaskTriggerRagPatternProcessor implements PatternProcessor {

    private static final String PREFIX = "TriggerQuery:";

    @Resource
    private DefaultFragmentLlmRag defaultFragmentLlmRag;

    @Override
    public boolean match(String pattern) {
        return pattern.startsWith(PREFIX);
    }

    @Override
    public boolean process(PatternParam param) {
        String pattern = param.getPattern();
        String query = pattern.substring(pattern.indexOf(PREFIX) + PREFIX.length()).trim();

        defaultFragmentLlmRag.query(param.getSession(), param.getMsgId()
                , query, param.getEntryPoint(), param.getCallback());

        return false;
    }
}
