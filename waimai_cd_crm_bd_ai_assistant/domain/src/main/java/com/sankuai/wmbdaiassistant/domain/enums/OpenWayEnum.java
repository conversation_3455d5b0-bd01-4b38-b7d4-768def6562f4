package com.sankuai.wmbdaiassistant.domain.enums;

/**
 * @description: 打开方式
 * @author: maningning03
 * @create: 2025/4/22
 **/
public enum OpenWayEnum {

    inNewTab("inNewTab", "新建页面打开"),
    inCurrentTabFull("inCurrentTabFull", "当前页面全屏打开"),
    ;

    private String code;
    private String desc;

    OpenWayEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OpenWayEnum findByCode(String code) {
        for (OpenWayEnum type : OpenWayEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
