package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.FaqModel;

import java.util.List;

/**
 * faq 仓储
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-05 19:28
 */
public interface FaqRepository {

    /**
     * 根据ID查询
     *
     * @param faqModel 模型
     * @return 主键ID
     */
    Long insert(FaqModel faqModel);

    /**
     * 批量添加
     * @param faqModelList 输出模型列表
     * @return ID List
     */
    List<Long> batchInsert(List<FaqModel> faqModelList);

    /**
     * 修改
     *
     * @param faqModel FAQ
     * @return
     */
    boolean update(FaqModel faqModel);

    /**
     * 根据ID查询
     *
     * @param faqId 主键ID
     * @return 模型
     */
    FaqModel findById(Long faqId);

    /**
     * 根据ID批量查询
     * 
     * @param faqIds FAQ主键列表
     * @return
     */
    List<FaqModel> findByIds(List<Long> faqIds);
}
