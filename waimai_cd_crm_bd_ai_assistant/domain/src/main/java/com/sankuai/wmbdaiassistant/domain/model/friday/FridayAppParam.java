package com.sankuai.wmbdaiassistant.domain.model.friday;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Friday请求参数模型
 * <a href="https://km.sankuai.com/collabpage/1934713151">...</a>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayAppParam {

    /**
     * 应用Id
     */
    private String appId;

    /**
     * 用户名，最大长度64字符
     */
    private String userId;

    /**
     * 用户类型：MIS、MEITUAN、EMP_ID、DX_UID、WE_CHAT
     */
    private String userType;
    /**
     * 语音开放平台鉴权token
     */
    private String accessToken;

    /**
     * 输入内容列表
     */
    private List<String> utterances;

    /**
     * 请求头信息
     */
    private Map<String, String> headers;

    /**
     * 是否启用流式，默认true
     */
    @Default
    private Boolean stream = Boolean.TRUE;


    /**
     * 是否开启debug模式，默认false
     */
    @Default
    private Boolean debug = Boolean.FALSE;

    /**
     * 是否为多轮会话，默认true
     */
    @Default
    private Boolean multiTurn = Boolean.TRUE;

    /**
     * 是否重新生成，默认false
     */
    @Default
    private Boolean redo = Boolean.FALSE;

    /**
     * 业务随路参数
     */
    private Map<String, String> businessInfo;

    /**
     * 会话Id
     */
    private String conversationId;

    /**
     * 会话过期策略：AUTO-自动过期(默认), NEVER-永不过期
     */
    private String expirationPolicy;

    /**
     * 最大超时时间(毫秒)，默认90秒，最大300秒
     */
    private Long timeout;
}
