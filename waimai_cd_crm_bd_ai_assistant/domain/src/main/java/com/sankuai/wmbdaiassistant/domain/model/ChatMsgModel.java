package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatContentTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.SensitiveStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 聊天记录
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-05-27 19:26
 */
@Data
public class ChatMsgModel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会话id
     */
    private Long sessionId;

    /**
     * 用户id
     */
    private Integer uid;

    /**
     * 用户mis
     */
    private String mis;

    /**
     * 技能类型
     */
    private AbilityTypeEnum abilityType;

    /**
     * 内容类型
     */
    private ChatContentTypeEnum contentType;

    /**
     * 回答类型
     */
    private AnswerTypeEnum answerType;

    /**
     * 内容
     */
    private String content;

    /**
     * 向用户展示的文本内容
     */
    private String viewContent;

    /**
     * 涉敏状态
     */
    private SensitiveStatusEnum sensitiveStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 关联的多轮会话ID
     */
    private String taskSessionId;

    /**
     * 上下文信息，json 格式
     */
    private String context;

    /**
     * 请求的入口点类型
     */
    private String entryPointType;
}
