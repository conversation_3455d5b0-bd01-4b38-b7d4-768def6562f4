package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 新店加权卡片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-01-10 15:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewPoiWeightCard implements ContentElement {
    private final String type = ContentElementTypeEnum.NEW_POI_WEIGHT_CARD.getCode();

    @JsonProperty("insert")
    private NewPoiWeightCardInfo newPoiWeightingCard;

    @Override
    public String toMarkdownText() {
        if (newPoiWeightingCard == null || newPoiWeightingCard.getContent() == null
                || CollectionUtils.isEmpty(newPoiWeightingCard.getContent().getList())) {
            return "";
        }
        List<NewPoiWeightCardContent> contentList = newPoiWeightingCard.getContent().getList();
        StringBuilder sb = new StringBuilder();
        contentList.forEach(content -> {
            sb.append("新店加权").append(content.getTitle()).append("\n");
            ExecutorUtil.executeIfNotEmpty(content.getDescList(), descList -> {
                descList.forEach(desc -> sb.append("新店加权").append(desc).append("\n"));
            });
            ExecutorUtil.executeIfNotNull(content.getAvatar(), avatar -> {
                sb.append("新店加权状态：").append(avatar.getStatus()).append("\n");
                sb.append("新店加权生效天数：").append(avatar.getTotalLimit()).append("\n");
            });
            sb.append("\n\n");
        });
        return sb.toString();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewPoiWeightCardInfo {
        @JsonProperty("newPoiWeightingCard")
        private NewPoiWeightCardContentList content;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewPoiWeightCardContentList {
        @JsonProperty("list")
        private List<NewPoiWeightCardContent> list;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewPoiWeightCardContent {
        private String title;

        @JsonProperty("desc")
        private List<String> descList;

        @JsonProperty("avatar")
        private NewPoiWeightCardAvatar avatar;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewPoiWeightCardAvatar {
        /**
         * 新店加权状态
         */
        private Integer status;

        /**
         * 新店加权生效天数
         */
        private Integer totalLimit;
    }
}
