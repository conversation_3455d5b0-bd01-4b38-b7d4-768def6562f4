package com.sankuai.wmbdaiassistant.domain.service.chat.vectordb;

import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;

import java.util.List;

/**
 * 向量库服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-12 18:50
 */
public interface VectorDatabaseService {

    /**
     * 向量生成
     *
     * @param input 输入
     * @return 向量
     */
    List<Double> embedding(String input);

    /**
     * 插入 Phrase 到向量库
     *
     * @param phraseModel phrase
     * @return 是否成功
     */
    boolean insertPhrase(PhraseModel phraseModel);

    /**
     * 删除
     *
     * @param phraseIdList phraseId列表
     * @return 是否成功
     */
    boolean deleteByPhraseIdList(List<Long> phraseIdList);

    /**
     * 删除
     *
     * @param phraseList phrase列表
     * @return 是否成功
     */
    boolean deleteByPhraseList(List<PhraseModel> phraseList);

    /**
     * 修改
     *
     * @param phraseModel phrase对象
     * @return 是否修改成功
     */
    boolean modifyPhrase(PhraseModel phraseModel);

    /**
     * 搜索
     *
     * @param phraseVectorQuery 查询条件
     * @return 返回匹配到的 vectorBo 对象
     */
    List<VectorBo> searchPhrase(PhraseVectorQuery phraseVectorQuery);

}
