package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 表格
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-01-10 14:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Table implements ContentElement {
    private final String type = ContentElementTypeEnum.TABLE.getCode();

    private TableInsert insert;

    @Override
    public String toMarkdownText() {
        if (insert == null || insert.getContent() == null) {
            return "";
        }
        TableContent content = insert.getContent();
        StringBuilder stringBuilder = new StringBuilder();

        ExecutorUtil.executeIfNotBlank(content.getComment(), comment -> stringBuilder.append(comment).append("\n"));
        ExecutorUtil.executeIfTrue(content.getShowCollapse()
                , () -> stringBuilder.append(content.getCollapseDesc()).append("\n"));
        ExecutorUtil.executeIfNotEmpty(content.getColumns(), columns -> {
            stringBuilder.append("|");
            columns.forEach(column -> stringBuilder.append(column.getTitle()).append("|"));
            stringBuilder.append("\n");
            stringBuilder.append("|");
            columns.forEach(column -> stringBuilder.append("-----").append("|"));
            stringBuilder.append("\n");
        });

        ExecutorUtil.executeIfNotEmpty(content.getRows(), rows -> {
            rows.forEach(row -> {
                stringBuilder.append("|");
                content.getColumns().forEach(column -> stringBuilder.append(row.get(column.getDataIndex())).append("|"));
                stringBuilder.append("\n");
            });
        });
        return stringBuilder.toString();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableInsert {
        @JsonProperty("table")
        private TableContent content;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableContent {
        private List<TableColumnDesc> columns;

        @JsonProperty("data")
        private List<Map<String, Object>> rows;

        /**
         * collapseDesc存在时默认为true，是否展示折叠控件
         */
        private Boolean showCollapse;

        /**
         * 折叠控件提示文案
         */
        private String collapseDesc;

        /**
         *  默认为折叠，折叠控件初始状态（折叠/展开）
         */
        private Boolean collapseState;

        /**
         * 表格注释，为空或者undefined则不展示，如：*点评高分规则:评分数大于75,且评价数大于50
         */
        private String comment;

        /**
         * 是否允许表格滚动，当列数较多时可能需要
         */
        private Boolean scrollable;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableColumnDesc {
        /**
         * 列的英文名
         */
        private String dataIndex;

        /**
         * 列的中文名
         */
        private String title;
    }

}
