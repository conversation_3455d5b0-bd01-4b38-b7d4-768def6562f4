package com.sankuai.wmbdaiassistant.domain.service.sign.impl;

import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.model.SignRecordModel;
import com.sankuai.wmbdaiassistant.domain.repository.SignRecordRepository;
import com.sankuai.wmbdaiassistant.domain.service.sign.SignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-05
 */
@Slf4j
@Service
public class SignServiceImpl implements SignService {

    @Resource
    private SignRecordRepository signRecordRepository;

    @Override
    public void sign(UserBo user) {
        if (Objects.isNull(user)) {
            return;
        }
        SignRecordModel signRecord = new SignRecordModel();
        signRecord.setUid(user.getUid());
        signRecord.setMis(user.getMis());
        signRecord.setSignTime(new Date());
        signRecordRepository.insert(signRecord);
    }

    @Override
    public boolean alreadySigned(Integer uid) {
        return signRecordRepository.countByUid(uid) > 0L;
    }

}
