package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.VectorModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.VectorQuery;

import java.util.List;

/**
 * 向量
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-15 15:25
 */
public interface VectorRepository {

    /**
     * 插入
     *
     * @param vectorModel 向量库对象
     * @return 是否成功
     */
    Long insert(VectorModel vectorModel);

    /**
     * 删除
     *
     * @param idList 向量库中的主键ID列表
     * @return 是否成功
     */
    boolean delete(List<Long> idList);

    /**
     * 搜索
     *
     * @param vectorQuery 查询条件
     * @return 返回匹配到的 vectorBo 对象
     */
    List<VectorModel> findByQuery(VectorQuery vectorQuery);
}
