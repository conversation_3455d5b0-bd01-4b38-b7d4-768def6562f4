package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.enums.FaqImportStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.FaqImportProcessModel;

import java.util.List;

/**
 * Faq批量插入过程
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
public interface FaqImportProcessRepository {
	/**
	 * 插入Faq批量导入过程模型
	 *
	 * @param processModel Faq批量导入过程模型
	 * @return 主键ID
	 */
	Long insert(FaqImportProcessModel processModel);

	/**
	 * 修改
	 *
	 * @param processModel Faq批量导入过程模型
	 * @return 是否成功
	 */
	boolean update(FaqImportProcessModel processModel);

	/**
	 * 根据ID查询
	 *
	 * @param processId 主键ID
	 * @return 模型
	 */
	FaqImportProcessModel findById(Long processId);

    /**
     * 根据状态查询，返回限制数量的结果
     * 
     * @param status 状态 FaqImportStatusEnum
     * @return 模型
     */
    List<FaqImportProcessModel> findByStatus(FaqImportStatusEnum status);

}
