package com.sankuai.wmbdaiassistant.domain.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-13
 */
public enum ServiceScoreSubAbilityEnum {
    SERVICE_SCORE_RATE(1, "查看服务得分率"),
    SERVICE_MAX_SCORE(2, "查看服务分满分"),
    SERVICE_SCORE(3, "查看服务分实际得分"),
    SERVICE_NUM(4, "查看服务量");


    private int code;
    private String desc;

    ServiceScoreSubAbilityEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static List<ServiceScoreSubAbilityEnum> getAllSubAbility() {
        return Lists.newArrayList(ServiceScoreSubAbilityEnum.values());
    }
}

