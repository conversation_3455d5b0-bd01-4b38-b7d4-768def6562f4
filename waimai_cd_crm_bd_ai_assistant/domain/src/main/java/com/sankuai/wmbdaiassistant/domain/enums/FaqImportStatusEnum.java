package com.sankuai.wmbdaiassistant.domain.enums;

import com.sankuai.wmbdaiassistant.common.enums.CodeBaseEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-24 11:37
 */
@Getter
public enum FaqImportStatusEnum implements CodeBaseEnum {

    INIT("init", "进行中"),
    IMPORT_FAIL("imported_fail", "录入失败"),
    IMPORTED("imported", "已导入"),
    RECORDED("recorded", "已录入"),
    FINISH_FAIL("finish_fail", "完成失败"),
    FINISH("finish", "已完成"),
    ROLLBACK("rollback", "已回滚")

    ;

    private String code;
    private String desc;

    FaqImportStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FaqImportStatusEnum getByCode(String code) {
        for (FaqImportStatusEnum faqImportStatusEnum : values()) {
            if (StringUtils.equals(faqImportStatusEnum.getCode(), code)) {
                return faqImportStatusEnum;
            }
        }
        throw new IllegalArgumentException(String.format("FaqImportStatusEnum 不识别 code %s", code));
    }
}
