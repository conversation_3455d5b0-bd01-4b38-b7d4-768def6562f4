package com.sankuai.wmbdaiassistant.domain.repository.query;

import com.sankuai.wmbdaiassistant.domain.enums.PhraseStateEnum;
import lombok.Data;

import java.util.List;

/**
 * Phrase 查询
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-01-06 10:58
 */
@Data
public class PhraseQuery {

    /**
     * 模糊匹配的 phrase
     */
    private String phraseLike;

    /**
     * 域ID
     */
    private Long domainId;

    /**
     * 状态
     */
    private List<PhraseStateEnum> stateList;
}
