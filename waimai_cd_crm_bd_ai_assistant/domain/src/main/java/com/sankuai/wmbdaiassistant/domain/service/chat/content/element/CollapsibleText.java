package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 可折叠文案
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR>
 * @date 2025-05-07 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollapsibleText implements ContentElement {

    private final String type = ContentElementTypeEnum.COLLAPSIBLE_TEXT.getCode();

    @JsonProperty("insert")
    private CollapsibleText.CollapsibleTextObject info;

    @Override
    public String toMarkdownText() {
        if (info == null || info.getCollapsibleText() == null || CollectionUtils.isEmpty(info.getCollapsibleText().getContent())) {
            return null;
        }
        List<Object> contentList = info.getCollapsibleText().getContent();
        StringBuilder stringBuilder = new StringBuilder();
        for (Object object : contentList) {
            stringBuilder.append(object);
        }
        return stringBuilder.toString();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CollapsibleTextObject  {
        private CollapsibleTextInfo collapsibleText;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CollapsibleTextInfo {
        private List<Object> content;
        private String extendButtonName;
        private Long maxHeight;
    }


}