package com.sankuai.wmbdaiassistant.domain.bo;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
@Getter
@Setter
public class ChatFetchAnswerBo implements Cloneable {
    /**
     * 回答状态 枚举见 com.sankuai.wmbdaiassistant.server.common.ChatAnswerStatusEnum
     */
    private Integer status;

    /**
     * 是否涉敏 true涉敏，false不涉敏
     */
    private boolean sensitive;
    /**
     * 能力类型
     */
    private Integer abilityType;
    /**
     * 回答消息ID
     */
    private Long msgId;

    /**
     * 问题记录id
     */
    private Long questionMsgId;
    /**
     * 子能力类型(文本输入可以不填)
     */
    private Integer subAbilityType;

    /**
     * 是否还有数据
     */
    private boolean hasNext;

    /**
     * 页码(请求下一组选项时必填)
     */
    private Integer pageNum;

    /**
     * 消息类型(1.普通纯文本，2.选项型) 枚举见com.sankuai.wmbdaiassistant.server.common.MsgTypeEnum
     */
    private Integer msgType;

    private Integer answerType;
    /**
     * 当前纯文本消息(消息类型=普通纯文本才有)
     */
    private String currentContent;

    /**
     * 内容涉敏时，替换文本内容
     */
    private String viewContent;

    /**
     * 前序所有消息内容(消息类型=普通纯文本才有)
     */
    private String previousContent;

    /**
     * 前置内容(消息类型=选项型才有)
     */
    private String prefixTextContent;

    /**
     * 后置内容(消息类型=选项型才有)
     */
    private String postTextContent;

    /**
     * 选项(消息类型=选项型才有)
     */
    private List<AnswerItemBo> items;

    private List<String> imageList;

    private Double top1FQScore;

    private Long  top1QuestionId;

    private List<String> tags;

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(items) && StringUtils.isEmpty(currentContent)  && StringUtils.isEmpty(previousContent);
    }

    @Override
    public ChatFetchAnswerBo clone() {
        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();

        chatFetchAnswerBo.setStatus(status);
        chatFetchAnswerBo.setSensitive(sensitive);
        chatFetchAnswerBo.setAbilityType(abilityType);
        chatFetchAnswerBo.setMsgId(msgId);
        chatFetchAnswerBo.setQuestionMsgId(questionMsgId);
        chatFetchAnswerBo.setSubAbilityType(subAbilityType);
        chatFetchAnswerBo.setHasNext(hasNext);
        chatFetchAnswerBo.setPageNum(pageNum);
        chatFetchAnswerBo.setMsgType(msgType);
        chatFetchAnswerBo.setAnswerType(answerType);
        chatFetchAnswerBo.setCurrentContent(currentContent);
        chatFetchAnswerBo.setViewContent(viewContent);
        chatFetchAnswerBo.setPreviousContent(previousContent);
        chatFetchAnswerBo.setPrefixTextContent(prefixTextContent);
        chatFetchAnswerBo.setPostTextContent(postTextContent);
        chatFetchAnswerBo.setItems(items);
        chatFetchAnswerBo.setImageList(imageList);
        chatFetchAnswerBo.setTop1FQScore(top1FQScore);
        chatFetchAnswerBo.setTop1QuestionId(top1QuestionId);
        chatFetchAnswerBo.setTags(tags);

        return chatFetchAnswerBo;
    }

    @Setter
    @Getter
    public static class AnswerItemBo {
        /**
         * 能力类型
         */
        private Integer abilityType;
        /**
         * 子能力类型(缺省)
         */
        private Integer subAbilityType;
        /**
         * 选项名称
         */
        private String content;
        /**
         * 跳转链接
         */
        private String url;
        /**
         * 用户点击选项之后的操作类型：1.url跳转，2.继续提问 枚举见com.sankuai.wmbdaiassistant.server.common.OperationTypeEnum
         */
        private Integer operationType;
    }
}
