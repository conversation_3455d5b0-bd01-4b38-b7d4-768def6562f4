package com.sankuai.wmbdaiassistant.domain.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 状态
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-22 13:26
 */
@Getter
public enum PhraseStateEnum {

    ENABLE("enable", "启用"),
    DISABLE("disable", "禁用"),
    DELETE("delete", "删除"),
    ;

    private String code;
    private String desc;

    PhraseStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PhraseStateEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (PhraseStateEnum phraseStateEnum : values()) {
            if (phraseStateEnum.getCode().equals(code)) {
                return phraseStateEnum;
            }
        }
        return null;
    }
}
