package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 选择项
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferenceDoc implements ContentElement {
    private final String type = ContentElementTypeEnum.REFERENCE_DOC.getCode();

    @JsonProperty("insert")
    private ReferenceDocInfo info;

    @Override
    public String toMarkdownText() {
        return "";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReferenceDocInfo  {
        private ReferenceDocs referenceDoc;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReferenceDocs {
        private String title;
        private List<Doc> list;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Doc {
        private String type;
        private String text;
        private String link;
    }

}
