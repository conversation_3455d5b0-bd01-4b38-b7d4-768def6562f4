package com.sankuai.wmbdaiassistant.domain.service.chat.metric;

/**
 * 打点的常量
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-15 13:54
 */
public class MetricConstant {

    public static final String BIZ = "biz";

    public static final String BIZ_ERROR = "biz_error";

    public static final String BIZ_EVENT = "biz_event";

    public static final String AI_MODEL_TYPE = "AIModel";
    public static final String AI_MODEL_TYPE_NAME_CHATGLM3_EMBEDDING = "chatglm3.embedding";
    public static final String AI_MODEL_TYPE_NAME_CHATGLM_CHAT = "chatglm.chat";
    public static final String AI_MODEL_TYPE_NAME_CHATGPT_CHAT = "chatgpt.chat";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_7B_CHAT = "longcat7b.chat";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_70B_CHAT = "longcat70b.chat";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_LARGE_32K_CHAT = "longcat32k.chat";
    public static final String AI_MODEL_TYPE_NAME_CHATGLM_CHAT_STREAM = "chatglm.chat.stream";
    public static final String AI_MODEL_TYPE_NAME_CHATGPT_CHAT_STREAM = "chatgpt.chat.stream";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_7B_CHAT_STREAM = "longcat7b.chat.stream";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_70B_CHAT_STREAM = "longcat70b.chat.stream";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_LARGE_32K_CHAT_STREAM = "longcat32k.chat.stream";
    public static final String AI_MODEL_TYPE_NAME_CHATGPT_FUNCTION_CALL = "chatgpt.function.call";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_7B_FUNCTION_CALL = "longcat7b.function.call";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_70B_FUNCTION_CALL = "longcat70b.function.call";
    public static final String AI_MODEL_TYPE_NAME_LONGCAT_LARGE_32K_FUNCTION_CALL = "longcat32k.function.call";
    public static final String AI_MODEL_TYPE_NAME_VEX_INSERT = "vex.insert";
    public static final String AI_MODEL_TYPE_NAME_VEX_SEARCH = "vex.search";

    public static final String DOMAIN = "domain";
    public static final String DOMAIN_RECOGNITION_QUERY = "domain_recognition_query";


    public static final String INTENTION_RECOGNITION_QUERY = "intention_recognition_query";

    public static final String POI = "poi";

    public static final String POI_SEARCH = "poi_search";

    public static final String POI_SEARCH_PAGE = "poi_search_page";

    public static final String POI_QUERY= "poi_query";

    public static final String POI_BATCH_QUERY= "poi_batch_query";

    public static final String POI_OWNED_COUNT= "poi_owned_count";

    public static final String CRM_PLATFORM = "crm_platform";

    public static final String CRM_PLATFORM_USER_QUERY = "crm_platform_user_query";

    public static final String SESSION_SOURCE_IS_NULL = "session_source_is_null";

    public static final String FRAGMENT_RECALL = "fragment_recall";

    public static final String RECOMMEND_QUESTION = "recommend_question";

    public static final String FIRST_ANSWER = "first_answer";

    public static final String KM = "km";

    public static final String KM_PERMISSION_AUTHENTICATION = "km_permission_authentication";
}
