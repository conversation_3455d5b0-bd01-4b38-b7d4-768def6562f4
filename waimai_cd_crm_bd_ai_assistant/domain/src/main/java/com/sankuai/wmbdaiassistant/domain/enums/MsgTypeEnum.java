package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/11
 **/
public enum MsgTypeEnum {
    TEXT(1, "普通纯文本"),
    SELECTION(2, "选项型");
    private int code;
    private String desc;

    MsgTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MsgTypeEnum findByCode(int code) {
        for (MsgTypeEnum type : MsgTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    public static boolean isText(int code) {
        return Objects.equals(TEXT.getCode(), code);
    }
}
