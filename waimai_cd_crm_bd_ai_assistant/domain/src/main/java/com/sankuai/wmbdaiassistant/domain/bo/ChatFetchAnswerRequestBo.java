package com.sankuai.wmbdaiassistant.domain.bo;

import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/11
 **/
@Setter
@Getter
public class ChatFetchAnswerRequestBo {

    private SessionBo sessionBo;
    /**
     * 能力类型
     */
    private Integer abilityType;
    /**
     * 回答消息ID
     */
    private Long msgId;

    /**
     * 问题记录id
     */
    private Long questionMsgId;

    /**
     * 子能力类型(文本输入可以不填)
     */
    private Integer subAbilityType;

    /**
     * 页码(请求下一组选项时必填)
     */
    private Integer pageNum;

    /**
     * 兼容字段，目前V3全量
     */
    private String version;


    public static ChatFetchAnswerRequestBo of(Integer abilityType, String version) {
        ChatFetchAnswerRequestBo requestBo = new ChatFetchAnswerRequestBo();
        requestBo.setAbilityType(abilityType);
        requestBo.setVersion(version);
        return requestBo;
    }
}
