package com.sankuai.wmbdaiassistant.domain.service.chat.output.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.enums.OutputTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.apiarrange.ApiArrangeService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.output.OutputModelProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 模版类型输出处理
 * @create 2024/4/23 11:45
 */

@Component
@Slf4j
public class TemplateOutputModelProcessor implements OutputModelProcessor {
    @MdpConfig("output.dsl.template:{\"tasks\":[],\"name\":\"\",\"description\":\"\",\"outputs\":\"%s\"}")
    private String dslTemplate;

    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private ApiArrangeService apiArrangeService;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Override
    public boolean isMatch(OutputModel outputModel) {
        return OutputTypeEnum.TEMPLATE.equals(outputModel.getType());
    }

    @Override
    public void processOutputToAnswer(OutputModel outputModel, GeneralAnswerBo answer, String params) {
        answer.setAnswer(this.getContentByOutput(outputModel, params));
        answer.setTags(outputModel.getTags());
    }

    @Override
    public String getContentByOutput(OutputModel outputModel, String params) {
        
        String content = outputModel.getContent();
        if (StringUtils.isNotBlank(outputModel.getTtUrl())) {
            content = chatContentConverter.merge(outputModel.getContent(), chatContentConverter.buildTtUrl(outputModel.getTtUrl()));
        }

        String response = apiArrangeService.arrange(buildDsl(content), params);
        return parseResponse(response);
    }

    private String buildDsl(String template) {
        return String.format(dslTemplate, StringUtil.escape(template));
    }

    private static String parseResponse(String response) {
        if (StringUtils.isBlank(response)) {
            return response;
        }
        response = StringUtil.trim(response);

        boolean isJsonText = JsonUtil.isJsonText(response);
        if (!isJsonText) {
            response = StringUtil.trimQuotation(response);
            response = StringUtil.unescape(response);
        }
        return response;
    }
}
