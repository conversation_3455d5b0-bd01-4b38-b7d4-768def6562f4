package com.sankuai.wmbdaiassistant.domain.model;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.enums.SessionStatusEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

/**
 * 会话模型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-05-24 16:32
 */
@Data
public class SessionModel {

    private static final String TAG_STANDARD_PHRASE_PREFIX = "StandardPhrase_";
    private static final String TAG_PHRASE_PREFIX = "Phrase_";
    private static final String TAG_SUBMITTED_TT = "SubmittedTT";
    private static final String TAG_SESSION_REMARKED = "SessionRemarked";
    private static final String TAG_DOMAIN_PREFIX = "Domain_";
    private static final String TAG_HAVA_CONVERSATIONS = "Chat";
    private static final String TAG_DONE = "Done";
    private static final String TAG_USER_RD = "USER_RD";
    private static final String TAG_FRAGMENT_RAG = "FragmentRag";
    private static final String TAG_FRAGMENT_GRAY = "FragmentGray";

    /**
     * 主键
     */
    private Long id;

    /**
     * uid
     */
    private Integer uid;

    /**
     * 用户misId
     */
    private String mis;

    /**
     * 会话状态
     */
    private SessionStatusEnum status;

    /**
     * 来源
     */
    private String source;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 会话标签（用于会话筛选）
     */
    private Set<String> tags;

    /**
     * 额外信息
     */
    private Map<String, Object> extra;

    public Boolean submittedTt() {
        return CollectionUtils.isNotEmpty(tags) && tags.contains(getSubmittedTtTag());
    }

    public Boolean remarked() {
        return CollectionUtils.isNotEmpty(tags) && tags.contains(getRemarkedTag());
    }

    public void addTag(String tag) {
        if (CollectionUtils.isEmpty(tags)) {
            this.tags = new HashSet<>();
        }
        this.tags.add(tag);
    }

    public void addTags(List<String> addTags) {
        DefaultUtil.defaultList(addTags).forEach(this::addTag);
    }

    public List<Long> getStandardPhraseIdList() {
        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        return tags.stream().filter(tag -> tag.contains(TAG_STANDARD_PHRASE_PREFIX))
                .map(tag -> Long.valueOf(tag.substring(TAG_STANDARD_PHRASE_PREFIX.length()))).collect(Collectors.toList());
    }

    public List<Long> getDomainIdList() {
        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        return tags.stream().filter(tag -> tag.contains(TAG_DOMAIN_PREFIX))
                .map(tag -> Long.valueOf(tag.substring(TAG_DOMAIN_PREFIX.length()))).collect(Collectors.toList());
    }

    public void setExtra(String extraStr) {
        if(StringUtils.isBlank(extraStr)){
            this.extra = new HashMap<>();
            return;
        }
        this.extra = JsonUtil.fromJson(extraStr, new TypeReference<HashMap<String, Object>>() {});
    }

    public void setExtra(Map<String, Object> extra) {
        if (Objects.isNull(extra)) {
            this.extra = new HashMap<>();
            return;
        }
        this.extra = extra;
    }

    public static String getSubmittedTtTag() {
        return TAG_SUBMITTED_TT;
    }

    public static String getRemarkedTag() {
        return TAG_SESSION_REMARKED;
    }

    public static String getUserRdTag() {
        return TAG_USER_RD;
    }

    public static String getPhraseTag(Long phraseId) {
        return String.format("%s%d", TAG_PHRASE_PREFIX, phraseId);
    }

    public static String getStandardPhraseTag(Long phraseId) {
        return String.format("%s%d", TAG_STANDARD_PHRASE_PREFIX, phraseId);
    }

    public static String getDomainTag(Long domainId) {
        return String.format("%s%d", TAG_DOMAIN_PREFIX, domainId);
    }

    public static String getHaveConversationTag() {
        return TAG_HAVA_CONVERSATIONS;
    }

    public static String getToolbarTag(String suffix) {
        return String.format("toolbar_%s", suffix);
    }

    public static String getDoneTag() {
        return TAG_DONE;
    }

    public static String getFragmentRagTag() {
        return TAG_FRAGMENT_RAG;
    }

    public static String getFragmentGrayTag() {
        return TAG_FRAGMENT_GRAY;
    }

}
