package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.SessionRemarkModel;

/**
 * 会话标注
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-05-28 10:27
 */
public interface SessionRemarkRepository {

    /**
     * 标注
     *
     * @param sessionRemarkModel 请求
     */
    void remark(SessionRemarkModel sessionRemarkModel);

    /**
     * 根据会话查询会话标注
     *
     * @param sessionId 会话ID
     * @return
     */
    SessionRemarkModel findBySessionId(Long sessionId);
}
