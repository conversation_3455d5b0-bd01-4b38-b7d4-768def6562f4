package com.sankuai.wmbdaiassistant.domain.model;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc 片段聚合模型
 * <AUTHOR>
 * @Date 2025/3/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FragmentAggModel {

    public FragmentAggModel(String fieldName) {
        this.fieldName = fieldName;
        this.counts = new HashMap<>();
        this.subAggregations = new HashMap<>();
    }

    /**
     * 聚合字段名称
     */
    private String fieldName;

    /**
     * 当前层的计数
     */
    private Map<String,Long> counts =new HashMap<>();

    /**
     * 子聚合列表
     */
    private Map<String, FragmentAggModel> subAggregations;

}
