package com.sankuai.wmbdaiassistant.domain.service.chat.content;

import com.sankuai.wmbdaiassistant.domain.service.chat.content.element.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 内容元素类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:05
 */
@Getter
@AllArgsConstructor
public enum ContentElementTypeEnum {

    TEXT("text", Text.class),
    STYLED_TEXT("styledText", StyledText.class),
    LINK("link", Link.class),
    VIDEO("video", Video.class),
    IMAGE("image", Image.class),
    OPTIONS("options", OptionList.class),
    SEPARATOR("separator", Separator.class),
    BUTTON_GROUP("buttons", ButtonGroup.class),
    SUFFIX_OPTIONS("suffixOptions", SuffixOptions.class),
    MARKDOWN("markdown", Markdown.class),
    CARD_WITH_AVATAR("cardWithAvatar", CardWithAvatar.class),
    SELECTOR("selector", Selector.class),
    SELECTOR_ITEM("selectorItem", SelectorItem.class),
    TABLE("table", Table.class),
    NEW_POI_WEIGHT_CARD("newPoiWeightingCard", NewPoiWeightCard.class),
    FORM("form", Form.class),
    CONFIG("config", Config.class),
    ADDITION("addition", Addition.class),
    COLLAPSIBLE_TEXT("collapsibleText", CollapsibleText.class),
    DESCRIPTIONS("descriptions", Descriptions.class),
    TITLE("title", Title.class),
    REFERENCE_DOC("referenceDoc", ReferenceDoc.class),
    THINK_CONTENT("thinkContent", ThinkContent.class),

    ;
    private String code;
    private Class<? extends ContentElement> clazz;

    public static ContentElementTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ContentElementTypeEnum contentTypeEnum : values()) {
            if (contentTypeEnum.getCode().equals(code)) {
                return contentTypeEnum;
            }
        }
        return null;
    }

}
