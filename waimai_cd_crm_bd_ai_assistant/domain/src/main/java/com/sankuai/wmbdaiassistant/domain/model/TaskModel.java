package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.*;

import java.util.Date;

/**
 * 任务流
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-19 19:45
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TaskModel {
    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 大模型的 prompt
     */
    private String prompt;

    /**
     * 使用的大模型
     */
    private String model;

    /**
     * 关联的资源，json格式。
     */
    private String resources;

    /**
     * 字段: config
     */
    private TaskConfigModel config;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 是否有效
     */
    private ValidEnum valid;

    public TaskConfigModel getConfig() {
        if (config == null) {
            config = TaskConfigModel.builder().build();
        }
        return config;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskConfigModel {

        /**
         * 是否使用历史会话
         */
        @Builder.Default
        private boolean useHistory = true;

        private String role;
    }
}
