package com.sankuai.wmbdaiassistant.domain.service.chat.output;

import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;

/**
 * <AUTHOR>
 * @description 根据output构造输出
 * @create 2024/4/23 11:33
 */
public interface OutputModelProcessor {
    /**
     * 是否匹配
     *
     * @param outputModel
     * @return
     */
    boolean isMatch(OutputModel outputModel);

    /**
     * 根据output构造相应的回答
     *
     * @param outputModel
     * @param answer
     * @param params
     */
    void processOutputToAnswer(OutputModel outputModel, GeneralAnswerBo answer, String params);

    /**
     * 根据output获取内容
     *
     * @param outputModel
     * @param params
     * @return
     */
    String getContentByOutput(OutputModel outputModel, String params);
}
