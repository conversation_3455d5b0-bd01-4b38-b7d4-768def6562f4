package com.sankuai.wmbdaiassistant.domain.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 输出类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-05 14:33
 */
@Getter
public enum OutputTypeEnum {

    TEXT("text", "文本"),
    TEMPLATE("template", "模板"),
    OPTION("option", "选项列表"),
    COMPOSITE("composite", "复合"),
    ;

    private String code;
    private String desc;

    OutputTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OutputTypeEnum getByCode(String code) {
        for (OutputTypeEnum outputTypeEnum : values()) {
            if (StringUtils.equals(outputTypeEnum.getCode(), code)) {
                return outputTypeEnum;
            }
        }
        throw new IllegalArgumentException(String.format("OutputTypeEnum 不识别 code %s", code));
    }
}
