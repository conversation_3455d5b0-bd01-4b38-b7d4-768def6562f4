package com.sankuai.wmbdaiassistant.domain.enums.km;

import lombok.Getter;

/**
 * @description:
 * @author: fengxin21
 * @create: 2025/4/10
 **/
@Getter
public enum KmPermissionTypeEnum {

    /**
     * 仅浏览权限
     */
    BROWSE(0, "仅浏览"),

    /**
     * 编辑权限
     */
    EDIT(1, "编辑"),

    /**
     * 添加权限
     */
    ADD(2, "添加"),

    /**
     * 删除权限
     */
    DELETE(3, "删除"),

    /**
     * 管理权限
     */
    MANAGE(4, "管理"),

    /**
     * 评论权限
     */
    COMMENT(5, "评论");

    private int code;
    private String desc;

    KmPermissionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
