package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.OperationTypeEnum;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 结尾的悬浮列表
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuffixOptions implements ContentElement {
    private final String type = ContentElementTypeEnum.SUFFIX_OPTIONS.getCode();

    @JsonProperty("insert")
    private SuffixOptionsData data;

    @Override
    public String toMarkdownText() {
        return data == null ? "" : (data.getDataDesc() == null ? ""
                : JsonUtil.toJson(data.getDataDesc().getOptions()));
    }

    public static SuffixOptions build(String prefix, List<String> questionList) {
        return SuffixOptions.builder().data(SuffixOptionsData.builder().dataDesc(SuffixOptionDataDesc.builder()
                .descriptions(prefix)
                .options(DefaultUtil.defaultList(questionList).stream()
                        .map(question -> OptionItem.builder()
                                .operationType(OperationTypeEnum.CONTINUE_QUERY.getCode())
                                .abilityType(AbilityTypeEnum.GENERAL.getCode())
                                .content(question)
                                .build())
                        .collect(Collectors.toList()))
                .build()).build()).build();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SuffixOptionsData {
        @JsonProperty("suffixOptions")
        SuffixOptionDataDesc dataDesc;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SuffixOptionDataDesc {
        private String descriptions;
        private List<OptionItem> options;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionItem {
        private Integer abilityType;
        private Integer subAbilityType;
        private Integer operationType;
        private String content;
        private String url;

        @Override
        public String toString() {
            return String.format("%s;", content);
        }
    }
}


