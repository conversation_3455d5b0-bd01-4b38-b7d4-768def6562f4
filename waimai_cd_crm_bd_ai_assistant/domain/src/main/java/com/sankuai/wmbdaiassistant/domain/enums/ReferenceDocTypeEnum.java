package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 参考文档类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-05-28 10:00
 */
@Getter
public enum ReferenceDocTypeEnum {
    KM("km", "学城"),
    ;

    private String code;
    private String remark;

    ReferenceDocTypeEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public static ReferenceDocTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ReferenceDocTypeEnum value : ReferenceDocTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
