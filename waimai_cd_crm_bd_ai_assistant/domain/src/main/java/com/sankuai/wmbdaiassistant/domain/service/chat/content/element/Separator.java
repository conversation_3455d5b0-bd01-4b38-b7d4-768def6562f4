package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 换行符
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Separator implements ContentElement {
    private final String type = ContentElementTypeEnum.SEPARATOR.getCode();
    @JsonProperty("insert")
    private SeparatorInfo info;

    @Override
    public String toMarkdownText() {
        return "\n";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeparatorInfo {
        @JsonProperty("separator")
        private SeparatorDetail detail;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeparatorDetail {
        private String type;
    }
}
