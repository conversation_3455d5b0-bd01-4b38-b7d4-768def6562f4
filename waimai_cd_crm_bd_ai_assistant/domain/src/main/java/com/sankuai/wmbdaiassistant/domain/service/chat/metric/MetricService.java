package com.sankuai.wmbdaiassistant.domain.service.chat.metric;

import com.sankuai.wmbdaiassistant.common.event.ObservationEventEnum;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;

import java.util.concurrent.Callable;

/**
 * 记录服务（例如：打点、上报异常，raptor）
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-08 19:24
 */
public interface MetricService {

    /**
     * 记录耗时
     *
     * @param type      一级分类
     * @param subType   二级分类
     * @param costMills 耗时（单位毫秒）
     */
    void recordCost(String type, String subType, Long costMills);

    /**
     * 记录耗时
     *
     * @param type     一级分类
     * @param subType  二级分类
     * @param callable 执行逻辑
     */
    <T> T recordCost(String type, String subType, Callable<T> callable);

    /**
     * 记录耗时
     *
     * @param type     一级分类
     * @param subType  二级分类
     * @param runnable 执行逻辑
     */
    <T> void recordCost(String type, String subType, Runnable runnable);

    /**
     * 上报异常
     *
     * @param type 分类
     * @param data 异常数据
     */
    void reportError(String type, String data);

    /**
     * 上报异常
     *
     * @param bizErrorEnum 异常类型
     * @param data 异常数据
     */
    void reportError(BizErrorEnum bizErrorEnum, String data);

    /**
     * 上报事件
     * 
     * @param type 事件类型
     * @param name 事件名称
     */
    void reportEvent(String type, String name);

    /**
     * 上报业务事件
     * 
     * @param event 业务事件
     */
    void reportEvent(ObservationEventEnum event);
}
