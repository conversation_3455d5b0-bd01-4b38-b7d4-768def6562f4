package com.sankuai.wmbdaiassistant.domain.bo;

import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
@Setter
@Getter
public class ChatSubmitQueryBo {

    private SessionBo sessionBo;

    private Integer bizId;
    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 子能力类型(文本输入可以不填)
     */
    private Integer subAbilityType;

    private Long msgId;

    /**
     * 问题文本内容
     */
    private String content;

    /** 提问类型 */
    private String entryPointType;

    /**
     * 版本号，用于标识客户端的版本信息
     */
    private String version;
}
