package com.sankuai.wmbdaiassistant.domain.enums;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
public enum AbilityTypeEnum {
    GENERAL(1, "标准问"),
    JUMP(2, "快速跳转"),
    SERVICE_SCORE(3, "服务分"),
    GUIDANCE(4,"提问引导"),
    CATEGORY(5,"目录引导"),
    ;

    private int code;
    private String desc;

    AbilityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AbilityTypeEnum findByCode(int code) {
        for (AbilityTypeEnum type : AbilityTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
