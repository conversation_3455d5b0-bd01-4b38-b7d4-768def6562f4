package com.sankuai.wmbdaiassistant.domain.constant;

/**
 * 类描述？
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/27 上午9:48
 */
public class WikiConstant {
    // Node types
    public static final String NODE_TYPE_DOC = "doc";
    public static final String NODE_TYPE_HTML = "html";
    public static final String NODE_TYPE_TEXT = "text";
    public static final String NODE_TYPE_HEADING = "heading";
    public static final String NODE_TYPE_TASK_ITEM = "task_item";
    public static final String NODE_TYPE_TABLE = "table";
    public static final String NODE_TYPE_TABLE_CELL = "table_cell";
    public static final String NODE_TYPE_TABLE_HEADER = "table_header";
    public static final String NODE_TYPE_IMAGE = "image";
    public static final String NODE_TYPE_DRAWIO = "drawio";
    public static final String NODE_TYPE_PLANTUML = "plantuml";
    public static final String NODE_TYPE_MINDER = "minder";
    public static final String NODE_TYPE_PARAGRAPH = "paragraph";

    // Node attributes
    public static final String ATTR_NODE_ID = "nodeId";
    public static final String ATTR_COLSPAN = "colspan";
    public static final String ATTR_ROWSPAN = "rowspan";
    public static final String ATTR_LEVEL = "level";


    // other
    public static final String TEXT_FORMAT_STRIKETHROUGH = "strikethrough";
    public static final String EMPTY_STRING = "";
    public static final String SPACING = " ";
    public static final String Q = "Q";
    public static final String A = "A";
    public static final String TABLE_QA_PREFIX = Q + SPACING + A;
}
