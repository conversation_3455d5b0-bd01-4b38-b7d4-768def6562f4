package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 带样式文本
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StyledText implements ContentElement {
    private final String type = ContentElementTypeEnum.STYLED_TEXT.getCode();

    private StyledTextAttribute attributes;

    @JsonProperty("insert")
    private String content;

    @Override
    public String toMarkdownText() {
        return content;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StyledTextAttribute {
        private Boolean bold;
        private String color;
    }

    @Override
    public Map<String, String> toTtTransferContent() {
        Map<String, String> map = new HashMap<>();
        map.put("type", "text");
        map.put("text", content);
        return map;
    }
}
