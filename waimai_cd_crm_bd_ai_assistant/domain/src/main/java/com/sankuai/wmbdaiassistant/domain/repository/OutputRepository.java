package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.OutputModel;

import java.util.Collection;
import java.util.List;

/**
 * 输出仓储
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-05 16:02
 */
public interface OutputRepository {

    /**
     * 添加
     *
     * @param outputModel 输出
     * @return 返回主键ID
     */
    Long insert(OutputModel outputModel);

    /**
     * 批量添加
     * @param outputModelList 输出模型列表
     * @return ID List
     */
    List<Long> batchInsert(List<OutputModel> outputModelList);

    /**
     * 更新
     *
     * @param outputModel 输出
     */
    void update(OutputModel outputModel);

    /**
     * 根据ID查询
     *
     * @param outputId 输出ID
     * @return ID
     */
    OutputModel findById(Long outputId);

    /**
     * 批量根据ID查询
     * @param outputIds 输出ID列表
     * @return 模型List
     */
    List<OutputModel> findByIds(Collection<Long> outputIds);

}
