package com.sankuai.wmbdaiassistant.domain.service.feedback;

import com.sankuai.wmbdaiassistant.domain.bo.UserBo;

/**
 * 反馈服务
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface FeedBackService {

    /**
     * 反馈
     *
     * @param user              用户
     * @param type              类型
     * @param chatRecordId      针对哪个消息
     * @param suggestionContent 建议
     */
    void feedback(UserBo user, Integer type, Long chatRecordId, String suggestionContent);
}
