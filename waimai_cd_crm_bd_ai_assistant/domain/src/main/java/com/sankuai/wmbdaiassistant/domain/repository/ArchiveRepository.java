package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.ArchiveModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.ArchiveQuery;
import java.util.List;

/**
 * @Desc 归档仓储层
 * <AUTHOR>
 * @Date 2025/2/14
 **/
public interface ArchiveRepository {

    boolean insert(ArchiveModel archiveModel);

    /**
     * 根据ID查询目录
     *
     * @param id
     * @return
     */
    ArchiveModel findById(Long id);

    /**
     * 更新目录
     *
     * @param ArchiveModel
     * @return
     */
    boolean update(ArchiveModel ArchiveModel);

    /**
     * 根据条件查询
     *
     * @param query 查询条件
     * @return
     */
    List<ArchiveModel> findByQuery(ArchiveQuery query);

    /**
     * 根据条件统计
     *
     * @param query 查询条件
     * @return
     */
    long countByQuery(ArchiveQuery query);

    List<ArchiveModel> findRootByBizLine(String bizLine);

    /**
     * 批量删除归档
     *
     * @param code 业务线
     */
    void deleteByBizLine(String code);

    /**
     * 批量插入
     *
     * @param archiveModels
     * @return
     */
    boolean batchInsert(List<ArchiveModel> archiveModels);
}
