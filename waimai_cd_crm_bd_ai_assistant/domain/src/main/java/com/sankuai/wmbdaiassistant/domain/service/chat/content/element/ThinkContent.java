package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 思考内容元素
 * 协议:
 * interface ThinkContent { //
 * 所有ThinkContent合并展示，status以最后一个ThinkContent的status作为思考是否结束的标志
 * type: 'thinkContent';
 * insert: {
 * thinkContent: {
 * status: 'thinking' | 'done';
 * content: string;
 * }
 * }
 * }
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThinkContent implements ContentElement {

    private final String type = ContentElementTypeEnum.THINK_CONTENT.getCode();

    @JsonProperty("insert")
    private InsertDTO insert;

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String toMarkdownText() {
        return "";
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void merge(ContentElement other) {
        if (!(other instanceof ThinkContent)) {
            return; // 类型不匹配，不合并
        }
        ThinkContent otherThink = (ThinkContent) other;
        if (this.insert == null || this.insert.getThinkContent() == null) {
            this.insert = otherThink.getInsert(); // 如果当前为空，直接使用对方的
            return;
        }
        if (otherThink.getInsert() == null || otherThink.getInsert().getThinkContent() == null) {
            return; // 对方为空，不合并
        }

        // 合并内容：将对方的content追加到当前content
        String currentContent = this.insert.getThinkContent().getContent() == null ? ""
                : this.insert.getThinkContent().getContent();
        String otherContent = otherThink.getInsert().getThinkContent().getContent() == null ? ""
                : otherThink.getInsert().getThinkContent().getContent();
        this.insert.getThinkContent().setContent(currentContent + otherContent);

        // status以最后一个ThinkContent的status为准
        this.insert.getThinkContent().setStatus(otherThink.getInsert().getThinkContent().getStatus());
    }

    // 嵌套类以匹配协议中的 insert.thinkContent 结构
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InsertDTO {
        @JsonProperty("thinkContent")
        private ThinkContentData thinkContent;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThinkContentData {
        private Status status;
        private String content;
    }

    public enum Status {
        @JsonProperty("thinking")
        THINKING,
        @JsonProperty("done")
        DONE
    }

    // Builder 模式的辅助方法
    public static ThinkContent build(String content, Status status) {
        return ThinkContent.builder()
                .insert(InsertDTO.builder()
                        .thinkContent(ThinkContentData.builder()
                                .content(content)
                                .status(status)
                                .build())
                        .build())
                .build();
    }
}
