package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 替换类型
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
@Getter
public enum SubstituteEnum {
	SUBSTITUTE((byte)1, "替换"),
	NOT_SUBSTITUTE((byte)0, "不替换"),
	;

	private Byte code;
	private String desc;

	SubstituteEnum(Byte code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static SubstituteEnum getByIntegerCode(Integer code) {
		if (code == null) {
			return null;
		}
		return getByCode(code.byteValue());
	}

	public static SubstituteEnum getByCode(Byte code) {
		for (SubstituteEnum substituteEnum : values()) {
			if (substituteEnum.getCode().equals(code)) {
				return substituteEnum;
			}
		}
		return null;
	}
}
