package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.SignRecordModel;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface SignRecordRepository {

    /**
     * 新增
     *
     * @param signRecord 签约记录
     * @return 是否成功
     */
    boolean insert(SignRecordModel signRecord);

    /**
     * 根据UID统计签约条数
     *
     * @param uid UID
     * @return 签约记录条数
     */
    long countByUid(Integer uid);
}
