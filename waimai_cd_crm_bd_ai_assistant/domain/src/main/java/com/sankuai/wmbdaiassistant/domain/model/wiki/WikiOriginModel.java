package com.sankuai.wmbdaiassistant.domain.model.wiki;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.constant.WikiConstant;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;
import org.apache.commons.collections4.CollectionUtils;


@Getter
@Setter
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class WikiOriginModel {

    @ToString.Exclude
    private WeakReference<WikiOriginModel> parentWeakRef;

    @Builder.Default
    private String type = "";
    @Builder.Default
    private Map<String, Object> attrs = new HashMap<>();
    @Builder.Default
    private List<WikiOriginModel> content = new ArrayList<>();
    @Builder.Default
    private String text = "";

    public static WikiOriginModel fromJson(String json) {
        WikiOriginModel model = JsonUtil.fromJson(json,WikiOriginModel.class);
        setParentRelationships(model);
        return model;
    }

    /**
     * 设置父节点关系
     *
     * @param node
     */
    private static void setParentRelationships(WikiOriginModel node) {
        if (node != null && CollectionUtils.isNotEmpty(node.getContent())) {
            for (WikiOriginModel child : node.getContent()) {
                child.setParentWeakRef(new WeakReference<>(node));
                setParentRelationships(child);
            }
        }
    }

    /**
     * 提取当前节点及其子节点中的所有文本内容
     *
     * @return 拼接后的文本内容
     */
    @JsonIgnore
    public String extractTextContent() {
        // 如果是文本类型节点，直接返回attrs中的text值
        if (WikiConstant.NODE_TYPE_TEXT.equals(this.type)) {
            return DefaultUtil.defaultString(this.text);
        }

        // 如果有子节点，递归处理所有子节点
        if (CollectionUtils.isNotEmpty(content)) {
            StringBuilder result = new StringBuilder();
            for (WikiOriginModel child : content) {
                if (result.length() > 0) {
                    result.append(WikiConstant.SPACING);
                }
                result.append(child.extractTextContent());
            }
            return result.toString();
        }

        return WikiConstant.EMPTY_STRING;
    }

    /**
     * 获取节点ID
     *
     * @return
     */
    @JsonIgnore
    public String findNodeId() {
        return DefaultUtil.defaultString(this.attrs.get(WikiConstant.ATTR_NODE_ID));
    }

    /**
     * 获取节点属性值
     *
     * @param key
     * @return
     */
    @JsonIgnore
    public Object findAttr(String key) {
        return attrs.get(key);
    }

    /**
     * 获取父节点
     * @return
     */
    @JsonIgnore
    public WikiOriginModel findParentNode() {
        return parentWeakRef.get();
    }

    /**
     * 获取前一个兄弟节点
     *
     * @return
     */
    @JsonIgnore
    public WikiOriginModel findPreBrotherNode() {
        WikiOriginModel parentNode = parentWeakRef.get();
        if (parentNode == null) {
            return null;
        }
        List<WikiOriginModel> children = parentNode.getContent();
        int index = children.indexOf(this);
        return index - 1 >= 0 ? children.get(index - 1) : null;
    }

    /**
     * 获取后一个兄弟节点
     *
     * @return
     */
    @JsonIgnore
    public WikiOriginModel findNextBrotherNode() {
        WikiOriginModel parentNode = parentWeakRef.get();
        if (parentNode == null) {
            return null;
        }
        List<WikiOriginModel> children = parentNode.getContent();
        int index = children.indexOf(this);
        return index + 1 < children.size() ? children.get(index + 1) : null;
    }

    /**
     * 判断节点类型是否包含目标类型
     *
     * @param targetTypeList
     * @return
     */
    @JsonIgnore
    public boolean containTargetType(List<String> targetTypeList) {
        return containTargetType(new HashSet<>(targetTypeList));
    }

    /**
     * 判断节点类型是否包含目标类型
     *
     * @param targetTypeSet
     * @return
     */
    @JsonIgnore
    public boolean containTargetType(Set<String> targetTypeSet) {
        if (CollectionUtils.isEmpty(targetTypeSet) || CollectionUtils.isEmpty(content)) {
            return false;
        }
        for (WikiOriginModel child : content) {
            if (targetTypeSet.contains(child.getType())) {
                return true;
            }
            if (child.containTargetType(targetTypeSet)) {
                return true;
            }
        }
        return false;
    }
}