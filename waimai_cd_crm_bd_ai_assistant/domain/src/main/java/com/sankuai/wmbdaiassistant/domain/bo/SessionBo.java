package com.sankuai.wmbdaiassistant.domain.bo;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.model.SessionModel;
import com.sankuai.wmbdaiassistant.domain.model.TaskModel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
@Setter
@Getter
public class SessionBo implements Serializable {

    /**
     * 用户id
     */
    private Integer uid;

    /**
     * 用户mis
     */
    private String mis;

    /**
     * sessionId
     */
    private Long sessionId;

    /**
     * 会话来源
     */
    private String source;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 上下文栈
     */
    private List<ChatContext> contextStack = new ArrayList<>();

    public static SessionBo from(int uid, String mis, long sessionId, String source, String extra) {
        SessionBo sessionBo = new SessionBo();
        sessionBo.setUid(uid);
        sessionBo.setMis(mis);
        sessionBo.setSessionId(sessionId);
        sessionBo.setSource(source);
        sessionBo.setExtra(extra);
        return sessionBo;
    }

    public static SessionBo from(SessionModel sessionModel) {
        if (sessionModel == null) {
            return null;
        }
        return from(sessionModel.getUid(), sessionModel.getMis(), sessionModel.getId(), sessionModel.getSource(), JsonUtil.toJson(sessionModel.getExtra()));
    }

    public String getTaskSessionId() {
        ChatContext chatContext = currentContext();
        return chatContext.getId();
    }

    public Long getTaskTriggerQuestionId() {
        return currentContext().getTriggerQuestionId();
    }

    public Long getCurrentQuestionId() {
        if(currentContext() == null) {
            return null;
        }
        if(currentContext().getCurrentQuestionId() == null) {
            currentContext().setCurrentQuestionId(getTaskTriggerQuestionId());
        }
        return currentContext().getCurrentQuestionId();
    }

    public Long fetchTaskId() {
        return currentContext().getTaskId();
    }

    public String fetchTaskModel() {
        return currentContext().getTaskModel();
    }

    public String fetchTaskPrompt() {
        return currentContext().getTaskPrompt();
    }

    public boolean fromScene() {
        ChatContext chatContext = currentContext();
        return chatContext.getTriggerQuestionId() == null || chatContext.getId() == null;
    }

    public String fetchTaskSessionId() {
        return currentContext().getId();
    }

    public Double fetchTaskTop1Score() {
        return currentContext().getTop1QuestionScore();
    }

    public Long fetchTaskTop1QuestionId() {
        return currentContext().getTop1QuestionId();
    }

    public boolean inTask() {
        ChatContext chatContext = currentContext();
        return DefaultUtil.defaultBoolean(chatContext.getStick());
    }

    public void resetTaskState() {
        if (CollectionUtils.isNotEmpty(contextStack)) {
            contextStack.remove(contextStack.size() - 1);
        }
    }

    public String fetchSource() {
        if (StringUtils.isBlank(source)) {
            return SessionSourceConfigManager.BD_AI_ASSISTANT_BEE_CODE;
        }
        return source;
    }

    // DSL获取sessionBo platform字段，勿动
    public String getPlatform() {
        String source = fetchSource();
        SessionSourceEntity sessionSourceEntity = SessionSourceConfigManager.getByCodeWithLionConfigValue(source);
        return sessionSourceEntity.getPlatformCode();
    }

    public void configDomainId(Long domainId) {
        if (domainId != null) {
            currentContext().setDomainId(domainId);
        }
    }

    public ChatContext currentContext() {
        if (CollectionUtils.isEmpty(contextStack)) {
            contextStack.add(new ChatContext());
        }
        return contextStack.get(contextStack.size() - 1);
    }

    public void configTop1(Long id, Double score) {
        ChatContext chatContext = currentContext();
        chatContext.setTop1QuestionId(id);
        chatContext.setTop1QuestionScore(score);
    }

    public void configFromTask(TaskModel taskModel) {
        configFromTask(taskModel, null);
    }

    public void configFromTask(TaskModel taskModel, Long questionId) {
        ParamCheckUtil.notNull(taskModel, "多轮为空");

        ChatContext chatContext = currentContext();
        chatContext.setTaskId(taskModel.getId());
        chatContext.setTaskModel(taskModel.getModel());
        chatContext.setTaskPrompt(taskModel.getPrompt());
        chatContext.setTriggerQuestionId(questionId);
        chatContext.setStick(Boolean.TRUE);
        chatContext.setUseHistory(taskModel.getConfig().isUseHistory());
        configId(questionId);
    }

    public void configIntentRecognitionQuestionId(Long phraseId) {
        ChatContext chatContext = currentContext();
        chatContext.setIntentRecognitionQuestionId(phraseId);
    }

    public void pushContext(TaskModel taskModel, Long questionId) {
        contextStack.add(new ChatContext());
        configFromTask(taskModel, questionId);
    }

    public void configId(Long questionId) {
        ChatContext chatContext = currentContext();
        Long taskId = chatContext.getTaskId();
        ParamCheckUtil.notNull(taskId, "多轮ID为空");
        if (questionId != null) {
            chatContext.setId(String.format("%d-%d", taskId, questionId));
            chatContext.setTriggerQuestionId(questionId);
        }
    }

    public void configFragments(List<FragmentModel> fragments) {
        if (CollectionUtils.isEmpty(fragments)) {
            return;
        }
        ChatContext chatContext = currentContext();
        chatContext.setFragmentIdList(fragments.stream().map(FragmentModel::getId).collect(Collectors.toList()));
    }

    public List<String> fetchFragmentIdList() {
        return currentContext().getFragmentIdList();
    }

    public void refresh() {
        refresh(null);
    }

    public void refresh(Long questionMsgId) {
        if (CollectionUtils.isNotEmpty(contextStack)
                && !DefaultUtil.defaultBoolean(contextStack.get(contextStack.size() - 1).getStick())) {
            contextStack.remove(contextStack.size() - 1);
        }
        if (CollectionUtils.isEmpty(contextStack)) {
            contextStack.add(new ChatContext());
        }
        if(questionMsgId != null){
            currentContext().setCurrentQuestionId(questionMsgId);
        }
    }

    public void forceRefresh(Long questionMsgId) {
        if (CollectionUtils.isNotEmpty(contextStack)) {
            contextStack.remove(contextStack.size() - 1);
        }
        if (CollectionUtils.isEmpty(contextStack)) {
            contextStack.add(new ChatContext());
        }
        if(questionMsgId != null){
            currentContext().setCurrentQuestionId(questionMsgId);
        }
    }

}
