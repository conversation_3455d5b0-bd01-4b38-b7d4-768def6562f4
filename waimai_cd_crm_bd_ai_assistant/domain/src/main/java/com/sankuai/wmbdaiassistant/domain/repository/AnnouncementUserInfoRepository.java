package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.AnnouncementUserInfoModel;

/**
 * 公告用户信息存储库
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
public interface AnnouncementUserInfoRepository {

    /**
     * 根据公告ID、用户ID和租户ID查询
     *
     * @param announcementId 公告ID
     * @param uid 用户ID
     * @param tenantId 租户ID
     * @return 公告用户信息模型
     */
    AnnouncementUserInfoModel findByAnnouncementIdAndUidAndTenantId(Long announcementId, Integer uid, Long tenantId);

    /**
     * 新增
     *
     * @param announcementUserInfoModel 公告用户信息模型
     * @return 是否成功
     */
    boolean insert(AnnouncementUserInfoModel announcementUserInfoModel);

    /**
     * 更新
     *
     * @param announcementUserInfoModel 公告用户信息模型
     * @return 是否成功
     */
    boolean update(AnnouncementUserInfoModel announcementUserInfoModel);
}