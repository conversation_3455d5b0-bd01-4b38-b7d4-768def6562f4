package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;

import java.util.List;

/**
 * 会话消息
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface ChatMsgRepository {

    /**
     * 新增会话消息
     *
     * @param chatMsgModel 会话消息
     * @return 是否成功
     */
    boolean insert(ChatMsgModel chatMsgModel);

    /**
     * 更新
     *
     * @param chatMsg 会话消息
     * @return 是否成功
     */
    boolean update(ChatMsgModel chatMsg);

    /**
     * 将 taskSessionId 和 context 置空
     *
     * @param id ID
     */
    void updateTaskSessionIdAndContextNull(Long id);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 会话消息
     */
    ChatMsgModel findById(Long id);

    /**
     * 根据UID统计用户会话消息条数
     *
     * @param uid 用户ID
     * @return
     */
    long countByUid(Integer uid);

    /**
     * 获取用户最近 n 分钟内的提问次数
     *
     * @param uid 用户ID
     * @param lastNMinutes 最近 n 分钟
     * @return 次数
     */
    Long getLastNMinutesUserInputTimes(Integer uid, Integer lastNMinutes);

    /**
     * 根据会话查询会话消息
     *
     * @param sessionId 会话ID
     * @return 会话消息列表
     */
    List<ChatMsgModel> findBySessionId(Long sessionId);

    /**
     * 获取多轮的会话列表
     *
     * @param sessionId     会话ID
     * @param taskSessionId 多轮会话ID
     * @return
     */
    List<ChatMsgModel> findByTaskSessionId(Long sessionId, String taskSessionId);

    /**
     * 查询用户可以看到的会话消息（在一个会话中，chat_msg 中可能包含一些系统生成的会话消息，这些会话消息不用对用户展示）
     *
     * @param sessionId 会话ID
     * @param uid       用户ID
     * @param msgId     最大 msgId
     * @param limit     查询的条数
     * @return 会话消息列表
     */
    List<ChatMsgModel> findUserVisibleMsgBySessionId(Long sessionId, Integer uid, Long msgId, Integer limit);

    /**
     * 分页查询会话信息
     *
     * @param sessionId 会话ID
     * @param offset    偏移
     * @param size      查询量
     * @return
     */
    List<ChatMsgModel> findBySessionPage(Long sessionId, Integer offset, Integer size);

    /**
     * 根据会话统计会话消息条数
     *
     * @param sessionId 会话ID
     * @return 消息条数
     */
    Long countBySession(Long sessionId);

    /**
     * 查找用户可见的消息
     * 
     * @param uid 用户ID（可选，为null无效）
     * @param sessionIds 会话ID列表（可选，为null无效）
     * @param msgId 第一条MsgId（含），or 截止MsgId(不含) （可选，为null无效）
     * @param limit 限制条数（必选）
     * @param isAsc 是否升序（必选）
     * @return
     */
    List<ChatMsgModel> findUserVisibleMsg(Integer uid, List<Long> sessionIds, Long msgId, int limit, boolean isAsc);

    /**
     * 统计会话内TT点击次数
     * 
     * @param sessionIds
     * @return
     */
    long countTTClick(List<Long> sessionIds);
}
