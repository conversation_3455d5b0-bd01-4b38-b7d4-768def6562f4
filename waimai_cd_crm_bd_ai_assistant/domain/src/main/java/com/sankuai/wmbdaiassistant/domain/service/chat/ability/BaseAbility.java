package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
public interface BaseAbility {

    AbilityTypeEnum getAbilityType();

    Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo);

    ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo);
}
