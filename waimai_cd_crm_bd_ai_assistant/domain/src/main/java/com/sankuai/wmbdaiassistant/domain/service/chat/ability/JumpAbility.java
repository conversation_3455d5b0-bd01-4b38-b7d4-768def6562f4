package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.MsgTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;
import com.sankuai.wmbdaiassistant.domain.repository.Page;
import com.sankuai.wmbdaiassistant.domain.service.chat.SubAbilityConfigService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentBuilder;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.impl.ChatRecordServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
@Component
public class JumpAbility implements BaseAbility {

    @Resource
    private SubAbilityConfigService subAbilityConfigService;
    @Resource
    private ChatRecordServiceImpl chatRecordService;
    @Resource
    private AbilityConfig abilityConfig;
    @Resource
    private ChatContentConverter chatContentConverter;

    @Override
    public AbilityTypeEnum getAbilityType() {
        return AbilityTypeEnum.JUMP;
    }

    @Override
    public Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo) {
        ChatMsgModel msg = chatRecordService.createQueryMsg(chatSubmitQueryBo);
        return msg.getId();
    }

    @Override
    public ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo) {

        Integer pageNum = requestBo.getPageNum();
        if (Objects.isNull(pageNum) || pageNum == BigDecimal.ZERO.intValue()) {
            pageNum = 1;
        }
        Page page = new Page();
        page.setPageSize(abilityConfig.selectionTypeAnswerCountPerPage);
        page.setPageNum(pageNum);
        List<SubAbilityConfigModel> configList = subAbilityConfigService.selectConfig(AbilityTypeEnum.JUMP, page);
        Long count = subAbilityConfigService.countPageConfig(AbilityTypeEnum.JUMP);

        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setMsgId(requestBo.getMsgId());
        chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        chatFetchAnswerBo.setAbilityType(requestBo.getAbilityType());
        chatFetchAnswerBo.setSubAbilityType(requestBo.getSubAbilityType());
        chatFetchAnswerBo.setHasNext(count > abilityConfig.selectionTypeAnswerCountPerPage);
        //前端透传返回的pageNum，因此此处返回pageNum+1
        chatFetchAnswerBo.setPageNum(pageNum + 1);

        chatFetchAnswerBo.setMsgType(MsgTypeEnum.SELECTION.getCode());

        String content = new ChatContentBuilder()
                .add(chatContentConverter.buildText(abilityConfig.jumpAbilityAnswerPrefixTextContent))
                .add(chatContentConverter.buildOptionsBySubAbilityConfig(configList))
                .build();
        chatFetchAnswerBo.setCurrentContent(content);

        return chatFetchAnswerBo;
    }
}
