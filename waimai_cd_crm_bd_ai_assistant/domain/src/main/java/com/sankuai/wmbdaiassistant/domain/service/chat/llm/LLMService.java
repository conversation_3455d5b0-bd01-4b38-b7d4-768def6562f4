package com.sankuai.wmbdaiassistant.domain.service.chat.llm;

import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.model.LLMTypeModel;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 大模型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-19 19:21
 */
public interface LLMService {

    /**
     * 段式聊天
     *
     * @param model   模型
     * @param prompt  提示语
     * @param history 历史会话
     * @param message 当前消息
     * @return 大模型的返回消息
     */
    String chat(LLMTypeModel model, String prompt, List<ChatRecordBo> history, String message);

    /**
     * 流式聊天
     *
     * @param model     模型
     * @param prompt    提示语
     * @param history   历史会话
     * @param message   给模型提出的问题
     * @param onError   当返回异常时，异常的处理器
     * @param onMessage 当返回收到了消息后的处理器
     * @return 返回当前聊天所使用的大模型
     */
    String chatWithStream(LLMTypeModel model, String prompt, List<ChatRecordBo> history, String message
            , Consumer<Throwable> onError, BiConsumer<String, Boolean> onMessage);

    /**
     * 函数调用
     *
     * @param model     模型
     * @param prompt    提示语
     * @param history   历史会话
     * @param functions 函数
     * @return 模型返回
     */
    String functionCall(LLMTypeModel model, String prompt, List<ChatRecordBo> history, List<FunctionCallParam> functions);

}
