package com.sankuai.wmbdaiassistant.domain.service.chat;

import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;

/**
 * <AUTHOR>
 * @description outputModel服务
 * @create 2024/4/28 19:28
 */
public interface OutputService {
    /**
     * 根据output构造相应的回答
     *
     * @param outputModel
     * @param answer
     * @param params
     */
    void processOutputToAnswer(OutputModel outputModel, GeneralAnswerBo answer, String params);

    /**
     * 根据output获取内容
     *
     * @param outputModel
     * @param params
     * @return
     */
    String getContentByOutput(OutputModel outputModel, String params);
}
