package com.sankuai.wmbdaiassistant.domain.bo;

import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/13
 **/
@Setter
@Getter
@ToString
public class GeneralAnswerBo implements Serializable {

    private Long msgId;

    /**
     * 回答状态 枚举见com.sankuai.wmbdaiassistant.server.common.ChatAnswerStatusEnum
     */
    private Integer status;

    /**
     * 回答类型：1.FAQ，2 3 4 5 AI类回答
     */
    private Integer answerType;

    /**
     * AI回答内容
     */
    private String answer;

    /**
     * 前置内容，FAQ回答
     */
    @Deprecated
    private String prefixTextContent;

    /**
     * 后置内容，FAQ回答
     */
    @Deprecated
    private String postTextContent;
    /**
     * 选项，FAQ回答
     */
    @Deprecated
    private List<String> selections;

    /**
     * 关联问top1或标准问识别分数
     */
    private Double top1FQScore;
    /**
     * 关联问top1或标准问题ID
     */
    private Long top1QuestionId;

    /**
     * 文本末尾展示的图片
     */
    @Deprecated
    private List<String> picUrlList;

    /**
     * topK
     */
    private List<VectorBo> topK;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 替换关键词还原 Map<替换后值,原来值>
     */
    private Map<String, String> outputVariableMap;
}
