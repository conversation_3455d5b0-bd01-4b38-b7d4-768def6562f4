package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 带头像的卡片
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardWithAvatar implements ContentElement {
    private final String type = ContentElementTypeEnum.CARD_WITH_AVATAR.getCode();

    @JsonProperty("insert")
    private CardWithAvatarDesc desc;

    @Override
    public String toMarkdownText() {
        if (desc == null || desc.getDetail() == null) {
            return null;
        }
        CardWithAvatarDetail detail = desc.getDetail();
        StringBuilder stringBuilder = new StringBuilder();
        String prefix = getPrefix(detail.getType());
        stringBuilder.append(String.format("%s头图链接：%s\n", prefix, detail.getAvatar()));
        stringBuilder.append(String.format("%s名称：%s\n", prefix, detail.getTitle()));
        DefaultUtil.defaultList(detail.getContents()).forEach(content -> {
            stringBuilder.append(String.format("%s%s：%s\n", prefix, content.getLabel(), content.getValue()));
        });
        return stringBuilder.toString();
    }

    private String getPrefix(String type) {
        if ("poi".equals(type)) {
            return "商家";
        }
        return "";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CardWithAvatarDesc {
        @JsonProperty("cardWithAvatar")
        private CardWithAvatarDetail detail;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CardWithAvatarDetail {
        /**
         * 类型
         */
        private String type;

        /**
         * 头像链接
         */
        private String avatar;

        /**
         * 标题
         */
        private String title;

        /**
         * 内容
         */
        @JsonProperty("content")
        private List<CardWithAvatarContent> contents;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CardWithAvatarContent {
        /**
         * 标题
         */
        private String label;
        /**
         * 值
         */
        private String value;
        /**
         * 表示是否占满一行，默认一行展示两个数据
         */
        private Boolean block;
    }
}
