package com.sankuai.wmbdaiassistant.domain.factory;

import com.sankuai.wmbdaiassistant.domain.enums.FaqImportStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.SubstituteEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import com.sankuai.wmbdaiassistant.domain.model.FaqImportExcelRowModel;
import com.sankuai.wmbdaiassistant.domain.model.FaqImportItemModel;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;

/**
 * input description here
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
public class FaqImportItemModelFactory {
    public static FaqImportItemModel create(Long domainId, String question, String answer, String ttUrl, Long processId,
            Long similarPhraseId, SubstituteEnum substitute, ValidEnum valid, FaqImportStatusEnum status) {
        FaqImportItemModel model = new FaqImportItemModel();
        model.setDomainId(domainId);
        model.setProcessId(processId);
        model.setQuestion(question);
        model.setAnswer(answer);
        model.setTtUrl(ttUrl);
        model.setSimilarPhraseId(similarPhraseId);
        model.setSubstitute(substitute);
        model.setValid(valid);
        model.setStatus(status);
        model.setCreateTime(new Date());
        model.setModifyTime(new Date());
        return model;
    }

    public static FaqImportItemModel create(FaqImportExcelRowModel rowModel, Map<String, Long> domain2IdMap) {
        if (rowModel == null || domain2IdMap == null) {
            return null;
        }
        return create(domain2IdMap.get(rowModel.getDomain()), rowModel.getQuestion(), rowModel.getAnswer(), null, null,
                null, null, ValidEnum.VALID, FaqImportStatusEnum.INIT);
    }

    public static List<FaqImportItemModel> create(List<FaqImportExcelRowModel> modelList,
            Map<String, Long> domain2IdMap) {
        if (CollectionUtils.isEmpty(modelList)) {
            return Collections.emptyList();
        }
        return modelList.stream().map(model -> create(model, domain2IdMap)).collect(Collectors.toList());
    }

}
