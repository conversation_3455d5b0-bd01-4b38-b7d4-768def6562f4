package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.enums.FragmentStateEnum;
import com.sankuai.wmbdaiassistant.domain.model.FragmentAggModel;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentAggregation;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentQuery;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 分片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 16:56
 */
public interface FragmentRepository {

    /**
     * 根据ID查询
     * @param id
     */
    FragmentModel findById(String id);

    /**
     * 通过 idList 批量查询分片数据
     *
     * @param idList 分片 ID 列表
     * @return 分片数据列表
     */
    List<FragmentModel> findByIdList(List<String> idList);

    /**
     * 根据条件查询
     *
     * @param query 查询条件
     * @return
     */
    List<FragmentModel> findByQuery(FragmentQuery query);

    /**
     * 查询所有
     *
     * @param query
     * @return
     */
    List<FragmentModel> findAll(FragmentQuery query);

    /**
     * 基于wiki的批次和wikiId信息获取关联分片集合
     *
     * @param batchId 批次ID
     * @param wikiId  wiki id
     * @return
     */
    List<FragmentModel> findByWiki(String batchId, Long wikiId);

    /**
     * 基于wiki的批次和wikiId信息获取关联分片集合
     *
     * @param batchId   批次ID
     * @param wikiId    wiki id
     * @param stateList 状态列表
     * @return
     */
    List<FragmentModel> findByWiki(String batchId, Long wikiId, List<FragmentStateEnum> stateList);

    /**
     * 获取知识库片段数量
     *
     * @param datasetId 知识库ID
     * @return
     */
    Long count(Long datasetId);

    /**
     * 获取批次下分片数量
     * @param batchId
     * @param wikiId
     * @return
     */
    Long countByWiki(String batchId,Long wikiId);

    /**
     * 根据条件查询数量
     *
     * @param query 查询条件
     * @return
     */
    Long countByQuery(FragmentQuery query);

    /**
     * 获取批次下分片数量
     * @param batchId
     * @return
     */
    Long countByBatchId(String batchId);

    /**
     * 获取批次下指定状态分片数量
     * @param batchId
     * @param fragmentStateEnums
     * @return
     */
    Long countByBatchId(String batchId,List<FragmentStateEnum> fragmentStateEnums);

    /**
     * 插入或更新
     *
     * @param fragmentModelList
     * @return
     */
    boolean batchInsertOrUpdate(List<FragmentModel> fragmentModelList);

    /**
     * 滚动查询
     *
     * @param query
     * @param consumer
     */
    void scrollQueryForMap(FragmentQuery query, Consumer<List<Map<String,String>>> consumer);

    /**
     * 滚动查询
     *
     * @param query
     * @param consumer
     */
    void scrollQuery(FragmentQuery query, Consumer<List<FragmentModel>> consumer);

    FragmentAggModel  aggregationByQuery(FragmentQuery query, FragmentAggregation fragmentAggregation);
}
