package com.sankuai.wmbdaiassistant.domain.service.chat.rag;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;

/**
 * 大模型RAG
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-01 17:42
 */
public interface LlmRag {

    /**
     * 提问
     *
     * @param sessionBo  会话上下文
     * @param id         用户问题ID
     * @param input      问题
     * @param entryPoint 入口点
     * @param callback   回调
     */
    void query(SessionBo sessionBo, Long id, String input, String entryPoint, GeneralCallback callback);

}
