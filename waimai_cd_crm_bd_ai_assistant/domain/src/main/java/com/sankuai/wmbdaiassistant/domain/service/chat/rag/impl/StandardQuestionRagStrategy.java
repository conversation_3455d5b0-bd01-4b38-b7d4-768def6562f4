package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.*;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.common.exception.BizException;
import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.enums.*;
import com.sankuai.wmbdaiassistant.domain.model.*;
import com.sankuai.wmbdaiassistant.domain.repository.*;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatSessionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.OutputService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.AbilityConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.DomainRecognitionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.IntentRecognitionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentCompressor;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.llm.LLMService;
import com.sankuai.wmbdaiassistant.domain.service.chat.pattern.PatternFactory;
import com.sankuai.wmbdaiassistant.domain.service.chat.pattern.PatternParam;
import com.sankuai.wmbdaiassistant.domain.service.chat.pattern.TaskBreakPatternProcessor;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.RagStrategy;
import com.sankuai.wmbdaiassistant.domain.service.chat.rerank.RearrangeService;
import com.sankuai.wmbdaiassistant.domain.service.chat.user.WmUserService;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.PhraseVectorQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorDatabaseService;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import com.sankuai.wmbdaiassistant.domain.service.gray.GrayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 基于标准问的RAG策略
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-28 10:44
 */
@Slf4j
@Component
public class StandardQuestionRagStrategy implements RagStrategy {

    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private AbilityConfig abilityConfig;

    @Resource
    private VectorDatabaseService vectorDatabaseService;

    @Resource
    private PhraseRepository phraseRepository;

    @Resource
    private OutputRepository outputRepository;

    @Resource
    private FaqRepository faqRepository;

    @Resource
    private TaskRepository taskRepository;

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @Resource
    private LLMService llmService;

    @Resource
    private PatternFactory patternFactory;

    @Resource
    private ChatRecordService chatRecordService;

    @Resource
    private ChatSessionService chatSessionService;

    @Resource
    private OutputService outputService;

    @Resource
    private WmUserService wmUserService;

    @Resource
    private RearrangeService rearrangeService;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private DomainRecognitionService domainRecognitionService;

    @Resource
    private DomainRepository domainRepository;

    @Resource
    private TaskBreakPatternProcessor taskBreakPatternProcessor;

    @Resource
    private TraceLogService traceLogService;

    @Resource
    private IntentRecognitionService intentRecognitionService;

    @MdpConfig("chat.trigger.lessK2.model:chatglm3")
    private String chatTriggerLessK2Model;

    @Resource
    private GrayService grayService;


    @Override
    public void query(SessionBo sessionBo, Integer uid, Long bizId, Long id
        , String input, String entryPoint, GeneralCallback callback, String version) {
        try {
            // 增加触发日志
            ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildGeneralChatTriggerTraceLog(
                sessionBo, id, input, entryPoint)));
            trigger(sessionBo, uid, bizId, id, input, entryPoint, callback, version);
            updateInputMsgContext(id, sessionBo);
            chatSessionService.updateSessionBo(sessionBo, null);
        } catch (Exception e) {
            log.error("ChatServiceImpl chat error, msg = {}", e.getMessage(), e);
            GeneralAnswerBo answer = new GeneralAnswerBo();
            answer.setMsgId(id);
            answer.setAnswer(aiChatConfig.aiChatDefaultMsg);
            answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
            answer.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());
            callback.answerCallback(answer);
        }
    }

    private void trigger(SessionBo sessionBo, Integer uid, long bizId, long id, String input, String entryPoint,
        GeneralCallback callback, String version) {

        sessionBo.refresh(id);
        log.info("ChatServiceImpl trigger，uid = {}, bizId = {}, input = {}, session = {}"
            , uid, bizId, input, JsonUtil.toJson(sessionBo));

        // 当前已经处于多轮中
        if (sessionBo.inTask()) {
            triggerTask(sessionBo, uid, bizId, id, input, entryPoint, callback, version);
            return;
        }

        // 域识别
        Long domainId = domainRecognitionService.recognizeDomain(input);
        ExecutorUtil.safeExecute(() -> traceLogService
            .insert(TraceLogModel.buildDomainRecognizeTraceLog(sessionBo, id, entryPoint, input, domainId)));

        if (domainId != null) {
            sessionBo.configDomainId(domainId);
        }

        // 向量检索
        PhraseVectorQuery query = PhraseVectorQuery.builder().phrase(input).filterByDomain(Boolean.TRUE)
            .topN(aiChatConfig.aiChatFetchSize)
            .domainPathIdList(domainId == null ? null : Collections.singletonList(domainId)).build();
        List<VectorBo> phraseVectorList = vectorDatabaseService.searchPhrase(query);
        if (CollectionUtils.isNotEmpty(phraseVectorList)) {
            sessionBo.configTop1(phraseVectorList.get(0).getId(), phraseVectorList.get(0).getScore());
        }
        ExecutorUtil.safeExecute(() -> traceLogService.insert(
            TraceLogModel.buildPhraseVectorQueryTraceLog(sessionBo, id, entryPoint, query, phraseVectorList)));

        // case1: 精确匹配
        List<VectorBo> faqList = DefaultUtil.defaultList(phraseVectorList).stream()
            .filter(faqVectorBo -> faqVectorBo.getScore() >= aiChatConfig.k1).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(faqList)) {
            greaterAndEqualK1(sessionBo, id, bizId, input, faqList.get(0), entryPoint, callback, version);
            return;
        }

        // case : 小于 k1 的场景，现在有三种场景
        // 场景一：域多轮
        if (domainId != null) {
            DomainModel domainModel = domainRepository.findById(domainId);
            TaskModel taskModel = taskRepository.findById(domainModel.getTaskId());
            sessionBo.configFromTask(taskModel, id);
            triggerTask(sessionBo, uid, bizId, id, input, entryPoint, callback, version);
            return;
        }

        // 场景二：意图识别
        Long phraseId = intentRecognitionService.recognizeIntention(input);
        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildIntentionRecognizeTraceLog(sessionBo,
            id, entryPoint, input, Collections.singletonList(phraseId))));
        if (phraseId != null) {
            triggerIntention(sessionBo, uid, bizId, id, input, entryPoint, phraseId, callback, version);
            return;
        }

        // 场景三：新交互模型（大模型重组）
        ChatContentCompressor compressor = new ChatContentCompressor();
        List<PhraseModel> phraseModelList = phraseRepository.findByIdList(DefaultUtil.defaultList(phraseVectorList)
            .stream().map(VectorBo::getId).collect(Collectors.toList()));
        List<String> queryList = DefaultUtil.defaultList(phraseModelList).stream()
            .map(PhraseModel::getPhrase).collect(Collectors.toList());
        List<Long> phraseIdList;
        if (DefaultUtil.defaultBoolean(aiChatConfig.answerModelMockRearrange)) {
            phraseIdList = DefaultUtil.defaultList(phraseModelList).stream()
                .map(PhraseModel::getId).collect(Collectors.toList());
        } else {
            List<Long> rearrangedPhraseIds = rearrangeService.rearrange(input, queryList,
                aiChatConfig.answerModelRerankTopN);
            ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildRearrangeTraceLog(sessionBo,
                id, entryPoint, input, queryList, aiChatConfig.answerModelRerankTopN, rearrangedPhraseIds)));
            phraseIdList = rearrangedPhraseIds;
        }
        if (CollectionUtils.isEmpty(phraseIdList)) {
            phraseIdList = DefaultUtil.defaultList(phraseModelList).stream()
                .map(PhraseModel::getId).collect(Collectors.toList());
        }
        List<PhraseModel> rearrangeModelList = phraseRepository.findByIdList(phraseIdList);
        String prompt = buildNewPrompt(bizId, rearrangeModelList, input);
        List<PhraseModel> promptFaqList = fetchPromptFaqList(rearrangeModelList);
        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildRearrangePromptFaqTraceLog(sessionBo,
            id, entryPoint, input, promptFaqList)));

        if (abilityConfig.urlReplaceSwitch) {
            prompt = compressor.compress(prompt);
        }
        log.info("ChatServiceImpl newAnswerModel msgId = {}, prompt : {}", id, prompt);
        rearrangeChat(sessionBo, id, uid, input, prompt, phraseVectorList, rearrangeModelList, callback,compressor.getDecompressionMap());
    }

    private void triggerTask(SessionBo sessionBo, Integer uid, long bizId, long id, String input, String entryPoint,
        GeneralCallback callback, String version) {
        log.info("ChatServiceImpl triggerTask，id = {}, uid = {}, bizId = {}, input = {}, session = {}", id, uid
            , bizId, input, JsonUtil.toJson(sessionBo));

        LLMTypeModel LLMTypeModel = aiChatConfig.LLMTypeMap.get(sessionBo.fetchTaskModel());

        // 设置多轮信息
        boolean isQuestionFromScene = sessionBo.fromScene();
        if (isQuestionFromScene) {
            sessionBo.configId(id);
        }

        // 更新 chatMsg 中的多轮会话ID
        updateInputMsgContext(id, sessionBo);

        // 获取大模型的返回
        List<ChatRecordBo> history = sessionBo.currentContext().isUseHistory() ? fetchSessionHistory(sessionBo)
            : Collections.emptyList();

        String pattern = fetchTaskLlmAnswer(sessionBo, LLMTypeModel, sessionBo.fetchTaskPrompt()
            , history, chatContentConverter.toMarkdownTextFromJson(input));

        // 输出范围校验
        if (!checkOutputScope(sessionBo.fetchTaskId(), pattern)) {
            pattern = "-1";
        }

        // 记录大模型的返回
        insertAiMsg(pattern, LLMTypeModel, sessionBo);

        // 模式识别
        GeneralAnswerBo answer = new GeneralAnswerBo();
        PatternParam patternParam = PatternParam.builder().msgId(id).pattern(pattern).answer(answer)
                .session(sessionBo).callback(callback).input(input).build();
        boolean needBackToNormalMatch = patternFactory.process(patternParam);
        if (needBackToNormalMatch) {
            trigger(sessionBo, uid, bizId, id, input, entryPoint, callback, version);
            return;
        }

        // 增加多轮的曝光日志
        ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel.buildTaskDisplayTraceLog(sessionBo
            , chatContentConverter.extractComponentFromText(answer.getAnswer()))));

        // 返回
        answer.setMsgId(id);
        answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        answer.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());
        answer.setTop1FQScore(sessionBo.fetchTaskTop1Score());
        answer.setTop1QuestionId(sessionBo.fetchTaskTop1QuestionId());
        answer.setTopK(Collections.singletonList(VectorBo.builder().id(sessionBo.fetchTaskTop1QuestionId()).score(sessionBo.fetchTaskTop1Score()).build()));
        callback.answerCallback(answer);
    }

    private void triggerIntention(SessionBo sessionBo, Integer uid, long bizId, long questionMsgId, String input,
        String entryPoint, long recognizePhraseId, GeneralCallback callback, String version) {
        log.info(
            "ChatServiceImpl triggerIntention，id = {}, uid = {}, bizId = {}, input = {},recognizePhraseId={},session = {}",
            questionMsgId, uid, bizId, input, recognizePhraseId, JsonUtil.toJson(sessionBo));
        PhraseModel phraseModel = phraseRepository.findById(recognizePhraseId);
        ParamCheckUtil.isTrue(phraseModel != null && phraseModel.isEnable(),
            "意图识别配置的phraseModel不合法，phraseId=" + recognizePhraseId);

        sessionBo.configIntentRecognitionQuestionId(recognizePhraseId);
        if (TriggerTypeEnum.TASK.equals(phraseModel.getTriggerType())) {
            TaskModel taskModel = taskRepository.findById(phraseModel.getTriggerId());
            sessionBo.configFromTask(taskModel, questionMsgId);
            triggerTask(sessionBo, uid, bizId, questionMsgId, phraseModel.getPhrase(), entryPoint, callback, version);
            return;
        }

        if(TriggerTypeEnum.FAQ.equals(phraseModel.getTriggerType())){
            VectorBo vectorBo = VectorBo.builder().id(recognizePhraseId).score(1.0).build();
            GeneralAnswerBo answerBo = generalFaqAnswerBo(questionMsgId, phraseModel.getTriggerId(), vectorBo);
            callback.answerCallback(answerBo);
            return;
        }
    }

    private void rearrangeChat(SessionBo sessionBo, Long msgId, Integer uid, String input, String prompt
        , List<VectorBo> phraseVectorList, List<PhraseModel> rearrangeModelList, GeneralCallback callback, Map<String, String> outputVariableMap) {

        LLMTypeModel LLMTypeModel = aiChatConfig.LLMTypeMap.get(aiChatConfig.answerModelChatLlm);
        ParamCheckUtil.notNull(LLMTypeModel, "新回复模型的大模型为空");

        VectorBo top1 = CollectionUtils.isNotEmpty(phraseVectorList) ? phraseVectorList.get(0) : null;
        BiConsumer<String, Boolean> consumer = (responseMessage, lastOne) -> {
            if (responseMessage == null && !DefaultUtil.defaultBoolean(lastOne)) {
                return;
            }

            List<Object> contentObjList = new ArrayList<>();
            contentObjList.add(chatContentConverter.buildMarkdown(responseMessage));
            if (lastOne) {
                List<String> rearrangePhraseList = DefaultUtil.defaultList(rearrangeModelList).stream().filter(Objects::nonNull)
                    .map(PhraseModel::getStandardizedPhrase).distinct().limit(aiChatConfig.aiChatTopN).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(rearrangePhraseList)) {
                    contentObjList.add(chatContentConverter.buildSuffixOptions(aiChatConfig.answerModelSuffixOptionPrefix, rearrangePhraseList));
                }
                // 增加曝光log
                ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel
                    .buildFloatingOptionDisplayTraceLog(sessionBo.getSessionId(), input, rearrangePhraseList)));
            }

            GeneralAnswerBo answer = new GeneralAnswerBo();
            answer.setMsgId(msgId);
            answer.setStatus(lastOne ? ChatAnswerStatusEnum.FINISH.getCode() : ChatAnswerStatusEnum.ANSWERING.getCode());
            answer.setAnswerType(AnswerTypeEnum.REARRANGE.getCode());
            answer.setAnswer(JsonUtil.toJson(contentObjList));
            answer.setOutputVariableMap(outputVariableMap);
            if (top1 != null) {
                answer.setTop1FQScore(top1.getScore());
                answer.setTop1QuestionId(top1.getId());
            }
            answer.setTopK(phraseVectorList);
            callback.answerCallback(answer);
        };
        Consumer<Throwable> onError = e -> log.error("ChatServiceImpl rearrangeChat chat error，uid = {}, msgId = {}, msg = {}", uid, msgId, e.getMessage(), e);
        llmService.chatWithStream(LLMTypeModel, prompt, Collections.emptyList(), input, onError, consumer);
    }

    private String fetchTaskLlmAnswer(SessionBo sessionBo, LLMTypeModel LLMTypeModel, String prompt, List<ChatRecordBo> history, String input) {
        if (aiChatConfig.modifyLabour) {
            PhraseModel phraseModel = phraseRepository.findById(sessionBo.fetchTaskTop1QuestionId());
            if (phraseModel != null && StringUtils.isNotBlank(phraseModel.getStandardizedPhrase())
                && StringUtils.equals(phraseModel.getStandardizedPhrase(), aiChatConfig.labourTaskName)) {
                return CollectionUtils.isEmpty(history) ? aiChatConfig.labourApi : "-1";
            }
        }

        if (sessionBo.fetchTaskId() != null && sessionBo.fetchTaskId().equals(aiChatConfig.qualificationRejectedDomainTaskId)) {
            AtomicInteger index = new AtomicInteger(1);
            history = DefaultUtil.defaultList(history).stream().map(record -> {
                ChatRecordBo bo = new ChatRecordBo();
                bo.setType(record.getType());
                if (record.getType() == ChatContentTypeEnum.QUESTION) {
                    bo.setContent(String.format("第%d个问题：%s", index.getAndIncrement(), record.getContent()));
                } else {
                    bo.setContent(record.getContent());
                }
                return bo;
            }).collect(Collectors.toList());
            if (DefaultUtil.defaultBoolean(aiChatConfig.qualificationRejectedDomainOnlyOneInput)) {
                history = new ArrayList<>();
            }
            input = String.format("第%d个问题：%s", index.getAndIncrement(), input);
        }
        if (DefaultUtil.defaultBoolean(aiChatConfig.checkRepeatBreak) && CollectionUtils.isNotEmpty(history)) {
            int counter = 0;
            for (int i = history.size() - 1; i >= 0; i--) {
                ParamCheckUtil.isTrue(counter < 5, "出现连续break");
                ChatRecordBo chatRecordBo = history.get(i);
                if (chatRecordBo != null && chatRecordBo.getType() == ChatContentTypeEnum.ANSWER
                    && taskBreakPatternProcessor.match(chatRecordBo.getContent())) {
                    counter++;
                } else {
                    break;
                }
            }
        }

        String pattern = StringUtil.trim(llmService.chat(LLMTypeModel, prompt, history, input));
        boolean anyMatch = patternFactory.canProcess(pattern);
        if (!anyMatch) {
            log.error("ChatServiceImpl fetchTaskLlmAnswer not match, pattern = {}", pattern);
            return llmService.chat(aiChatConfig.LLMTypeMap.get(aiChatConfig.defaultModel), prompt, history, input);
        }
        return pattern;
    }

    private void updateInputMsgContext(Long msgId, SessionBo sessionBo) {
        ChatMsgModel chatMsg = new ChatMsgModel();
        chatMsg.setId(msgId);
        chatMsg.setTaskSessionId(sessionBo.fetchTaskSessionId());
        chatMsg.setContext(JsonUtil.toJson(sessionBo.currentContext()));
        chatMsgRepository.update(chatMsg);
    }

    private void insertAiMsg(String pattern, LLMTypeModel LLMTypeModel, SessionBo sessionBo) {
        ChatMsgModel aiMsg = new ChatMsgModel();
        aiMsg.setSessionId(sessionBo.getSessionId());
        aiMsg.setUid(sessionBo.getUid());
        aiMsg.setMis(sessionBo.getMis());
        aiMsg.setAbilityType(AbilityTypeEnum.GENERAL);
        aiMsg.setContentType(ChatContentTypeEnum.ANSWER);
        aiMsg.setAnswerType(AnswerTypeEnum.getTaskAnswerTypeByLlmType(LLMTypeModel));
        aiMsg.setContent(pattern);
        aiMsg.setSensitiveStatus(SensitiveStatusEnum.NORMAL);
        aiMsg.setTaskSessionId(sessionBo.fetchTaskSessionId());
        aiMsg.setCreateTime(new Date());
        aiMsg.setContext(JsonUtil.toJson(sessionBo.currentContext()));

        chatMsgRepository.insert(aiMsg);
    }

    private void greaterAndEqualK1(SessionBo sessionBo, Long msgId, Long bizId, String input, VectorBo vectorBo,
        String entryPoint, GeneralCallback callback, String version) {

        PhraseModel phrase = phraseRepository.findById(vectorBo.getId());
        if (phrase == null) {
            log.error("Phrase 不存在， id = {}", vectorBo.getId());
            throw new BizException(BizErrorEnum.PHRASE_NOT_EXIST);
        }

        Long triggerId = phrase.getTriggerId();
        TriggerTypeEnum triggerTypeEnum = phrase.getTriggerType();

        log.info("StandardQuestionRagStrategy.greaterAndEqualK1,vectorBo.getId()={},phrase={},version={},mis={}", vectorBo.getId(), phrase, version, sessionBo.getMis());
        if (VersionEnum.isLatestVersion(version)) {
            AiChatConfig.PhraseGrayModel phraseGrayModel = grayService.getPhraseGrayModel(phrase, new UserBo(sessionBo.getUid(), sessionBo.getMis()));
            if (phraseGrayModel != null) {
                triggerId = phraseGrayModel.getTriggerId();
                triggerTypeEnum = TriggerTypeEnum.getByCode(phraseGrayModel.getTriggerType());
            }
        }

        if (triggerTypeEnum == null) {
            log.error("TriggerTypeEnum 不存在， code = {}", phrase.getTriggerType());
            throw new BizException(BizErrorEnum.ENUM_NOT_EXIST);
        }

        if (triggerTypeEnum == TriggerTypeEnum.FAQ) {
            GeneralAnswerBo answerBo = generalFaqAnswerBo(msgId, triggerId, vectorBo);
            callback.answerCallback(answerBo);
            return;
        }

        if (triggerTypeEnum == TriggerTypeEnum.TASK) {
            TaskModel taskModel = taskRepository.findById(triggerId);
            ParamCheckUtil.notNull(taskModel, String.format("触发的多轮不存在, triggerId = %d", triggerId));

            sessionBo.configFromTask(taskModel, msgId);
            triggerTask(sessionBo, sessionBo.getUid(), bizId, msgId, phrase.getStandardizedPhrase()
                , entryPoint, callback, version);
        }
    }

    private GeneralAnswerBo generalFaqAnswerBo(Long msgId, Long faqId, VectorBo top1) {
        FaqModel faq = faqRepository.findById(faqId);
        ParamCheckUtil.notNull(faq, String.format("faq不存在：%d", faqId));
        OutputModel output = outputRepository.findById(faq.getAnswerId());
        ParamCheckUtil.notNull(output, String.format("output不存在：%d", faq.getAnswerId()));

        GeneralAnswerBo answer = new GeneralAnswerBo();
        outputService.processOutputToAnswer(output, answer, null);

        answer.setMsgId(msgId);
        answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        answer.setAnswerType(AnswerTypeEnum.STANDARD_FAQ.getCode());
        answer.setTop1FQScore(top1.getScore());
        answer.setTop1QuestionId(top1.getId());
        answer.setTopK(Collections.singletonList(top1));
        return answer;
    }

    private List<ChatRecordBo> fetchSessionHistory(SessionBo sessionBo) {
        List<ChatRecordBo> history = chatRecordService.fetchTaskFlowHistory(sessionBo.getSessionId()
            , sessionBo.fetchTaskSessionId());

        if (CollectionUtils.size(history) >= 1) {
            // 第一个问题调整
            if (aiChatConfig.modifyHistory && sessionBo.inTask() && CollectionUtils.size(history) > 1) {
                PhraseModel phraseModel = phraseRepository.findById(sessionBo.fetchTaskTop1QuestionId());
                boolean needModify = phraseModel != null && !phraseModel.isStandardPhrase();
                if (needModify) {
                    history.get(0).setContent(phraseModel.getStandardizedPhrase());
                }
            }

            // truncate 最后一条消息
            history = history.subList(0, history.size() - 1);
        }
        return history;
    }

    public String buildNewPrompt(long bizId, List<PhraseModel> rerankModelList, String query) {
        String knowledgePrompt = buildNewKnowledgePrompt(rerankModelList);
        String promptFormat  = aiChatConfig.tenantNewPromptMap.get(String.valueOf(bizId));
        return String.format(promptFormat, knowledgePrompt, query);
    }

    private List<PhraseModel> fetchPromptFaqList(List<PhraseModel> rerankModelList) {
        if (CollectionUtils.isEmpty(rerankModelList)) {
            return Collections.emptyList();
        }

        int maxFaqSize = aiChatConfig.promptSize;
        int counter = 0;
        List<PhraseModel> faqList = new ArrayList<>();
        for (PhraseModel phrase : rerankModelList) {
            if (phrase == null) {
                continue;
            }
            if (counter >= maxFaqSize) {
                break;
            }
            TriggerTypeEnum triggerTypeEnum = phrase.getTriggerType();
            if (triggerTypeEnum != TriggerTypeEnum.FAQ) {
                continue;
            }

            FaqModel faq = faqRepository.findById(phrase.getTriggerId());
            if (faq == null) {
                continue;
            }

            OutputModel output = outputRepository.findById(faq.getAnswerId());
            ParamCheckUtil.notNull(output, String.format("fetchPromptFaqList output不存在：%d", faq.getAnswerId()));

            faqList.add(phrase);
            counter++;
        }
        return faqList;
    }

    private String buildNewKnowledgePrompt(List<PhraseModel> rerankModelList) {
        if (CollectionUtils.isEmpty(rerankModelList)) {
            return StringUtils.EMPTY;
        }

        int maxFaqSize = aiChatConfig.promptSize;
        int counter = 0;
        StringBuilder stringBuilder = new StringBuilder();
        List<PhraseModel> faqList = new ArrayList<>();
        for (PhraseModel phrase : rerankModelList) {
            if (phrase == null) {
                continue;
            }
            if (counter >= maxFaqSize) {
                break;
            }
            TriggerTypeEnum triggerTypeEnum = phrase.getTriggerType();
            if (triggerTypeEnum != TriggerTypeEnum.FAQ) {
                continue;
            }

            FaqModel faq = faqRepository.findById(phrase.getTriggerId());
            if (faq == null) {
                continue;
            }

            OutputModel output = outputRepository.findById(faq.getAnswerId());
            ParamCheckUtil.notNull(output, String.format("output不存在：%d", faq.getAnswerId()));

            faqList.add(phrase);

            String content = output.getContent();
            if (StringUtils.isNotBlank(output.getTtUrl())) {
                content = chatContentConverter.merge(content, chatContentConverter.buildTtUrl(output.getTtUrl()));
            }
            stringBuilder.append("query:").append(phrase.getPhrase()).append(", ")
                .append("answer:").append(chatContentConverter.toMarkdownTextFromJson(content)).append("###");
            counter++;
        }
        return stringBuilder.toString();
    }

    private boolean checkOutputScope(Long taskId, String pattern) {
        TaskModel taskModel = taskRepository.findById(taskId);
        if (taskModel == null || StringUtils.isBlank(taskModel.getResources())
            || !JsonUtil.isJsonText(taskModel.getResources())) {
            log.error("TaskModel resources 字段配置错误,taskModel:{}", taskModel);
            return false;
        }

        List<String> patterns = new ArrayList<>();

        patterns.add("-1");

        TaskResourceModel taskResourceModel = JsonUtil.fromJson(taskModel.getResources(), TaskResourceModel.class);
        ParamCheckUtil.notNull(taskResourceModel, "多轮的资源为空");

        if(CollectionUtils.isNotEmpty(taskResourceModel.getOutputIds())){
            taskResourceModel.getOutputIds().forEach(outputId -> patterns.add("Output:" + outputId));
        }

        if(CollectionUtils.isNotEmpty(taskResourceModel.getApiIds())){
            taskResourceModel.getApiIds().forEach(apiId -> patterns.add("Api:" + apiId));
        }

        if(patterns.stream().anyMatch(p -> p.equalsIgnoreCase(pattern))){
            return true;
        }
        log.error("多轮输出范围不匹配，pattern={},范围={}",pattern,patterns);
        return false;
    }
}
