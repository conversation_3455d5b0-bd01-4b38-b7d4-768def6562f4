package com.sankuai.wmbdaiassistant.domain.enums.dx;

import lombok.Getter;

/**
 * @Desc 大象消息类型枚举
 * <AUTHOR>
 * @Date 2025/2/6
 **/

@Getter
public enum DxContentTypeEnum {

    TEXT(1, "文本"),
    AUDIO(2, "语音"),
    VIDEO(3, "视频"),
    IMAGE(4, "图片"),
    CAlENDER(5, "日程"),
    LINK(6, "图文"),
    MULTI_LINK(7, "多图文"),
    FILE(8, "文件"),
    GPS(9, "位置"),
    VCARD(10, "名片"),
    EMOTION(11, "表情"),
    EVENT(12, "事件"),
    CUSTOM(13, "模版"),
    GENERIC(14, "通用｜富文本｜卡片"),
    NEW_EMOTION(19, "新表情");

    private Integer code;

    private String desc;

    DxContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
