package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/11
 **/
public enum SensitiveStatusEnum {

    NORMAL(0, "正常"),
    SENSITIVE(1, "敏感");
    private int code;
    private String desc;

    SensitiveStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SensitiveStatusEnum findByCode(int code) {
        for (SensitiveStatusEnum status : SensitiveStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    public static boolean isSensitive(int code) {
        return Objects.equals(SENSITIVE.getCode(), code);
    }
}
