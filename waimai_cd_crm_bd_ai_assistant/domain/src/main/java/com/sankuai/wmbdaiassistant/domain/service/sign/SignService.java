package com.sankuai.wmbdaiassistant.domain.service.sign;

import com.sankuai.wmbdaiassistant.domain.bo.UserBo;

/**
 * 签约服务（BD在使用小助手时，需要先签约免责协议）
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface SignService {

    /**
     * 用户签约协议
     *
     * @param user 当前用户
     */
    void sign(UserBo user);


    /**
     * 检查一个用户是否已经签约
     *
     * @param uid UID
     * @return 是否已经签约
     */
    boolean alreadySigned(Integer uid);
}
