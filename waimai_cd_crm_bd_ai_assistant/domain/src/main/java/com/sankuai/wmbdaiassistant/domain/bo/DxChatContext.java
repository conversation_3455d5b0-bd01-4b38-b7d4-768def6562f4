package com.sankuai.wmbdaiassistant.domain.bo;

import com.sankuai.wmbdaiassistant.domain.enums.EntryPointTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.dx.DxActionTypeEnum;
import lombok.Data;
import lombok.ToString;

/**
 * 大象聊天上下文
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/1/26 12:37
 */
@Data
@ToString
public class DxChatContext {

    /**
     * 群聊id
     */
    private Long gid;

    /**
     * 大象uid
     */
    private Long dxUid;

    /**
     * SSO uid
     */
    private Long empId;

    /**
     * 用户mis
     */
    private String misId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户提问内容
     */
    private String query;

    /**
     * 大象消息id
     */
    private Long dxMsgId;

    /**
     * 事件时间，来之 dxEvent.getCtime()
     */
    private Long eventTime;

    /**
     * 消息事件包含全部的信息
     **/
    private String dxEvent;

    /**
     * 消息事件类型
     **/
    private int eventType;

    /**
     * 消息类型:文本，语音 . . .
     */
    private int msgType;

    /**
     * 大象会话信息
     */
    private SessionBo session;

    /**
     * 问题消息小蜜id
     */
    private Long queryMsgXmId;

    /**
     * 答案消息小蜜id
     */
    private Long answerMsgXmId;


    /**
     * 来之哪条消息小蜜id，主要针对回复消息的回调
     */
    private Long sourceMsgXmId;

    /**
     * 入口类型
     */
    private EntryPointTypeEnum entryPointType;

    /**
     * 大象交互类型，点赞，点踩，提问
     */
    private DxActionTypeEnum actionTypeEnum;

    public String getCardId() {
        if (dxUid == null || dxMsgId == null || eventTime == null) {
            return null;
        }
        return String.format("%s-%s-%s", dxUid, dxMsgId, eventTime);
    }

    public boolean isGroup() {
        return gid != null;
    }

}
