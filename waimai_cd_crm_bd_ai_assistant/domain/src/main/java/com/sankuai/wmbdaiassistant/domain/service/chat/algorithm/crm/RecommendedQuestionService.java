package com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm;

import java.util.List;

/**
 * 推荐问题服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-28 10:22
 */
public interface RecommendedQuestionService {

    /**
     * 推荐问题
     *
     * @param question 用户当前问题
     * @param datasetIds 知识库ID列表
     * @param size 返回的推荐问题数量
     * @return
     */
    List<String> recommendQuestions(String question, List<Long> datasetIds, int size);

}
