package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Markdown格式的文本
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Markdown implements ContentElement {
    private final String type = ContentElementTypeEnum.MARKDOWN.getCode();

    @JsonProperty("insert")
    private MarkdownDetail markdownDetail;

    public static Markdown build(String content) {
        return Markdown.builder().markdownDetail(MarkdownDetail.builder().markdownConfig(
                MarkdownConfig.builder().text(content).build()).build()).build();
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void merge(ContentElement other) {
        ParamCheckUtil.isTrue((other instanceof Markdown), "Markdown merge error");
        Markdown otherMk = (Markdown) other;
        if (otherMk == null || otherMk.getMarkdownDetail() == null
                || otherMk.getMarkdownDetail().getMarkdownConfig() == null) {
            return;
        }
        String text = otherMk.getMarkdownDetail().getMarkdownConfig().getText();
        if (markdownDetail == null) {
            this.markdownDetail = MarkdownDetail.builder().markdownConfig(MarkdownConfig.builder()
                    .text(text).build()).build();
            return;
        }
        if (markdownDetail.getMarkdownConfig() == null) {
            this.markdownDetail.setMarkdownConfig(MarkdownConfig.builder().text(text).build());
            return;
        }
        String mergedText = DefaultUtil.defaultValue(this.markdownDetail.getMarkdownConfig().getText(), "") + text;
        this.markdownDetail.getMarkdownConfig().setText(mergedText);
    }

    @Override
    public String toMarkdownText() {
        if (getMarkdownDetail() == null || getMarkdownDetail().getMarkdownConfig() == null) {
            return null;
        }
        return getMarkdownDetail().getMarkdownConfig().getText();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarkdownDetail {
        @JsonProperty("markdown")
        MarkdownConfig markdownConfig;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarkdownConfig {
        private String text;
    }
}
