package com.sankuai.wmbdaiassistant.domain.service.chat;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.domain.model.GrayModel;
import com.sankuai.wmbdaiassistant.domain.model.LLMTypeModel;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

/**
 * AI会话配置
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-14 16:29
 */
@Configuration
public class AiChatConfig {

    /**
     * 关联问最多展示的个数
     */
    @MdpConfig("chat.relative.question.top.n:5")
    public int relativeQuestionTopN;

    /**
     * 关联问获取个数
     */
    @MdpConfig("chat.relative.question.fetch.size:10")
    public int relativeQuestionFetchSize;

    /**
     * AI会话获取个数
     */
    @MdpConfig("chat.ai.fetch.size:10")
    public int aiChatFetchSize;

    /**
     * AI会话最多展示的相关问题的个数
     */
    @MdpConfig("chat.ai.top.n:5")
    public int aiChatTopN;

    /**
     * K1 值
     */
    @MdpConfig("chat.k1:0.95")
    public double k1;

    /**
     * K2 值
     */
    @MdpConfig("chat.k2:0.6")
    public double k2;

    /**
     * 当用户同时提多个问题时，给出提示
     */
    @MdpConfig("ai.chat.concurrent.error.msg:正在回答，请稍等")
    public String aiChatConcurrentErrorMsg;

    /**
     * 租户的 prompt 配置 【V2】
     */
    @MdpConfig("tenant.prompt.v2.map:{\"5001\":\"【角色】：你是一个面向BD的AI智能助理，名叫小蜜AI助手\\n【任务】：请参考所给定的问答知识库，回答用户提出的问题。你需要忽略知识库中与问题无关的部分，只选择最相关的部分进行输出，如果知识库中不存在相关的知识，可以引导用户再次详细描述下所遇到的问题\\n需要参考的问答知识库（以###分割）：%s\\n【注意】：\\n- 【重要】1.如果用户问题与知识库问答高度相似，则直接返回对应答案，注意不要修改任何内容\\n- 【重要】2.如果用户问题在知识库中提供了解决方案，则整合相关的答案进行输出，相应答案中的要点必须都包含，尤其是相关的图片和网站链接需要原样输出\\n- 【重要】3.你需要先判断用户问题所属的意图，用户意图主要是咨询业务问题，另外还会包含闲聊/吐槽/宣泄/感谢等意图\\n- 4.如果用户意图是闲聊/吐槽/宣泄/感谢，则不需要参考给定的知识库，需要付有感情地礼貌与用户沟通\\n- 5.回答要简明扼要，不要提到根据知识库，不需要重复用户问题，直接给出相关的答案即可\\n- 6.你来自美团公司，你的回答不能包含抖音、饿了么、字节跳动、阿里等其它竞对公司的词语\\n- 7. 输出时不需要任何格式，比如加粗和斜体等\\n【问题】：%s\\n【输出】：\",\"1\":\"【角色】：你是一个面向BD的AI智能助理，名叫小蜜AI助手\\n【任务】：请参考所给定的问答知识库，回答用户提出的问题。你需要忽略知识库中与问题无关的部分，只选择最相关的部分进行输出，如果知识库中不存在相关的知识，可以引导用户再次详细描述下所遇到的问题\\n需要参考的问答知识库（以###分割）：%s\\n【注意】：\\n- 【重要】1.如果用户问题与知识库问答高度相似，则直接返回对应答案，注意不要修改任何内容\\n- 【重要】2.如果用户问题在知识库中提供了解决方案，则整合相关的答案进行输出，相应答案中的要点必须都包含，尤其是相关的图片和网站链接需要原样输出\\n- 【重要】3.你需要先判断用户问题所属的意图，用户意图主要是咨询业务问题，另外还会包含闲聊/吐槽/宣泄/感谢等意图\\n- 4.如果用户意图是闲聊/吐槽/宣泄/感谢，则不需要参考给定的知识库，需要付有感情地礼貌与用户沟通\\n- 5.回答要简明扼要，不要提到根据知识库，不需要重复用户问题，直接给出相关的答案即可\\n- 6.你来自美团公司，你的回答不能包含抖音、饿了么、字节跳动、阿里等其它竞对公司的词语\\n- 7. 输出时不需要任何格式，比如加粗和斜体等\\n【问题】：%s\\n【输出】：\"}")
    public HashMap<String, String> tenantNewPromptMap;

    /**
     * 知识库的 prompt 配置
     */
    @MdpConfig("faq.prompt.format:知识库的格式为 json，内容为：%s。")
    public String faqPromptFormat;

    /**
     * 分片 prompt 格式
     */
    @MdpConfig("fragment.prompt.format:知识库：%s, query = %s")
    public String fragmentPromptFormat;

    @MdpConfig("fragment.size:5")
    public Integer fragmentSize;

    /**
     * 分片知识库的前缀
     */
    @MdpConfig("fragment.knowledge.prefix:需要参考的知识库")
    public String fragmentKnowledgePrefix;

    /**
     * prompt 中所带的 faq 的 size
     */
    @MdpConfig("prompt.faq.size:3")
    public int promptSize;

    /**
     * 如果问题处于 k1 和 k2 ，回答有一个前缀
     */
    @MdpConfig("relation.question.prefix:您是想问如下问题吗？")
    public String relationQuestionPrefix;

    /**
     * 当系统异常时，默认的提示语
     */
    @MdpConfig("ai.chat.default.msg:抱歉，因服务过于火爆，暂时无法给出答案，建议老铁右下角点击刷新按钮，刷新会话后再重新咨询一下")
    public String aiChatDefaultMsg;

    /**
     * 选项列表的前缀
     */
    @MdpConfig("ai.chat.option.list.prefix:请选择具体问题")
    public String aiChatOptionListPrefix;

    /**
     * 是否修改历史记录
     */
    @MdpConfig("ai.chat.history.modif:true")
    public boolean modifyHistory;

    /**
     * 是否修改人工服务的多轮
     */
    @MdpConfig("ai.chat.labour.modify:true")
    public boolean modifyLabour;

    /**
     * 人工服务多轮的名称
     */
    @MdpConfig("ai.chat.labour.name:人工服务")
    public String labourTaskName;

    /**
     * 人工服务多轮的调用的 api
     */
    @MdpConfig("ai.chat.labour.api:Api:1")
    public String labourApi;

    /**
     * 新版本回复模型的重排及补召的个数
     */
    @MdpConfig("answer.model.rerank.top.n:10")
    public Integer answerModelRerankTopN;

    /**
     * 新版本回复模型的大模型
     */
    @MdpConfig("answer.model.chat.model:chatgpt4o")
    public String answerModelChatLlm;

    /**
     * 新版本回复模型的大模型
     */
    @MdpConfig("answer.model.suffix.options.prefix:你还想问什么")
    public String answerModelSuffixOptionPrefix;

    /**
     * 新版本回复模型是否模拟重排接口
     */
    @MdpConfig("answer.model.mock.rearrange:false")
    public Boolean answerModelMockRearrange;

    @MdpConfig("qualification.rejected.domain.task.id:51")
    public Long qualificationRejectedDomainTaskId;

    @MdpConfig("qualification.rejected.domain.only.one.input:true")
    public Boolean qualificationRejectedDomainOnlyOneInput;

    @MdpConfig("check.repeat.break:true")
    public Boolean checkRepeatBreak;

    /**
     * 工具栏打点映射
     */
    @MdpConfig("toolbar.trace.map:{\"话术陪练\":\"ChatPractice\"}")
    public HashMap<String, String> toolbarTractMap;

    @MdpConfig("llm.default.model:chatgpt4o")
    public String defaultModel;

    /**
     * 大模型类型
     */
    @MdpConfig("llm.type.map:{}")
    public HashMap<String, LLMTypeModel> LLMTypeMap;

    /**
     * phrase的触发规则的灰度逻辑
     */
    @MdpConfig("phrase.gray.map:{}")
    public HashMap<Long, PhraseGrayModel> phraseGrayMap;

    @Data
    public static class PhraseGrayModel {
        private Long triggerId;
        private String triggerType;
        private GrayModel gray;
    }

    @MdpConfig("recommended.question.size:3")
    public Integer recommendedQuestionSize;

    @MdpConfig("multi.task.default.message:解决方案正在接入大象中，您可以在蜜蜂或先富进行操作～")
    public String defaultTaskMessage;

    @MdpConfig("system.rag.gray:{\"graySwitch\":true,\"forceAll\":false,\"whiteMisList\":[],\"whiteOrgIdList\":[]}")
    public GrayModel systemGrayModel;

    @MdpConfig("sop.filter.switch:false")
    public Boolean sopFilterSwitch;

    @MdpConfig("picture.addition.data.modify.switch:true")
    public Boolean pictureAdditionDataModifySwitch;

    @MdpConfig("picture.data.question.empty:false")
    public Boolean pictureDataQuestionEmpty;
}
