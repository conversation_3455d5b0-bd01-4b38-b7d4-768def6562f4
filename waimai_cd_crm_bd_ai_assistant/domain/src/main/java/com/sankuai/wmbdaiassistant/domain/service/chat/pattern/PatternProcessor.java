package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

/**
 * 模式处理器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 11:05
 */
public interface PatternProcessor {

    /**
     * 是否匹配
     *
     * @param pattern 模式
     * @return 是否匹配
     */
    boolean match(String pattern);

    /**
     * 处理
     *
     * @param param 参数
     * @return 当前模式处理完后，是否走跳出当前多轮，执行正常的匹配逻辑
     */
    boolean process(PatternParam param);

}
