package com.sankuai.wmbdaiassistant.domain.enums.km;

/**
 * @description: 知识库权限主体枚举
 * @author: fengxin21
 * @create: 2025/4/10
 **/
public enum KmPermissionMainBodyEnum {
    DEPARTMENT(0, "部门"),
    PERSONAL(1, "个人"),
    EMAIL_GROUP(3, "邮件组"),
    DAXIANG_GROUP(4, "大象群");

    private final int code;
    private final String desc;

    KmPermissionMainBodyEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}


