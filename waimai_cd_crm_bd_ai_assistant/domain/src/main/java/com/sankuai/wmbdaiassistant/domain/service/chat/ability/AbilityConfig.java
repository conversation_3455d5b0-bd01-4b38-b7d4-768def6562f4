package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import java.util.HashMap;
import java.util.HashSet;
import org.springframework.context.annotation.Configuration;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/26
 **/
@Configuration
public class AbilityConfig {

    @MdpConfig("fetch.ai.answer.max.loop.count:10")
    public int fetchAiAnswerMaxLoopCount;

    @MdpConfig("fetch.ai.answer.latest.max.loop.count:200")
    public int fetchAiAnswerLatestMaxLoopCount;

    @MdpConfig("ai.sensitive.replace.content:因AI输出的内容涉及敏感信息，暂时无法为您显示")
    public String aiSensitiveReplaceContent;

    @MdpConfig("jump.ability.answer.prefix.text.content:你希望跳转到以下哪个页面呢？")
    public String jumpAbilityAnswerPrefixTextContent;

    @MdpConfig("selection.type.answer.count.per.page:5")
    public int selectionTypeAnswerCountPerPage;

    @MdpConfig("tt.url.transfer.map:{}")
    public HashMap<String, String> ttUrlTransferMap;

    @MdpConfig("tt.url.replace.switch:true")
    public boolean ttUrlReplaceSwitch;

    @MdpConfig("url.replace.switch:false")
    public boolean urlReplaceSwitch;
}
