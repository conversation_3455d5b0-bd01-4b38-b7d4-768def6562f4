package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.common.exception.BizException;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.MsgTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ServiceScoreSubAbilityEnum;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.authenticate.AuthenticateRoleQueryService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentBuilder;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.impl.ChatRecordServiceImpl;
import com.sankuai.wmbdaiassistant.domain.service.chat.servicescore.ServiceIndex;
import com.sankuai.wmbdaiassistant.domain.service.chat.servicescore.ServiceScoreService;
import com.sankuai.wmbdaiassistant.domain.service.chat.user.WmUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
@Component
@Slf4j
public class ServiceScoreAbility implements BaseAbility {

    @Resource
    private WmUserService wmUserService;

    @Resource
    private ChatRecordServiceImpl chatRecordService;

    @Resource
    private ServiceScoreService serviceScoreService;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private AuthenticateRoleQueryService authenticateRoleQueryService;

    @MdpConfig("service_score_prefix_text_content:请问您想要查看本月的哪些数据呢～")
    private String prefixTextContent;

    @MdpConfig("service_score_bd_position:[20,21]")
    private ArrayList<Integer> bdPositionIds;

    @MdpConfig("service_score_delay_day:2")
    private int delayDay;

    @MdpConfig("service_score_interception_prompt:很抱歉，我们暂时不支持，能力小蜜还在努力升级中哦～")
    private String interceptionPrompt;

    @MdpConfig("simulate_bd_uid:-1")
    private int simulateBdUid;

    @MdpConfig("ai_assistant_service_score_query_role:wmbdaiassistant-role-service_score_query")
    public String aiAssistantServiceScoreQueryRole;

    @Override
    public AbilityTypeEnum getAbilityType() {
        return AbilityTypeEnum.SERVICE_SCORE;
    }

    @Override
    public Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo) {
        if (simulateBdUid != -1) {
            chatSubmitQueryBo.getSessionBo().setUid(simulateBdUid);
        }

        // 如果用户权限角色列表为空或没有BD智能助手-服务分查询角色，直接抛异常
        List<String> roles = authenticateRoleQueryService.getUserRoleListByUid(chatSubmitQueryBo.getSessionBo().getUid());
        if (CollectionUtils.isEmpty(roles) || !roles.contains(aiAssistantServiceScoreQueryRole)) {
            throw new BizException(BizErrorEnum.NO_PERMISSION_USER, interceptionPrompt);
        }
        ChatMsgModel msg = chatRecordService.createQueryMsg(chatSubmitQueryBo);

        return msg.getId();
    }

    @Override
    public ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo) {
        if (Objects.isNull(requestBo)) {
            return null;
        }
//      subAbilityType为null，返回服务分所有的子技能
        Integer subAbilityType = requestBo.getSubAbilityType();
        if (Objects.isNull(subAbilityType)) {
            return buildAllSubAbilityResponse(requestBo);
        }
        int uid = requestBo.getSessionBo().getUid();
        if (simulateBdUid != -1) {
            uid = simulateBdUid;
        }
        Long startTime = 0L;
        Long endTime = 0L;
        if (getCurMonthData(delayDay)) {
            startTime = getFirstDayOfCurMonth().toEpochSecond(ZoneOffset.of("+8"));
        }
        else {
            startTime = getFirstDayOfLastMonth().toEpochSecond(ZoneOffset.of("+8"));
        }
        endTime = LocalDateTime.now().plusDays(-delayDay).toEpochSecond(ZoneOffset.of("+8"));
        log.info("startTime:{},endTime:{}", startTime, endTime);

        ServiceIndex serviceIndex = serviceScoreService.query(uid, startTime.intValue(), endTime.intValue());
        if (Objects.isNull(serviceIndex)) {
            throw new BizException(BizErrorEnum.NO_SERVICE_SCORE_INDEX, "未查到服务分信息");
        }

        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setMsgId(requestBo.getMsgId());
        chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        chatFetchAnswerBo.setAbilityType(requestBo.getAbilityType());
        chatFetchAnswerBo.setSubAbilityType(requestBo.getSubAbilityType());
        chatFetchAnswerBo.setHasNext(false);

        String currentContent = "";
        if (subAbilityType.equals(ServiceScoreSubAbilityEnum.SERVICE_MAX_SCORE.getCode())) {
            currentContent = buildMaxScoreContent(serviceIndex);
        } else if (subAbilityType.equals(ServiceScoreSubAbilityEnum.SERVICE_SCORE_RATE.getCode())) {
            currentContent = buildScoreRateContent(serviceIndex);
        } else if (subAbilityType.equals(ServiceScoreSubAbilityEnum.SERVICE_SCORE.getCode())) {
            currentContent = buildServiceScoreContent(serviceIndex);
        } else if (subAbilityType.equals(ServiceScoreSubAbilityEnum.SERVICE_NUM.getCode())) {
            currentContent = buildServiceNumContent(serviceIndex);
        }
        chatFetchAnswerBo.setCurrentContent(new ChatContentBuilder().add(chatContentConverter.buildText(currentContent)).build());
        return chatFetchAnswerBo;
    }

    private String buildServiceNumContent(ServiceIndex serviceIndex) {
        StringBuilder sb = new StringBuilder();
        sb.append(serviceIndex.getServiceNum());
        return sb.toString();
    }

    private String buildScoreRateContent(ServiceIndex serviceIndex) {
        StringBuilder sb = new StringBuilder();
        sb.append("服务态度得分率为").append(serviceIndex.getScoreRate()).append("%");
        if (Objects.nonNull(serviceIndex.getScoreRateYoy())) {
            sb.append("，").append("同比");
            if (serviceIndex.getScoreRateYoy() >= 0) {
                sb.append("上升");
            } else {
                sb.append("下降");
            }
            sb.append(Math.abs(serviceIndex.getScoreRateYoy()));
            sb.append("%").append("，").append("你要加油哦～");
        }
        return sb.toString();
    }

    private String buildServiceScoreContent(ServiceIndex serviceIndex) {
        StringBuilder sb = new StringBuilder();
        sb.append(serviceIndex.getServiceScore()).append("分");
        return sb.toString();
    }

    private String buildMaxScoreContent(ServiceIndex serviceIndex) {
        StringBuilder sb = new StringBuilder();
        sb.append(serviceIndex.getServiceMaxScore()).append("分");
        return sb.toString();
    }

    private ChatFetchAnswerBo buildAllSubAbilityResponse(ChatFetchAnswerRequestBo requestBo) {
        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setMsgId(requestBo.getMsgId());
        chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        chatFetchAnswerBo.setAbilityType(requestBo.getAbilityType());
        chatFetchAnswerBo.setSubAbilityType(requestBo.getSubAbilityType());
        chatFetchAnswerBo.setHasNext(false);

        chatFetchAnswerBo.setMsgType(MsgTypeEnum.TEXT.getCode());

        ChatContentBuilder chatContentBuilder = new ChatContentBuilder();
        chatContentBuilder.add(chatContentConverter.buildText(prefixTextContent));
        chatContentBuilder.add(chatContentConverter.buildServiceOptions());

        chatFetchAnswerBo.setCurrentContent(chatContentBuilder.build());

        return chatFetchAnswerBo;
    }

    private boolean getCurMonthData(int day) {
        int curDay = LocalDateTime.now().getDayOfMonth();
        return day < curDay;
    }

    private LocalDateTime getFirstDayOfCurMonth() {
        LocalDate currentDate = LocalDate.now();
        LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);
        LocalTime startTime = LocalTime.of(0, 0, 0);
        LocalDateTime firstDayOfMonthStart = LocalDateTime.of(firstDayOfMonth, startTime);
        return firstDayOfMonthStart;
    }

    private LocalDateTime getLastDayOfLastMonth() {
        LocalDateTime localDateTime = getFirstDayOfCurMonth().plusDays(-1);
        return localDateTime;
    }

    private LocalDateTime getFirstDayOfLastMonth() {
        LocalDate currentDate = LocalDate.now();
        LocalDate firstDayOfLastMonth = currentDate.plusMonths(-1).withDayOfMonth(1);
        LocalTime startTime = LocalTime.of(0, 0, 0);
        LocalDateTime firstDayOfLastMonthStart = LocalDateTime.of(firstDayOfLastMonth, startTime);
        return firstDayOfLastMonthStart;
    }

}
