package com.sankuai.wmbdaiassistant.domain.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 触发类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-05 16:11
 */
@Getter
public enum TriggerTypeEnum {

    FAQ("faq", "问答类"),
    TASK("task", "任务流"),
    ;

    private String code;
    private String desc;


    TriggerTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TriggerTypeEnum getByCode(String code) {
        for (TriggerTypeEnum triggerTypeEnum : values()) {
            if (StringUtils.equals(triggerTypeEnum.getCode(), code)) {
                return triggerTypeEnum;
            }
        }
        return null;
    }
}
