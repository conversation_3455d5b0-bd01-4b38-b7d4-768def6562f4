package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.TaskModel;

import java.util.List;

/**
 * 任务流
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-19 19:45
 */
public interface TaskRepository {

    /**
     * 插入
     *
     * @param taskModel 任务
     * @return 主键ID
     */
    Long insert(TaskModel taskModel);

    /**
     * 修改
     *
     * @param taskModel 多轮模型
     * @return 是否成功
     */
    boolean update(TaskModel taskModel);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 任务流
     */
    TaskModel findById(Long id);

    /**
     * 查询所有的任务
     *
     * @return 任务列表
     */
    List<TaskModel> findAll();
}
