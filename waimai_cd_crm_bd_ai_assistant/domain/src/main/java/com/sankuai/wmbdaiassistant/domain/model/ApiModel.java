package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.*;

import java.util.Date;

/**
 * API
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 13:57
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ApiModel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 海盗框架的 dsl 信息
     */
    private String dsl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 是否有效
     */
    private ValidEnum valid;
}
