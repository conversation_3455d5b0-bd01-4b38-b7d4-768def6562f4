package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * wiki 来源类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 17:11
 */
@Getter
public enum WikiSourceTypeEnum {
    WIKI("wiki", 1, "手工输入"),
    SUB_WIKI("sub_wiki", 2, "子wiki"),
    REFER_WIKI("refer_wiki", 3, "引用wiki"),
    ;

    private String code;
    private Integer priority;
    private String desc;

    WikiSourceTypeEnum(String code, Integer order, String desc) {
        this.code = code;
        this.priority = order;
        this.desc = desc;
    }

    public static WikiSourceTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WikiSourceTypeEnum value : WikiSourceTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public boolean hasHigherPriorityThan(WikiSourceTypeEnum wikiSourceTypeEnum) {
        if (wikiSourceTypeEnum == null || wikiSourceTypeEnum.getPriority() == null) {
            return true;
        }
        return this.priority < wikiSourceTypeEnum.getPriority();
    }
}
