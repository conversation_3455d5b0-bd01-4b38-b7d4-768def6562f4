package com.sankuai.wmbdaiassistant.domain.service.chat.cache;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;

/**
 * 会话缓存
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-17 19:43
 */
public interface SessionCacheService {

    /**
     * 从缓存中获取会话
     *
     * @param uid    用户id
     * @param source 会话来源
     * @return 会话上下文
     */
    SessionBo fetch(Integer uid, String source);

    /**
     * 从缓存中获取会话
     *
     * @param sessionId 会话id
     * @return 会话上下文
     */
    SessionBo fetch(Long sessionId);

    /**
     * 保存会话至缓存
     *
     * @param sessionBo 会话
     */
    void saveOrUpdate(SessionBo sessionBo);

    /**
     * 更新会话缓存的过期时间
     *
     * @param sessionBo 会话
     */
    void touch(SessionBo sessionBo);

    /**
     * 从缓存中删除会话
     *
     * @param sessionBo 会话
     */
    void delete(SessionBo sessionBo);

}
