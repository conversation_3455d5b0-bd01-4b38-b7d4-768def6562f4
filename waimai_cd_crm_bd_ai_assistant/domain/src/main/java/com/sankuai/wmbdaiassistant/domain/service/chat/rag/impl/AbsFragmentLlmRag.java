package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.FragmentReferenceBo;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatStreamChunkBo;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatContentTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.model.LLMTypeModel;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentRepository;
import com.sankuai.wmbdaiassistant.domain.repository.WikiRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.AbilityConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.AlgorithmChatService;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.DataRecallService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentCompressor;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.dataset.DatasetAuthService;
import com.sankuai.wmbdaiassistant.domain.service.chat.llm.LLMService;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricConstant;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricService;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.LlmRag;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import java.util.ArrayList;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 基于分片的LLM RAG抽象类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-01 17:43
 */
@Slf4j
public abstract class AbsFragmentLlmRag implements LlmRag {

    @Resource
    protected LLMService llmService;

    @Resource
    protected AbilityConfig abilityConfig;

    @Resource
    protected AiChatConfig aiChatConfig;

    @Resource
    protected MetricService metricService;

    @Resource
    protected WikiRepository wikiRepository;

    @Resource
    private TraceLogService traceLogService;

    @Resource
    protected DataRecallService dataRecallService;

    @Resource
    protected ChatRecordService chatRecordService;

    @Resource
    protected DatasetAuthService datasetAuthService;

    @Resource
    protected ChatContentConverter chatContentConverter;

    @Resource
    protected AlgorithmChatService algorithmChatService;

    @Resource
    private FragmentRepository fragmentRepository;

    @MdpConfig("abs.fragment.llm.rag.version:2")
    protected Integer llmRagVersion;

    /**
     * 构建回复消息
     *
     * @param message           大模型回复
     * @param lastOne           是否最后一条消息
     * @param fragmentModelList 召回分片
     * @return 合并后的消息
     */
    public abstract String buildResponseMessage(String message, Boolean lastOne, List<FragmentModel> fragmentModelList, String source);

    @Override
    public void query(SessionBo sessionBo, Long id, String input, String entryPoint, GeneralCallback callback) {
        if (llmRagVersion == 2) {
            queryV2(sessionBo, id, input, entryPoint, callback);
            return;
        }
        queryV1(sessionBo, id, input, entryPoint, callback);
    }

    private void queryV1(SessionBo sessionBo, Long id, String input, String entryPoint, GeneralCallback callback) {
        ChatContentCompressor compressor = new ChatContentCompressor();
        List<FragmentModel> fragmentModelList = metricService.recordCost(MetricConstant.BIZ, MetricConstant.FRAGMENT_RECALL
                , () -> dataRecallService.recall(input, datasetAuthService.queryUserVisibleDataset(sessionBo.getUid()),
                        fetchHistory(sessionBo.getSessionId(), input), aiChatConfig.fragmentSize));
        sessionBo.configFragments(fragmentModelList);

        ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel
            .buildFragmentRecallTraceLog(sessionBo.getSessionId(), id, input, fragmentModelList, entryPoint,
                new HashMap<>())));
        log.info("AbsFragmentLlmRag query msgId = {}, fragmentList = {}", id, DefaultUtil.defaultList(fragmentModelList)
            .stream().map(FragmentModel::getId).collect(Collectors.toList()));

        Long now = System.currentTimeMillis();

        String prompt = buildPrompt(input, fragmentModelList);
        if (abilityConfig.urlReplaceSwitch) {
            prompt = compressor.compress(prompt);
        }
        log.info("AbsFragmentLlmRag query msgId = {}, prompt : {}", id, prompt);
        rearrangeChat(sessionBo, id, input, prompt, fragmentModelList, callback, compressor.getDecompressionMap(), now);

    }

    private void queryV2(SessionBo sessionBo, Long id, String input, String entryPoint, GeneralCallback callback) {
        List<ChatRecordBo> history = fetchHistory(sessionBo.getSessionId(), input);
        AtomicBoolean setTime = new AtomicBoolean(false);
        long now = System.currentTimeMillis();
        Consumer<ChatStreamChunkBo> onMessage = streamChunkBo -> {
            if (!setTime.get()) {
                setTime.set(true);
                metricService.recordCost(MetricConstant.BIZ, MetricConstant.FIRST_ANSWER,
                    System.currentTimeMillis() - now);
            }

            if (streamChunkBo == null
                || StringUtils.isBlank(streamChunkBo.getAnswer()) && !streamChunkBo.isFinished()) {
                return;
            }

            List<FragmentModel> fragmentModelList = new ArrayList<>();
            boolean isFinished = streamChunkBo.isFinished();
            String content = streamChunkBo.getAnswer();

            if (isFinished) {
                fragmentModelList
                        .addAll(fragmentRepository.findByIdList(DefaultUtil.defaultList(streamChunkBo.getReferences())
                                .stream().map(FragmentReferenceBo::getId).collect(Collectors.toList())));
                ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel
                    .buildFragmentRecallTraceLog(sessionBo.getSessionId(), id, input, fragmentModelList, entryPoint,
                        new HashMap<>())));
                ExecutorUtil.safeExecute(
                        () -> traceLogService.insert(TraceLogModel.buildFragmentRag(sessionBo.getSessionId(), id, input,
                                entryPoint, streamChunkBo, fragmentModelList)));
                log.info("AbsFragmentLlmRagV2 query msgId = {}, fragmentList = {}", id,
                    DefaultUtil.defaultList(fragmentModelList).stream().map(FragmentModel::getId)
                        .collect(Collectors.toList()));
                sessionBo.configFragments(fragmentModelList);
            }

            GeneralAnswerBo answer = new GeneralAnswerBo();
            answer.setMsgId(id);
            answer.setStatus(
                isFinished ? ChatAnswerStatusEnum.FINISH.getCode() : ChatAnswerStatusEnum.ANSWERING.getCode());
            answer.setAnswerType(AnswerTypeEnum.FRAGMENT_REARRANGE.getCode());
            log.info("AbsFragmentLlmRag queryV2, source = {}", sessionBo.getSource());
            answer.setAnswer(buildResponseMessage(content, isFinished, fragmentModelList, sessionBo.getSource()));
            callback.answerCallback(answer);
        };
        List<Long> datasetIds = datasetAuthService.queryUserVisibleDataset(sessionBo.getUid());
        algorithmChatService.chatWithStream(sessionBo.getMis(), input, id, datasetIds, history, onMessage);
    }

    private List<ChatRecordBo> fetchHistory(Long sessionId, String input) {
        List<ChatRecordBo> history = chatRecordService.fetchSessionHistory(sessionId);
        if (CollectionUtils.isNotEmpty(history)) {
            ChatRecordBo lastMsg = history.get(history.size() - 1);
            if (lastMsg.getType() == ChatContentTypeEnum.QUESTION && StringUtils.equals(lastMsg.getContent(), input)) {
                history.remove(history.size() - 1);
            }
        }
        return history;
    }

    private void rearrangeChat(SessionBo sessionBo, Long msgId, String input, String prompt
            , List<FragmentModel> fragmentModelList, GeneralCallback callback
            , Map<String, String> outputVariableMap, Long now) {

        LLMTypeModel LLMTypeModel = aiChatConfig.LLMTypeMap.get(aiChatConfig.answerModelChatLlm);
        ParamCheckUtil.notNull(LLMTypeModel, "新回复模型的大模型为空");

        AtomicBoolean setTime = new AtomicBoolean(false);
        BiConsumer<String, Boolean> consumer = (responseMessage, lastOne) -> {
            if (!setTime.get()) {
                setTime.set(true);
                metricService.recordCost(MetricConstant.BIZ, MetricConstant.FIRST_ANSWER, System.currentTimeMillis() - now);
            }

            if (responseMessage == null && !DefaultUtil.defaultBoolean(lastOne)) {
                return;
            }

            GeneralAnswerBo answer = new GeneralAnswerBo();
            answer.setMsgId(msgId);
            answer.setStatus(lastOne ? ChatAnswerStatusEnum.FINISH.getCode() : ChatAnswerStatusEnum.ANSWERING.getCode());
            answer.setAnswerType(AnswerTypeEnum.FRAGMENT_REARRANGE.getCode());
            log.info("AbsFragmentLlmRag rearrangeChat, source = {}", sessionBo.getSource());
            answer.setAnswer(buildResponseMessage(responseMessage, lastOne, fragmentModelList, sessionBo.getSource()));
            answer.setOutputVariableMap(outputVariableMap);
            callback.answerCallback(answer);
        };
        Consumer<Throwable> onError = e -> log.error("AbsFragmentLlmRag rearrangeChat chat error" +
                ", msgId = {}, msg = {}", msgId, e.getMessage(), e);
        llmService.chatWithStream(LLMTypeModel, prompt, Collections.emptyList(), input, onError, consumer);
    }

    public String buildPrompt(String query, List<FragmentModel> fragmentModelList) {
        AtomicInteger chunkId = new AtomicInteger(1);
        List<String> fragmentContentList = DefaultUtil.defaultList(fragmentModelList).stream()
                .map(f -> String.format("chunk%d\n%s", chunkId.getAndIncrement(), f.getContent()))
                .limit(aiChatConfig.fragmentSize).collect(Collectors.toList());
        String knowledgePrompt = String.join("\n", fragmentContentList);
        if (StringUtils.isNotBlank(knowledgePrompt)) {
            knowledgePrompt = String.format("%s\n%s", aiChatConfig.fragmentKnowledgePrefix, knowledgePrompt);
        }
        return String.format(aiChatConfig.fragmentPromptFormat, knowledgePrompt, query);
    }
}
