package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Wiki 权限枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-15 10:46
 */
@Getter
public enum SyncWikiAuthEnum {
    DATASET_AUTH("dataset", "知识库权限"),
    WIKI_AUTH("wiki", "学城授权");

    private String code;
    private String remark;

    SyncWikiAuthEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public static WikiStateEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WikiStateEnum value : WikiStateEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
