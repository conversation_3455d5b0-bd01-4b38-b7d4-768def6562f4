package com.sankuai.wmbdaiassistant.domain.service.feedback.impl;

import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.enums.FeedBackTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.FeedBackModel;
import com.sankuai.wmbdaiassistant.domain.repository.FeedBackRepository;
import com.sankuai.wmbdaiassistant.domain.service.feedback.FeedBackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 反馈服务
 *
 * <AUTHOR>
 * @date 2023-12-05
 */
@Service
@Slf4j
public class FeedBackServiceImpl implements FeedBackService {

    @Resource
    private FeedBackRepository feedBackRepository;

    @Override
    public void feedback(UserBo user, Integer type, Long chatRecordId, String suggestionContent) {
        FeedBackModel feedBack = new FeedBackModel();
        feedBack.setUid(user.getUid());
        feedBack.setMis(user.getMis());
        feedBack.setType(FeedBackTypeEnum.getFeedBackType(type));
        feedBack.setChatRecordId(chatRecordId);
        feedBack.setSuggestionContent(suggestionContent);
        feedBack.setFeedbackTime(new Date());
        feedBackRepository.insert(feedBack);
    }
}
