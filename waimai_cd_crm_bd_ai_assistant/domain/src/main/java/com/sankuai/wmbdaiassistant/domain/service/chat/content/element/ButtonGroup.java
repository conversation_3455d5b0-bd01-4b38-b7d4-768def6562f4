package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 按钮组
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ButtonGroup implements ContentElement {

    private final String type = ContentElementTypeEnum.BUTTON_GROUP.getCode();

    @JsonProperty("insert")
    private ButtonGroupInfo info;

    @Override
    public String toMarkdownText() {
        return info == null ? "" : JsonUtil.toJson(info.getButtons());
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ButtonGroupInfo {
        private List<ButtonInfo> buttons;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ButtonInfo {
        private String text;
        private String url;
        private String action;
        private String color;
        private String type;

        @Override
        public String toString() {
            return text;
        }
    }
}
