package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;

import com.sankuai.wmbdaiassistant.domain.repository.query.TraceLogQuery;
import java.util.List;

/**
 * 埋点日志
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-28 17:12
 */
public interface TraceLogRepository {

    /**
     * 新增
     *
     * @param traceLogModel traceLog
     */
    void insert(TraceLogModel traceLogModel);

    /**
     * 批量增加
     *
     * @param traceLogModelList 埋点日志
     */
    void batchInsert(List<TraceLogModel> traceLogModelList);

    /**
     * 根据会话ID查询埋点信息
     *
     * @param sessionId 会话ID
     * @return
     */
    List<TraceLogModel> findBySessionId(Long sessionId);

    /**
     * 根据用户查询埋点信息
     * 
     * @param query 查询条件
     * @return
     */
    long count(TraceLogQuery query);

}
