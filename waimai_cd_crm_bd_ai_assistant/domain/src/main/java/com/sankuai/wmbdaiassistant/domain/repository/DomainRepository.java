package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.DomainModel;
import java.util.List;
import java.util.Map;

/**
 * 知识域
 *
 * <AUTHOR>
 * @date 2024/8/23
 */
public interface DomainRepository {

    /**
     * 获取所有知识域
     * 
     * @return 所有知识域
     */
    List<DomainModel> queryAll();

    /**
     * 获取所有知识域id和域名映射
     * @return Map
     */
    Map<Long, String> getId2DomainMap();

    /**
     * 获取所有域名和知识域id映射
     * @return Map
     */
    Map<String, Long> getDomain2IdMap();

    /**
     * 查指定知识域 by id
     */
    DomainModel findById(Long id);

    /**
     * 查指定知识域 by id
     */
    List<DomainModel> findByIds(List<Long> ids);

    /**
     * 查指定知识域 by domain
     * 
     * @param domain
     * @return
     */
    DomainModel findByDomain(String domain);

    /**
     * 有效性判断
     */
    Boolean isValid(Long id);

    /**
     * 有效性判断
     */
    Boolean isValid(String domain);

    /**
     * 根据id获取domain
     * @param id
     * @return
     */
    String getDomainById(Long id);


}
