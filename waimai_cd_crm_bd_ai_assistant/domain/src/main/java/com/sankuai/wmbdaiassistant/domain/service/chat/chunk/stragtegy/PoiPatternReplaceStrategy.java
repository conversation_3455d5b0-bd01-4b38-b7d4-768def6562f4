package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * POI模式替换策略
 * 用于处理POI相关的文本替换，如商家信息、地址等
 * 这是一个定制化的处理逻辑，不依赖template，直接根据value生成对应平台的展示内容
 */
@Slf4j
@Component
public class PoiPatternReplaceStrategy implements ChunkPatternReplaceStrategy {

    private static final String POI_MODE = "poi";

    @MdpConfig("poi_chunk_pattern_replace_map:{}")
    private HashMap<String, String> chunkPatternReplaceMap;

    @Override
    public boolean match(String mode) {
        return StringUtils.isNotBlank(mode) && mode.startsWith(POI_MODE);
    }

    @Override
    public String replace(String mode, String value, SessionSourceEntity sessionSourceEntity) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        String templateKey = sessionSourceEntity.getPlatformCode() + "_" + mode;
        String template = chunkPatternReplaceMap.get(templateKey);

        if (StringUtils.isBlank(template)) {
            return  value;
        }
        return template.replace("${value}", value);
    }
}   