package com.sankuai.wmbdaiassistant.domain.service.gray.impl;

import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.model.GrayModel;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.repository.PhraseRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.user.WmUserService;
import com.sankuai.wmbdaiassistant.domain.service.gray.GrayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 灰度服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/6 21:08
 */
@Slf4j
@Service
public class GrayServiceImpl implements GrayService {

    @Resource
    private WmUserService wmUserService;

    @Resource
    protected AiChatConfig aiChatConfig;

    @Resource
    protected PhraseRepository phraseRepository;

    @Override
    public boolean isGray(GrayModel grayModel, UserBo user) {
        if (grayModel == null) {
            return false;
        }

        if (grayModel.getGraySwitch() == null || !grayModel.getGraySwitch()) {
            return false;
        }

        if (grayModel.getForceAll() != null && grayModel.getForceAll()) {
            return true;
        }

        if (user == null) {
            return false;
        }
        int uid = user.getUid();
        String mis = user.getMis();
        List<String> whiteMisList = grayModel.getWhiteMisList();
        if (CollectionUtils.isNotEmpty(whiteMisList) && StringUtils.isNotBlank(mis)
                && whiteMisList.stream().anyMatch(mis::equalsIgnoreCase)) {
            return true;
        }

        Set<Integer> orgIdList = wmUserService.getOwnerOrgListByUid(uid);
        List<Integer> grayOrgIdList = grayModel.getWhiteOrgIdList();
        if (CollectionUtils.isNotEmpty(grayOrgIdList) && CollectionUtils.isNotEmpty(orgIdList)
                && grayOrgIdList.stream().anyMatch(orgIdList::contains)) {
            return true;
        }

        return false;
    }

    @Override
    public AiChatConfig.PhraseGrayModel getPhraseGrayModel(PhraseModel phraseModel, UserBo userBo) {
        if (phraseModel == null || userBo == null) {
             return null;
        }

        // 先假设该问法的phraseId就是标准问的phraseId，如果不是，需要查询后重新赋值
        Long standardPhraseId = phraseModel.getId();
        log.info("GrayServiceImpl.getPhraseGrayModel,standardPhraseIdConvertBefore: {}", standardPhraseId);
        if (!Objects.equals(phraseModel.getPhrase(), phraseModel.getStandardizedPhrase())) {
            List<PhraseModel> phraseModelList = phraseRepository.findByStandardizedPhrase(phraseModel.getStandardizedPhrase(), phraseModel.getDomainId());
            List<PhraseModel> filteredList = phraseModelList.stream()
                    .filter(phrase -> StringUtils.isNotBlank(phrase.getPhrase())
                            && StringUtils.isNotBlank(phrase.getStandardizedPhrase())
                            && Objects.equals(phrase.getPhrase(), phrase.getStandardizedPhrase()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredList)) {
                standardPhraseId = filteredList.get(0).getId();
            }
        }
        log.info("GrayServiceImpl.getPhraseGrayModel,standardPhraseIdConvertAfter: {}", standardPhraseId);

        if (MapUtils.isNotEmpty(aiChatConfig.phraseGrayMap)
                && aiChatConfig.phraseGrayMap.containsKey(standardPhraseId)) {
            AiChatConfig.PhraseGrayModel phraseGrayModel = aiChatConfig.phraseGrayMap.get(standardPhraseId);
            if (isGray(phraseGrayModel.getGray(), userBo)) {
                return phraseGrayModel;
            }
        }

        return null;
    }

}
