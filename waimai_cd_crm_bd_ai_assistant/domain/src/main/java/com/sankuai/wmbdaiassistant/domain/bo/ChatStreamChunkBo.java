package com.sankuai.wmbdaiassistant.domain.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 调用三方服务的流式消息片段
 * @author: fengxin21
 * @create: 2025/4/16
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatStreamChunkBo {
    /**
     * 当前片段内容
     */
    @JsonProperty("answer")
    private String answer = "";

    /**
     * 是否为最后一个片段
     */
    @JsonProperty("finished")
    private boolean finished = true;

    /**
     * 事件类型（如：chunk, error, done）
     */
    @JsonProperty("eventType")
    private String eventType = "chunk";

    /**
     * 引用来源
     */
    @JsonProperty("references")
    private List<FragmentReferenceBo> references = new ArrayList<>();

    /**
     * 埋点信息
     */
    @JsonProperty("tags")
    private Map<String,Object> tags = new HashMap<>();

    /**
     * 完整内容
     */
    private String fullContent = "";
}
