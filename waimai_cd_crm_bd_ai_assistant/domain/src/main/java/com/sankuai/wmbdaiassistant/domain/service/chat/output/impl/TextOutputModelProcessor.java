package com.sankuai.wmbdaiassistant.domain.service.chat.output.impl;

import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.enums.OutputTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.output.OutputModelProcessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 文本类型输出处理
 * @create 2024/4/23 11:41
 */

@Component
public class TextOutputModelProcessor implements OutputModelProcessor {
    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Override
    public boolean isMatch(OutputModel outputModel) {
        return OutputTypeEnum.TEXT.equals(outputModel.getType());
    }

    @Override
    public void processOutputToAnswer(OutputModel outputModel, GeneralAnswerBo answer, String params) {
        answer.setAnswer(this.getContentByOutput(outputModel, params));
        answer.setTags(outputModel.getTags());
    }

    @Override
    public String getContentByOutput(OutputModel outputModel, String params) {
        String resp = outputModel.getContent();
        if (StringUtils.isNotBlank(outputModel.getTtUrl())) {
            resp = chatContentConverter.merge(outputModel.getContent()
                    , chatContentConverter.buildTtUrl(outputModel.getTtUrl()));
        }

        return resp;
    }
}
