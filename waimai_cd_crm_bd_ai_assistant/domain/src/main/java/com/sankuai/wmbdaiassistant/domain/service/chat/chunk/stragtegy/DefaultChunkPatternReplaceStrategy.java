package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy;

import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;

import org.springframework.stereotype.Component;

/**
 * 默认文本块模式替换策略
 * 实现基本的模板变量替换功能
 */
@Component
public class DefaultChunkPatternReplaceStrategy implements ChunkPatternReplaceStrategy {
    @Override
    public boolean match(String mode) {
        // 默认策略不匹配任何特定模式，作为兜底策略
        return false;
    }

    @Override
    public String replace(String mode, String value, SessionSourceEntity sessionSourceEntity) {
        return value;
    }
} 