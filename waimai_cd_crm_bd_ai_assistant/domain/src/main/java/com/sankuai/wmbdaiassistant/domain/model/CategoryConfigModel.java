package com.sankuai.wmbdaiassistant.domain.model;

import lombok.AllArgsConstructor;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import java.io.IOException;
import java.text.ParseException;
import lombok.extern.slf4j.Slf4j;
import lombok.NoArgsConstructor;

/**
 * 目录配置子项模型
 * 问法：
 * - content = Phrase表phrase
 * - abilityType = AbilityTypeEnum.GENERAL
 * 
 * 目录：
 * - content = Category表name
 * - abilityType = AbilityTypeEnum.CATEGORY
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/29 14:25
 */
@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryConfigModel {
    private String content;
    private Integer abilityType;
    private Long domainId;
    private boolean highlight;

    public String Signature() {
        return String.format("%s-%s-%s", DefaultUtil.defaultString(abilityType), DefaultUtil.defaultString(domainId),
                DefaultUtil.defaultString(content));
    }
}
