package com.sankuai.wmbdaiassistant.domain.service.chat.dataset;

import java.util.List;

/**
 * 知识库权限服务接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/6 下午2:31
 */
public interface DatasetAuthService {

    /**
     * 获取用户可见的知识库
     *
     * @param uid 员工ID
     * @return
     */

    List<Long> queryUserVisibleDataset(Long uid);

    /**
     * 获取用户可见的知识库
     *
     * @param uid 员工ID
     * @return
     */
    List<Long> queryUserVisibleDataset(Integer uid);


    /**
     * 获取用户可见的知识库
     * @param mis 员工mis
     * @return
     */
    List<Long> queryUserVisibleDataset(String mis);
}
