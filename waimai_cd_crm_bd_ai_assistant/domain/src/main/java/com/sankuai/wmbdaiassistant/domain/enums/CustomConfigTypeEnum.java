package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 自定义配置枚举
 *
 * <AUTHOR> <yuhao<PERSON>@meituan.com>
 * @date 2025-04-17 10:31
 */
@Getter
public enum CustomConfigTypeEnum {

    TOOL_BAR_ORDER("toolbarOrder", "工具栏顺序配置"),
    ;

    private String code;
    private String desc;

    CustomConfigTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CustomConfigTypeEnum getByCode(String code) {
        for (CustomConfigTypeEnum customConfigTypeEnum : values()) {
            if (customConfigTypeEnum.getCode().equals(code)) {
                return customConfigTypeEnum;
            }
        }
        return null;
    }


}
