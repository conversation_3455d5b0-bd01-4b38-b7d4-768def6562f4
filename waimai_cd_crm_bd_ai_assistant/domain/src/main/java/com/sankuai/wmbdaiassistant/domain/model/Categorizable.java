package com.sankuai.wmbdaiassistant.domain.model;

import java.util.Date;

/**
 * 可分类接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/24 17:40
 */
public interface Categorizable {

    /**
     * 获取ID
     * @return
     */
    Long getId();
    /**
     * 获取父目录ID
     * @return
     */
    Long getCategoryId();

    /**
     * 获取排序位次
     * @return
     */
    Integer getSortOrder();

    /**
     * 获取修改时间
     * 
     * @return
     */
    Date getModifyTime();

    /**
     * 设置目录id
     */
    void setCategoryId(Long categoryId);

    /**
     * 设置排序位次
     */
    void setSortOrder(Integer sortOrder);
}
