package com.sankuai.wmbdaiassistant.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import java.util.Date;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 知识目录
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/23 19:06
 */
@Data
public class CategoryModel implements Categorizable {
    /**
     *    id
     */
    private Long id;

    /**
     *   父目录id,根节点父目录为虚拟节点0
     */
    private Long parentId;

    /**
     *   节点路径(0-id-id),父节点路径-id
     */
    private String path;

    /**
     *   目录别名
     */
    private String name;

    /**
     *   挂载目录的排序
     */
    private Integer sortOrder;

    /**
     *   说明: 是否有效，1：有效，0:无效
     */
    private ValidEnum valid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 标签信息 json格式
     */
    private Extra extra;

    public CategoryModel() {
        extra = new Extra();
    }

    @Data
    public static class Extra {
        @JsonProperty("ttCategory")
        private String ttCategory;
    }

    @Override
    public Long getCategoryId() {
        return parentId;
    }

    @Override
    public void setCategoryId(Long categoryId) {
        this.parentId = categoryId;
    }

    public String getFullPath() {
        return StringUtils.join(path, String.valueOf(id), "-");
    }

    public void setTTCategory(String ttCategory) {
        if (Objects.isNull(extra)) {
            extra = new Extra();
        }
        extra.setTtCategory(StringUtils.isNotBlank(ttCategory) ? ttCategory : null);
    }

    public String getTTCategory() {
        if (Objects.isNull(extra)) {
            return null;
        }
        return extra.getTtCategory();
    }
}
