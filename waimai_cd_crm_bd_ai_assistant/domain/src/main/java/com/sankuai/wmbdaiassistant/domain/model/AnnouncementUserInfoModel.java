package com.sankuai.wmbdaiassistant.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 公告用户信息模型
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnouncementUserInfoModel {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 公告ID
     */
    private Long announcementId;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 用户MIS
     */
    private String mis;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 状态 1-显示 0-关闭
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 租户ID
     */
    private Long tenantId;
}