package com.sankuai.wmbdaiassistant.domain.service.gray;

import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.model.GrayModel;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;

/**
 * 灰度服务接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/6 21:07
 */
public interface GrayService {

    /**
     * 判断是否灰度
     * @param grayModel 灰度模型
     * @param userBo    用户信息
     * @return true-灰度，false-非灰度
     */
    boolean isGray(GrayModel grayModel, UserBo userBo);

    /**
     * 判断问法回答修改是否灰度
     * @param phraseModel 问法模型
     * @param userBo      用户信息
     * @return 如果能查到相应问法信息且开灰度，返回修改后的问法回答内容，否则返回null
     */
    AiChatConfig.PhraseGrayModel getPhraseGrayModel(PhraseModel phraseModel, UserBo userBo);
}
