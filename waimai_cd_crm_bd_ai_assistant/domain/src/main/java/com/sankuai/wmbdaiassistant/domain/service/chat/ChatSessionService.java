package com.sankuai.wmbdaiassistant.domain.service.chat;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.enums.SessionStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.SessionModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface ChatSessionService {

    SessionModel createSession(UserBo user);

    SessionModel createSession(UserBo user, String source, String extra);

    void closeSession(SessionBo sessionBo, SessionStatusEnum sessionStatus);

    SessionModel insert(UserBo user);

    List<SessionModel> pageValidSession(Long minId, Integer pageSize);

    void updateSessionStatus(SessionModel session, Integer status);

    /**
     * 更新 SessionBo 信息
     *
     * @param sessionBo 当前的SessionBo
     * @param runnable  执行更新操作
     */
    void updateSessionBo(SessionBo sessionBo, Runnable runnable);
}
