package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentBuilder;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.LlmRag;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 大象上的基于分片的rag策略
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-01 14:17
 */
@Slf4j
@Component("dxFragmentRagStrategy")
public class DxFragmentRagStrategy extends AbsRagStrategy {

    @Resource
    private Workflow defaultWorkflow;

    @Resource
    private LlmRag defaultFragmentLlmRag;

    @MdpConfig("dx.allow.task.list:[4]")
    private ArrayList<Long> allowTaskIdList;

    @MdpConfig("dx.allow.content.element.list:[\"text\",\"markdown\",\"link\",\"image\",\"video\"]")
    private ArrayList<String> allowContentElementList;

    @Override
    public void triggerQuery(SessionBo sessionBo, long id, String input, String entryPoint, GeneralCallback callback) {
        defaultFragmentLlmRag.query(sessionBo, id, input, entryPoint, wrapCallbackWithFormatTransfer(callback));
    }

    @Override
    public void triggerTask(SessionBo sessionBo, long id, Long bizId, String input
            , String entryPoint, GeneralCallback callback, String version) {
        GeneralCallback wrapCallback = wrapCallbackWithFormatTransfer(callback);
        if (CollectionUtils.isNotEmpty(allowTaskIdList) && allowTaskIdList.contains(sessionBo.fetchTaskId())) {
            defaultWorkflow.trigger(sessionBo, id, bizId, input, entryPoint, wrapCallbackWithFormatTransfer(wrapCallback), version);
        } else {
            defaultTaskAnswer(id, wrapCallback);
        }
    }

    private GeneralCallback wrapCallbackWithFormatTransfer(GeneralCallback callback) {
        return answerBo -> {
            List<ContentElement> elementList =  chatContentConverter.extractComponentFromText(answerBo.getAnswer());
            String answer = chatContentConverter.componentToMarkdownText(DefaultUtil.defaultList(elementList).stream()
                    .filter(element -> allowContentElementList.contains(element.getType()))
                    .collect(Collectors.toList()));
            answerBo.setAnswer(answer);
            callback.answerCallback(answerBo);
        };
    }

    private void defaultTaskAnswer(Long id, GeneralCallback callback) {
        GeneralAnswerBo answer = new GeneralAnswerBo();

        answer.setMsgId(id);
        answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        answer.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());

        ChatContentBuilder chatContentBuilder = new ChatContentBuilder();
        chatContentBuilder.add(chatContentConverter.buildMarkdown(aiChatConfig.defaultTaskMessage));
        answer.setAnswer(chatContentBuilder.build());

        callback.answerCallback(answer);
    }

}
