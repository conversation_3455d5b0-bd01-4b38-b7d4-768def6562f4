package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.common.UrlUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.repository.ChatMsgRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.transaction.TransactionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/6
 **/
@Slf4j
@Component
public class AbilityFactory {

    @Resource
    private List<BaseAbility> abilityList;

    @Resource
    private TransactionHelper transactionHelper;

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @Resource
    private ChatRecordService chatRecordService;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private AbilityConfig abilityConfig;

    @Resource
    private SessionSourceConfigManager sessionSourceConfigManager;

    private final Map<AbilityTypeEnum, BaseAbility> typeBaseAbilityMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        abilityList.forEach(baseAbility -> typeBaseAbilityMap.put(baseAbility.getAbilityType(), baseAbility));
    }

    public Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo) {
        AbilityTypeEnum abilityType = AbilityTypeEnum.findByCode(chatSubmitQueryBo.getAbilityType());
        return typeBaseAbilityMap.get(abilityType).submitQuery(chatSubmitQueryBo);
    }

    public ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo) {
        AbilityTypeEnum abilityType = AbilityTypeEnum.findByCode(requestBo.getAbilityType());
        ChatFetchAnswerBo chatFetchAnswerBo = typeBaseAbilityMap.get(abilityType).fetchAnswer(requestBo);
        log.info("fetchAnswer,requestBo={},chatFetchAnswerBo={}", JsonUtil.toJson(requestBo),
                JsonUtil.toJson(chatFetchAnswerBo));
        ParamCheckUtil.notNull(chatFetchAnswerBo, "AbilityFactory fetchAnswer chatFetchAnswerBo is null");
        if (AbilityTypeEnum.GUIDANCE == abilityType) {
            return chatFetchAnswerBo;
        }
        if (abilityConfig.urlReplaceSwitch) {
            urlTransfer(chatFetchAnswerBo);
        }
        if (abilityConfig.ttUrlReplaceSwitch) {
            ttUrlTransfer(requestBo, chatFetchAnswerBo);
        }
        recordMsg(requestBo, chatFetchAnswerBo);
        return chatFetchAnswerBo;
    }

    private void urlTransfer(ChatFetchAnswerBo chatFetchAnswerBo) {
        if (Objects.isNull(chatFetchAnswerBo) || StringUtils.isBlank(chatFetchAnswerBo.getCurrentContent())) {
            return;
        }
        String content = chatFetchAnswerBo.getCurrentContent();
        content = StringUtil.replaceByMap(content, abilityConfig.ttUrlTransferMap);
        chatFetchAnswerBo.setCurrentContent(content);
    }

    private void ttUrlTransfer(ChatFetchAnswerRequestBo requestBo, ChatFetchAnswerBo chatFetchAnswerBo) {
        if (Objects.isNull(chatFetchAnswerBo) || StringUtils.isBlank(chatFetchAnswerBo.getCurrentContent())) {
            return;
        }
        String content = chatFetchAnswerBo.getCurrentContent();
        SessionSourceEntity sessionSourceEntity = sessionSourceConfigManager.getByCode(requestBo.getSessionBo().getSource());
        //tt链接 pc转bee
        if (sessionSourceConfigManager.getAllAppSourceCodeSet().contains(sessionSourceEntity.getCode())) {
            content = StringUtil.replaceByMap(content,
                UrlUtil.findAllPcUrl(content).stream()
                    .filter(url -> UrlUtil.isTTUrl(url, false))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toMap(url -> url, UrlUtil::ttUrlToBeeFormat)));
        }
        //tt链接 bee端转pc
        else {
            content = StringUtil.replaceByMap(content,
                UrlUtil.findAllBeeUrl(content).stream()
                    .filter(url -> UrlUtil.isTTUrl(url, true))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toMap(url -> url, UrlUtil::ttUrlToPcFormat)));
        }
        chatFetchAnswerBo.setCurrentContent(content);
    }

    private void recordMsg(ChatFetchAnswerRequestBo requestBo, ChatFetchAnswerBo chatFetchAnswerBo) {
        log.info("AbilityFactory fetchAnswer questionMsgId = {}, msgId = {}，chatFetchAnswerBoQuestionId = {}"
                , requestBo.getQuestionMsgId(), requestBo.getMsgId(), chatFetchAnswerBo.getQuestionMsgId());
        chatFetchAnswerBo.setQuestionMsgId(requestBo.getQuestionMsgId());
        if (Objects.isNull(requestBo.getMsgId()) && !chatFetchAnswerBo.isEmpty()) {
            ChatMsgModel chatMsg = chatRecordService.createAnswerMsg(chatFetchAnswerBo);
            chatFetchAnswerBo.setMsgId(chatMsg.getId());
        }
        if (Objects.nonNull(requestBo.getMsgId())) {
            ChatFetchAnswerBo cloned = chatFetchAnswerBo.clone();
            ChatMsgModel chatMsg = transactionHelper
                    .doInTransaction(() -> chatRecordService.findById(requestBo.getMsgId()));
            ChatFetchAnswerBo previous = JsonUtil.fromJson(chatMsg.getContent(), ChatFetchAnswerBo.class);
            cloned.setCurrentContent(chatContentConverter.merge(previous == null ? null : previous.getCurrentContent(),
                    chatFetchAnswerBo.getCurrentContent()));
            chatRecordService.updateAnswerMsg(requestBo.getMsgId(), cloned);
        }

        if (ChatAnswerStatusEnum.isFinished(chatFetchAnswerBo.getStatus())) {
            ChatMsgModel questionMsg = transactionHelper
                    .doInTransaction(() -> chatRecordService.findById(requestBo.getQuestionMsgId()));
            ParamCheckUtil.notNull(questionMsg, "问题不能为空");
            if (StringUtils.isNotBlank(questionMsg.getTaskSessionId())
                    || StringUtils.isNotBlank(questionMsg.getContext())) {
                ChatMsgModel answerMsg = new ChatMsgModel();
                answerMsg.setId(chatFetchAnswerBo.getMsgId());
                answerMsg.setTaskSessionId(questionMsg.getTaskSessionId());
                answerMsg.setContext(questionMsg.getContext());
                chatMsgRepository.update(answerMsg);
            } else {
                chatMsgRepository.updateTaskSessionIdAndContextNull(chatFetchAnswerBo.getMsgId());
            }
        }
    }
}
