package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 多轮中触发chat，即小蜜主流程
 *
 * <AUTHOR>
 * @date 2025-05-27 15:39
 */
@Slf4j
@Component
public class TaskTriggerChatPatternProcessor implements PatternProcessor {

    private static final String PREFIX = "TaskTriggerChat:";

    @Resource
    private ChatService chatService;

    @Override
    public boolean match(String pattern) {
        return pattern.startsWith(PREFIX);
    }

    @Override
    public boolean process(PatternParam param) {
        String pattern = param.getPattern();
        String query = pattern.substring(pattern.indexOf(PREFIX) + PREFIX.length()).trim();

        SessionBo sessionBo = param.getSession();
        sessionBo.forceRefresh(param.getMsgId());
        chatService.chat(sessionBo, sessionBo.getUid(), param.getBizId(), param.getMsgId()
                , query, param.getEntryPoint(), param.getCallback(), param.getVersion());
        return false;
    }
}
