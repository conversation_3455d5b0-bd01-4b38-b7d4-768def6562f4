package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 平台枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/12/17 10:40
 */
@Getter
public enum PlatformEnum {
    UNKNOWN("unknown", "未知平台"),
    APP("app", "APP端"),
    WEB("web", "网页端"),
    ;

    private String code;
    private String desc;

    PlatformEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PlatformEnum getByCode(String code) {
        for (PlatformEnum PlatformEnum : values()) {
            if (PlatformEnum.getCode().equals(code)) {
                return PlatformEnum;
            }
        }
        return null;
    }
}
