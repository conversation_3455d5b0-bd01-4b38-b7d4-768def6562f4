package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 表单元素
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-27 16:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Form implements ContentElement {

    private final String type = ContentElementTypeEnum.FORM.getCode();

    @JsonProperty("insert")
    private FormInfo info;

    @Override
    public String toMarkdownText() {
        return "";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormInfo {
        private FormConfig form;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormConfig {
        private List<FormConfigItem> config;
        private String buttonText;
        private String title;
        private String subTitle;
        private Double labelSpan;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormConfigItem {
        private String label;
        private String type;
        private List<String> options;
        private String defaultValue;
        private String tooltip;
        private String labelWrap;
    }
}