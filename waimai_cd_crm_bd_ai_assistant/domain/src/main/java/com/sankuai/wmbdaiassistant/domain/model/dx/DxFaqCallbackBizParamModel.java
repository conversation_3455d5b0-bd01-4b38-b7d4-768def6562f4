package com.sankuai.wmbdaiassistant.domain.model.dx;

import lombok.Data;

/**
 * 大象标准提问回调业务参数模型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/1/26 10:13
 */
@Data
public class DxFaqCallbackBizParamModel {

    /**
     * 原回复消息id
     */
    private Long msgId;

    /**
     * 大象消息ID
     */
    private Long dxMsgId;

    /**
     * 群id(没有则为单聊)
     */
    private Long gid;

    /**
     * 提问内容
     */
    private String question;

    /**
     * 消息类型 ("submitQuery", "提问"),("like", "点赞"),("dislike", "点踩")
     */
    private String type;

    /**
     * 回调创建时间
     */
    private long cts;
}
