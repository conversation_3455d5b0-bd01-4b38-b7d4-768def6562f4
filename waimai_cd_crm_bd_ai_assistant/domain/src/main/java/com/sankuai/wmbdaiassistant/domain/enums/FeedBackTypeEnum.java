package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-05
 */
public enum FeedBackTypeEnum {
    LIKE(1, "点赞"),

    DISLIKE(2, "点踩"),

    BLOCK_ANSWER(3, "阻断回答"),

    CLICK_TT(4, "点击TT"),

    OTHER(999, "其他");

    private Integer code;
    private String desc;

    FeedBackTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static FeedBackTypeEnum getFeedBackType(Integer code) {
        if (code == null) {
            return null;
        }
        for (FeedBackTypeEnum type : FeedBackTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static FeedBackTypeEnum findByDesc(String desc) {
        return Arrays.stream(FeedBackTypeEnum.values()).filter(e -> e.getDesc().equals(desc))
            .findFirst().orElse(null);
    }

    public static boolean isLike(Integer code) {
        return Objects.equals(LIKE.getCode(), code);
    }

    public static boolean isDislike(Integer code) {
        return Objects.equals(DISLIKE.getCode(), code);
    }

    public static boolean isBlock(Integer code) {
        return Objects.equals(BLOCK_ANSWER.getCode(), code);
    }
}
