package com.sankuai.wmbdaiassistant.domain.enums;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.sankuai.wmbdaiassistant.domain.model.LLMTypeModel;
import lombok.Getter;

import java.util.List;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
@Getter
public enum AnswerTypeEnum {
    STANDARD_FAQ(1,"标准回答", "standard_faq"),
    RELATIVE_FAQ(2,"相关问题", "relative_faq"),
    AI_CHAT_CHATGLM3(3,"AI-chatglm3", "ai_chat"),
    AI_CHAT_CHATGPT3(4,"AI-chatgpt3.5", "ai_chat"),
    AI_CHAT_LONGCAT_7B(5, "AI-Longcat-7B", "ai_chat"),
    AI_CHAT_LONGCAT_70B(6, "AI-Longcat-70B", "ai_chat"),
    AI_TASK_CHATGLM3(7, "【多轮】通过 chatglm3 模型的产出（对用户不展示）", "ai_task"),
    AI_TASK_CHATGPT3(8, "【多轮】通过 chatgpt3.5 模型的产出（对用户不展示）", "ai_task"),
    AI_TASK_LONGCAT7B(9, "【多轮】通过 longcat-7b 模型的产出（对用户不展示）", "ai_task"),
    AI_TASK_LONGCAT70B(10, "【多轮】通过 longcat-70b 模型的产出（对用户不展示）", "ai_task"),
    SYSTEM(11, "通过接口调用生成的展示数据（需要对用户展示）", "system"),
    REARRANGE(12, "算法侧的补召及重排方法", "rearrange"),
    API_EMBEDDING(13, "API接口调用时返回的嵌套模式", "api_embedding"),
    AI_TASK_CHATGPT4(14, "【多轮】通过 chatgpt4 模型的产出（对用户不展示）", "ai_task"),
    AI_TASK_LONGCAT_32K(15, "【多轮】通过 longcat-32k 模型的产出（对用户不展示）", "ai_task"),
    AI_CHAT_LONGCAT_32K(16, "AI-Longcat-32k", "ai_chat"),
    CATEGORY_OPTION_LIST(17, "目录选项", "category_option_list"),
    AI_CHAT(18,"所有AI-chat", "ai_chat"),
    AI_TASK(19, "所有AI-task", "ai_task"),
    FRAGMENT_REARRANGE(20, "片段重排", "fragment_rearrange"),
    EDI(21, "edi机器人", "edi_chat"),
    ;

    private int code;
    private String desc;
    private String way;

    private static List<Integer> AI_CHAT_ANSWER_TYPE_LIST = Lists.newArrayList(
            AI_CHAT_CHATGLM3.getCode(),
            AI_CHAT_CHATGPT3.getCode(),
            AI_CHAT_LONGCAT_7B.getCode(),
            AI_CHAT_LONGCAT_70B.getCode(),
            AI_CHAT_LONGCAT_32K.getCode(),
            REARRANGE.getCode()
    );

    private static List<Integer> AI_TASK_ANSWER_TYPE_LIST = Lists.newArrayList(
            AI_TASK_CHATGLM3.getCode(),
            AI_TASK_CHATGPT3.getCode(),
            AI_TASK_LONGCAT7B.getCode(),
            AI_TASK_LONGCAT70B.getCode(),
            AI_TASK_LONGCAT_32K.getCode(),
            AI_TASK_CHATGPT4.getCode()
    );

    AnswerTypeEnum(int code, String desc, String way) {
        this.code = code;
        this.desc = desc;
        this.way = way;
    }

    public static List<Integer> getAiChatAnswerTypeList() {
        return AI_CHAT_ANSWER_TYPE_LIST;
    }

    public static List<Integer> getAiTaskAnswerTypeList() {
        return AI_TASK_ANSWER_TYPE_LIST;
    }

    public static AnswerTypeEnum of(Integer code) {
        if (code == null) {
            return null;
        }
        for (AnswerTypeEnum answerTypeEnum : values()) {
            if (answerTypeEnum.code == code) {
                return answerTypeEnum;
            }
        }
        return null;
    }

    public static AnswerTypeEnum of(String way) {
        if (way == null) {
            return null;
        }
        if (Objects.equal(AI_CHAT.getWay(), way)) {
            return AI_CHAT;
        }
        if (Objects.equal(AI_TASK.getWay(), way)) {
            return AI_TASK;
        }
        for (AnswerTypeEnum answerTypeEnum : values()) {
            if (Objects.equal(answerTypeEnum.way, way)) {
                return answerTypeEnum;
            }
        }
        return null;
    }

    public static AnswerTypeEnum getChatAnswerTypeByLlmType(LLMTypeModel LLMTypeModel) {
        if (LLMTypeModel == null) {
            return null;
        }
        return AnswerTypeEnum.of(LLMTypeModel.getChatAnswerTypeCode());
    }

    public static AnswerTypeEnum getTaskAnswerTypeByLlmType(LLMTypeModel LLMTypeModel) {
        if (LLMTypeModel == null) {
            return null;
        }
        return AnswerTypeEnum.of(LLMTypeModel.getTaskAnswerTypeCode());
    }
}
