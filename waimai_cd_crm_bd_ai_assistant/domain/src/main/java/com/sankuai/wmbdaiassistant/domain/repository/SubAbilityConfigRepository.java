package com.sankuai.wmbdaiassistant.domain.repository;

import java.util.List;

import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface SubAbilityConfigRepository {

    /**
     * 分页查询
     *
     * @param abilityType 能力类型
     * @param page 分页参数
     * @return
     */
    List<SubAbilityConfigModel> findConfigByPage(int abilityType, Page page);

    /**
     * 条件统计
     *
     * @param abilityType 能力
     * @return 个数
     */
    Long countConfigByPage(int abilityType);

}
