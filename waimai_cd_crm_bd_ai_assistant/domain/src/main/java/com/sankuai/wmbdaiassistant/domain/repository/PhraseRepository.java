package com.sankuai.wmbdaiassistant.domain.repository;


import com.sankuai.wmbdaiassistant.domain.enums.PhraseStateEnum;
import com.sankuai.wmbdaiassistant.domain.enums.TriggerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.model.PhraseRelModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.PhraseQuery;

import java.util.List;
import java.util.Map;

/**
 * 问法仓储
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-05 15:34
 */
public interface PhraseRepository {

    /**
     * 增加
     *
     * @param phraseModel 问法
     * @return 主键ID
     */
    Long insert(PhraseModel phraseModel);

    /**
     * 批量添加
     *
     * @param phraseModelList 问法模型List
     * @return ID List
     */
    List<Long> batchInsert(List<PhraseModel> phraseModelList);

    /**
     * 更新
     *
     * @param phraseModel phrase
     */
    void update(PhraseModel phraseModel);

    /**
     * 更新phrase表数据
     *
     * @param phraseModelList
     */
    int batchUpdateByIdSelective(List<PhraseModel> phraseModelList);

    /**
     * 批量更新状态
     *
     * @param idList ID列表
     * @param state 状态
     * @return 成功更新数量
     */
    int batchUpdateStatuteByIds(List<Long> idList, PhraseStateEnum state);

    /**
     * 根据ID查询
     *
     * @param phraseId
     * @return
     */
    PhraseModel findById(Long phraseId);

    /**
     * 根据ID查询
     *
     * @param phraseIdList
     * @return
     */
    List<PhraseModel> findByIdList(List<Long> phraseIdList);

    /**
     * 根据问法查询（标准问和扩展问）
     *
     * @param phrase 问法
     * @param domainId 域ID
     * @return
     */
    PhraseModel findByPhrase(String phrase, Long domainId);

    /**
     * 根据问法查询（标准问和扩展问）
     * 指定PhraseStateEnum
     *
     * @param phrase
     * @return
     */
    PhraseModel findByPhrase(String phrase, Long domainId, PhraseStateEnum stateEnum);

    /**
     * 批量根据问法查询（标准问和扩展问）
     * 多种PhraseStateEnum
     *
     * @param phrase
     * @return
     */
    List<PhraseModel> findByPhrase(String phrase, Long domainId, List<PhraseStateEnum> stateEnumList);

    /**
     * 根据标准问法查询（标准问和扩展问）
     *
     * @param standardizedPhrase 标准问
     * @return
     */
    List<PhraseModel> findByStandardizedPhrase(String standardizedPhrase, Long domainId);

    /**
     * 根据标准问法查询（标准问和扩展问）
     *
     * @param standardizedPhrase 标准问
     * @return
     */
    List<PhraseModel> findByStandardizedPhrase(String standardizedPhrase, Long domainId, PhraseStateEnum stateEnum);

    /**
     * 根据标准问法查询扩展问（扩展问）
     *
     * @param pageNum  页码，从1开始
     * @param pageSize 数量
     * @param standardizedPhrase 标准问
     * @param domainId 域id 注意：null是一种类型，️约定domainId为-1时查询所有。
     * @return
     */
    List<PhraseModel> findExtendByStandardizedPhrasePage(String standardizedPhrase, Long domainId, int pageNum,
            int pageSize);

    /**
     * 根据标准问法查询扩展问个数（扩展问）
     *
     * @param standardizedPhrase 标准问
     * @param domainId           域id 注意：null是一种类型，️约定domainId为-1时查询所有。
     * @return
     */
    long countExtendByStandardizedPhrasePage(String standardizedPhrase, Long domainId);

    /**
     * 分页查询扩展问
     *
     * @param pageNum 分页号，从1开始
     * @param pageSize 单页数
     * @param orderByClause 排序信息
     * @return
     */
    List<PhraseRelModel> findExtendPhrasePage(int pageNum, int pageSize, String orderByClause);

    /**
     * 分页查询扩展问
     *
     * @return
     */
    Long countExtendPhrasePage();

    /**
     * 分页查询标准问
     *
     * @param standardPhrase 问法（模糊匹配）
     * @param domainId 域Id 注意：null是一种类型，️约定domainId为-1时查询所有。
     * @param type 类型
     * @param pageNum 分页号，从1开始
     * @param pageSize 单页数
     * @param orderByClause 排序信息
     * @return
     */
    List<PhraseModel> findStandardPhrasePage(String standardPhrase, Long domainId, TriggerTypeEnum type, int pageNum,
            int pageSize, String orderByClause);

    /**
     * 查询标准问个数
     *
     * @param standardPhrase 问法（模糊匹配）
     * @param domainId       域id 注意：null是一种类型，️约定domainId为-1时查询所有。
     * @param type 类型
     * @return
     */
    Long countStandardPhrasePage(String standardPhrase, Long domainId, TriggerTypeEnum type);

    /**
     * 分页全量查询有效的 phrase
     *
     * @return
     */
    List<PhraseModel> findAllEnable(int pageIndex, int pageSize);

    /**
     * 查询全量
     *
     * @return
     */
    long countAllEnable();

    /**
     * 根据vex平台唯一id查询
     *
     * @param vexId
     * @return
     */
    PhraseModel findByVexId(Long vexId);

    /**
     * 批量根据vexId查
     * @param vexIds
     * @return
     */
    List<PhraseModel> findByVexIdList(List<Long> vexIds);

    /**
     * 校验 phrase 是否重复
     *
     * @param phrase 问法
     * @param domainId 域id
     * @return 是否重复
     */
    boolean checkPhraseRepeat(String phrase, Long domainId);

    /**
     * 通过触发器id 查相关phrase 非删除状态
     */
    List<PhraseModel> findByTriggerId(Long triggerId,TriggerTypeEnum type);

    /**
     * 通过触发器id 查标准问 不限状态
     * @param triggerId
     * @return
     */
    PhraseModel findStandardPhraseByTriggerId(Long triggerId,TriggerTypeEnum type);

    /**
     * 搜索指定域下的所有标准问法(标准问) 默认ENABLE
     *
     * @param standardPhraseList 标准问集合（完全匹配）
     * @param domainIdList       域集合 空查所有域。
     * @return Map<标准问tag ， 标准问> 标准问tag = 域ID + 标准问名
     */
    Map<String, PhraseModel> findStandardByStandardPhrases(List<String> standardPhraseList, List<Long> domainIdList);

    /**
     * 统计所有状态下的问法数量(包含启用，删除，禁用)
     * @return
     */
    long countAllStatePhrase();

    /**
     * 分页查询所有状态下的问法(包含启用，删除，禁用)
     * @param pageIndex
     * @param pageSize
     * @return
     */
    List<PhraseModel> findAllStatePhraseByPage(int pageIndex, int pageSize);

    /**
     * 基于单个目录查询
     * 
     * @return
     */
    List<PhraseModel> findByCategoryId(Long categoryId);

    /**
     * 基于单个目录查询并限制排序位次
     * 
     * @return
     */
    List<PhraseModel> findByCategoryId(Long categoryId, Integer minSortOrder, Integer maxSortOder);

    /**
     * 基于多个目录查询
     * @param categoryIdList
     * @return
     */
    List<PhraseModel> findByCategoryIdList(List<Long> categoryIdList);

    /**
     * 统计指定目录下的问法数量
     * @param categoryIdList
     * @return
     */
    long countByCategoryIdList(List<Long> categoryIdList);

    /**
     *
     * @param query
     * @return
     */
    List<PhraseModel> findByQuery(PhraseQuery query);
}
