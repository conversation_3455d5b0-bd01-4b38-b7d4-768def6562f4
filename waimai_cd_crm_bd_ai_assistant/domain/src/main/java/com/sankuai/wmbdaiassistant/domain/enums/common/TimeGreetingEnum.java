package com.sankuai.wmbdaiassistant.domain.enums.common;

import java.time.LocalTime;
import lombok.Getter;

/**
 * @description:
 * @author: fengxin21
 * @create: 2025/4/17
 **/
@Getter
public enum TimeGreetingEnum {
    MORNING("早上好", LocalTime.of(6, 0), LocalTime.of(11, 59)),
    NOON("下午好", LocalTime.of(12, 0), LocalTime.of(17, 59)),
    EVENING("晚上好", LocalTime.of(18, 0), LocalTime.of(23, 59)),
    OTHER("你好", LocalTime.of(0, 0), LocalTime.of(5, 59));

    private String greeting;
    private LocalTime startTime;
    private LocalTime endTime;

    TimeGreetingEnum(String greeting, LocalTime startTime, LocalTime endTime) {
        this.greeting = greeting;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public static TimeGreetingEnum now() {
        LocalTime now = LocalTime.now();
        for (TimeGreetingEnum timeOfDay : TimeGreetingEnum.values()) {
            if (now.isAfter(timeOfDay.getStartTime()) && now.isBefore(timeOfDay.getEndTime())) {
                return timeOfDay;
            }
        }
        return OTHER;
    }

}
