package com.sankuai.wmbdaiassistant.domain.service.chat.impl;

import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.output.OutputModelProcessor;
import com.sankuai.wmbdaiassistant.domain.service.chat.OutputService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024/4/28 19:30
 */

@Service
public class OutputServiceImpl implements OutputService {
    @Resource
    private List<OutputModelProcessor> outputModelProcessors;

    @Override
    public void processOutputToAnswer(OutputModel outputModel, GeneralAnswerBo answer, String params) {
        for (OutputModelProcessor outputModelProcessor : outputModelProcessors) {
            if (outputModelProcessor.isMatch(outputModel)) {
                outputModelProcessor.processOutputToAnswer(outputModel, answer, params);
            }
        }
    }

    @Override
    public String getContentByOutput(OutputModel outputModel, String params) {
        String content = null;

        for (OutputModelProcessor outputModelProcessor : outputModelProcessors) {
            if (outputModelProcessor.isMatch(outputModel)) {
                content = outputModelProcessor.getContentByOutput(outputModel, params);
                break;
            }
        }

        return content;
    }
}
