package com.sankuai.wmbdaiassistant.domain.service.chat;

import java.util.List;

import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;
import com.sankuai.wmbdaiassistant.domain.repository.Page;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface SubAbilityConfigService {

    List<SubAbilityConfigModel> selectConfig(AbilityTypeEnum abilityType, Page page);

    Long countPageConfig(AbilityTypeEnum abilityType);
}
