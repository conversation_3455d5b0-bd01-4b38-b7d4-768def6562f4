package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 *
 *   表名: bd_ai_assistant_dataset
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DatasetModel {
    /**
     *   说明: 主键
     */
    private Long id;

    /**
     *   说明: 知识库
     */
    private String name;

    /**
     *   说明: 描述
     */
    private String desc;

    /**
     *   说明: 业务线
     */
    private String bizline;

    /**
     *   说明:  是否有效 0-无效 1-有效
     */
    private ValidEnum valid;

    /**
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   说明: 组织节点id逗号分割字符串
     */
    private List<Long> authOrgs;

    /**
     *   说明: HR人员id逗号分割字符串
     */
    private List<Long> authUids;
}