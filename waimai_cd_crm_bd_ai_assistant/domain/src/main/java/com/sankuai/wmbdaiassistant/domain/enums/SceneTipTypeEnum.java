package com.sankuai.wmbdaiassistant.domain.enums;

/**
 * @description: 场景提示语-场景类型
 * @author: maningning03
 * @create: 2025/4/22
 **/
public enum SceneTipTypeEnum {

    PICTURE("picture", "照片"),
    ;

    private String code;
    private String desc;

    SceneTipTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SceneTipTypeEnum findByCode(String code) {
        for (SceneTipTypeEnum type : SceneTipTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
