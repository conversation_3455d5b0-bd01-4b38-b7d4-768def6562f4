package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * FAQ
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-06 15:14
 */
@Getter
@Setter
@ToString
public class FaqModel {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 问题
     */
    private String question;

    /**
     * 回答ID
     */
    private Long answerId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 是否有效
     */
    private ValidEnum valid;
}
