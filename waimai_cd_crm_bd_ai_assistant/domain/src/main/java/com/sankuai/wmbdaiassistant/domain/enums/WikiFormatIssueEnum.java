package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 类描述？
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/26 下午6:25
 */
@Getter
public enum WikiFormatIssueEnum {
    NOT_SUPPORTED_HTML("检测到html组件", "请不要使用html组件", 0),
    NOT_SUPPORTED_DRAW_IO("检测到流程图", "流程图、UML图及思维导图暂不支持解析，建议在图片下方添加摘要或删除图片", 2),
    NOT_SUPPORTED_PLANTUML("检测到UML图", "流程图、UML图及思维导图暂不支持解析，建议在图片下方添加摘要或删除图片", 2),
    NOT_SUPPORTED_MINDER("检测到思维导图", "流程图、UML图及思维导图暂不支持解析，建议在图片下方添加摘要或删除图片", 2),
    NOT_SUPPORTED_TASK_ITEM("检测到勾选框", "建议删除", 0),
    NOT_SUPPORTED_TEXT_STRIKETHROUGH("检测到文本划线", "无用内容请直接删除，不要在文本上删除线", 0),
    QUESTION_FORMAT_WRONG("检测到问题行（Q）", "问题行（Q）请使用标题6样式", 0),
    HEADING_EMPTY("检测到标题为空", "请删除空标题", 0),
    TABLE_QA_NO_HEADING("检测到表格QA对", "QA表格前需要有大标题,大标题需包含QA/FAQ字样", 0),
    TABLE_CELL_MERGE("检测到合并单元格的表格", "建议不使用合并单元格", 1),
    TABLE_NESTED_TABLE("检测到表格嵌套表格", "建议不使用合并单元格", 1),
    TABLE_NESTED_IMAGE("检测到表格嵌套图片", "建议将图片转换为普通文字描述", 1),
    TABLE_NESTED_HEADING("检测到表格嵌套标题", "建议不要在表格中使用标题层级", 0),
    ;

    /**
     * 格式问题类型
     */
    private String title;
    /**
     * 格式问题描述
     */
    private String message;
    /**
     * 格式问题等级
     */
    private int level;

    WikiFormatIssueEnum(String title, String message, int level) {
        this.title = title;
        this.message = message;
        this.level = level;
    }
}
