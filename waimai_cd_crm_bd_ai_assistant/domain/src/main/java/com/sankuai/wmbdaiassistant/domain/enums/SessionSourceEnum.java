package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 会话来源
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-07 10:55
 */
@Getter
@AllArgsConstructor
public enum SessionSourceEnum {
    /**
     * 旧版本，灰度弃用
     */
    BD_AI_ASSISTANT_BEE("bdaiassistant_bee", "BD智能助手-蜜蜂端", PlatformEnum.APP, true, 1),
    /**
     * 以下为新版本
     */
    UNKNOWN("unknown", "未知来源", PlatformEnum.UNKNOWN, false, 1),
    BEE_HOME("bee-home", "蜜蜂首页", PlatformEnum.APP, true, 1),
    BEE_MINE("bee-mine", "蜜蜂我的", PlatformEnum.APP, true, 1),
    BEE_POI_HELP("bee-poi-help", "蜜蜂-商家详情页-在线提问", PlatformEnum.APP, true, 1),
    BEE_POI_SMS("bee-poi-sms", "蜜蜂-商家未收到短信场景", PlatformEnum.APP, true, 1),
    BEE_POI_REJECT("bee-poi-reject", "蜜蜂-商家详情页-驳回协助", PlatformEnum.APP, true, 1),
    TT_WEB("tt_web", "tt网页端", PlatformEnum.WEB, true, 1),
    TT_APP("tt_app", "tt应用端", PlatformEnum.APP, true, 1),
    XIANFU_WEB("xianfu_web", "先富网页端", PlatformEnum.WEB, true, 1),
    XIANFU_APP("xianfu_app", "先富应用端", PlatformEnum.APP, true, 1),
    DAXIANG_WEB("daxiang_web", "大象网页端", PlatformEnum.WEB, true, 1),
    DAXIANG_APP("daxiang_app", "大象应用端", PlatformEnum.APP, true, 1),
    DOVE_BEE("dove_app", "信鸽-蜜蜂端", PlatformEnum.APP, true, 1),
    SYSTEM("system", "系统内部", PlatformEnum.APP, false, 1),
    DX_EDI_RPIVATE_CHAT("dx_edi_private_chat", "edi大象单聊", PlatformEnum.WEB, true, 1),
    DX_EDI_GROUP_CHAT("dx_edi_group_chat", "edi大象群聊", PlatformEnum.WEB, true, 1),
    DX_PRIVATE_CHAT("dx_private_chat", "大象单聊", PlatformEnum.WEB, true, 1),
    DX_GROUP_CHAT("dx_group_chat", "大象群聊", PlatformEnum.WEB, true, 1),
    LIANSUO_WEB("liansuo_web", "连锁网页端", PlatformEnum.WEB, true, 2),
    PERFORMANCE_WEB("performance_web", "绩效网页端", PlatformEnum.WEB, true, 1),
    WDC_DETAIL("wdc_detail", "公海详情页", PlatformEnum.APP, true, 1),
    ;

    private String code;
    private String desc;
    private PlatformEnum platform;
    private boolean display;
    private int tenantId;

    public static SessionSourceEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return UNKNOWN;
        }
        for (SessionSourceEnum sessionSourceEnum : values()) {
            if (sessionSourceEnum.getCode().equals(code)) {
                return sessionSourceEnum;
            }
        }
        return UNKNOWN;
    }

    public static int getTenantIdByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return UNKNOWN.getTenantId();
        }
        for (SessionSourceEnum sessionSourceEnum : values()) {
            if (sessionSourceEnum.getCode().equals(code)) {
                return sessionSourceEnum.getTenantId();
            }
        }
        return UNKNOWN.getTenantId();
    }

    public static Set<SessionSourceEnum> getAllAppSource() {
        Set<SessionSourceEnum> result = new HashSet<>();
        for(SessionSourceEnum sessionSourceEnum : values()) {
            if (sessionSourceEnum.getPlatform() == PlatformEnum.APP) {
                result.add(sessionSourceEnum);
            }
        }
        return result;
    }

    public static List<SessionSourceEnum> getAllDisplaySource() {
        List<SessionSourceEnum> result = new ArrayList<>();
        for (SessionSourceEnum sessionSourceEnum : values()) {
            if (sessionSourceEnum.isDisplay()) {
                result.add(sessionSourceEnum);
            }
        }
        return result;
    }
}
