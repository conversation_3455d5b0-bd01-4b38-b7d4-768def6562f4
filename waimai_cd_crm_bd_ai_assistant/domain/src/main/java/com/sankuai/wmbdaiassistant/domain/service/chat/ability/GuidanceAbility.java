package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.bo.SubAbilityConfigDataBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JumpPageTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;
import com.sankuai.wmbdaiassistant.domain.repository.Page;
import com.sankuai.wmbdaiassistant.domain.service.chat.SubAbilityConfigService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentBuilder;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/19
 **/
@Component
@Setter
public class GuidanceAbility implements BaseAbility {

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private SubAbilityConfigService subAbilityConfigService;

    @MdpConfig("jump_page_size:5")
    private int pageSize;


    @Override
    public AbilityTypeEnum getAbilityType() {
        return AbilityTypeEnum.GUIDANCE;
    }

    @Override
    public Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo) {
        // 特殊能力，不需要提问
        return null;
    }

    @Override
    public ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo) {
        Integer pageNum = requestBo.getPageNum();
        if (Objects.isNull(pageNum) || pageNum == BigDecimal.ZERO.intValue()) {
            pageNum = 1;
        }
        Page page = new Page();
        page.setPageSize(pageSize);
        page.setPageNum(pageNum);

        List<SubAbilityConfigModel> configList = subAbilityConfigService.selectConfig(AbilityTypeEnum.GUIDANCE, page);
        Long count = subAbilityConfigService.countPageConfig(AbilityTypeEnum.GUIDANCE);

        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setAbilityType(requestBo.getAbilityType());
        chatFetchAnswerBo.setHasNext(count > pageSize);
        // 前端透传返回的pageNum，因此此处返回 pageNum+1
        chatFetchAnswerBo.setPageNum(pageNum + 1);

        String content = new ChatContentBuilder().add(chatContentConverter.buildOptionsBySubAbilityConfig(configList)).build();
        chatFetchAnswerBo.setCurrentContent(content);

        return chatFetchAnswerBo;
    }
}
