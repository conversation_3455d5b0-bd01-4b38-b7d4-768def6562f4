package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.SessionModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.SessionQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface SessionRepository {

    /**
     * 新增会话
     *
     * @param session 会话
     * @return 是否成功
     */
    boolean insert(SessionModel session);

    /**
     * 更新
     *
     * @param session 会话
     * @return 是否成功
     */
    boolean update(SessionModel session);

    /**
     * 根据主键查询
     *
     * @param id 会话ID
     * @return 会话
     */
    SessionModel findById(Long id);

    /**
     * 条件查询
     *
     * @param query 查询
     * @return 会话列表
     */
    List<SessionModel> findByQuery(SessionQuery query);

    /**
     * 条件查询
     *
     * @param query 查询
     * @return 会话个数
     */
    Long countByQuery(SessionQuery query);

    /**
     * 分页查询当前活跃会话
     *
     * @param minId 最小的会话ID
     * @param page 分页参数
     * @return 活跃会话
     */
    List<SessionModel> findActiveSessionByPage(Long minId, Page page);
}
