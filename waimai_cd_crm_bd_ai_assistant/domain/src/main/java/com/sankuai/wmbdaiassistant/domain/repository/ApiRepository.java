package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.ApiModel;

/**
 * API
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 13:56
 */
public interface ApiRepository {

    /**
     * 更新
     *
     * @param apiModel 模型
     * @return 是否成功
     */
    boolean update(ApiModel apiModel);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return API
     */
    ApiModel findById(Long id);

}
