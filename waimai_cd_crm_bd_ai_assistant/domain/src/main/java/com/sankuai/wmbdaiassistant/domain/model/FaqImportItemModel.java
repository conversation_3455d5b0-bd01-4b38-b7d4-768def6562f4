package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.FaqImportStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.SubstituteEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.Data;

import java.util.Date;

/**
 * 标准问导入项
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-24 10:19
 */
@Data
public class FaqImportItemModel {

    /**
     * ID
     */
    private Long id;

    /**
     * 域id
     */
    private Long domainId;

    /**
     * 批量添加过程id
     */
    private Long processId;

    /**
     * 待添加标准问的问题
     */
    private String question;

    /**
     * 待添加标准问的回答
     */
    private String answer;

    /**
     * 待添加标准问的tt链接
     */
    private String ttUrl;

    /**
     * 相似 phrase ID
     */
    private Long similarPhraseId;

    /**
     * 是否替换相似问题
     */
    private SubstituteEnum substitute;

    /**
     * 是否有效
     */
    private ValidEnum valid;

    /**
     * 状态
     */
    private FaqImportStatusEnum status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

}
