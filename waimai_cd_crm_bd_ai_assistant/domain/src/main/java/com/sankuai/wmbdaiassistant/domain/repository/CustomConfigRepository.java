package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.CustomConfigModel;

/**
 * 自定义配置
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
public interface CustomConfigRepository {

    /**
     * 根据uid、type、tenantId查询
     *
     * @param uid 用户id
     * @param type 类型
     * @param tenantId 租户id
     * @return 模型
     */
    CustomConfigModel findByUidAndTypeAndTenantId(Integer uid, String type, Long tenantId);

    /**
     * 新增
     *
     * @param customConfigModel
     * @return
     */
    boolean insert(CustomConfigModel customConfigModel);

    /**
     * 修改
     *
     * @param customConfigModel
     * @return
     */
    boolean update(CustomConfigModel customConfigModel);
}
