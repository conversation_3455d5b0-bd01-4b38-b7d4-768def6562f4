package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 链接
 * 式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Link implements ContentElement {
    private final String type = ContentElementTypeEnum.LINK.getCode();

    private LinkAttribute attributes;

    @JsonProperty("insert")
    private String text;

    public static Link build(String text, String link) {
        return Link.builder().text(text).attributes(LinkAttribute.builder().link(link).build()).build();
    }

    @Override
    public String toString() {
        return text;
    }

    @Override
    public String toMarkdownText() {
        return String.format("[%s](%s)", DefaultUtil.defaultValue(text, "")
                , attributes == null ? "" : DefaultUtil.defaultValue(attributes.getLink(), ""));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LinkAttribute {
        private String link;
    }

    @Override
    public Map<String, String> toTtTransferContent() {
        Map<String, String> map = new HashMap<>();
        map.put("type", "link");
        map.put("name", text);
        map.put("url", attributes.getLink());
        return map;
    }
}
