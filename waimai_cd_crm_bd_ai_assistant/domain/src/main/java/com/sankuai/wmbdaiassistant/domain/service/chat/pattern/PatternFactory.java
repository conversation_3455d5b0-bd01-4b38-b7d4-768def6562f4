package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 多轮的模式工厂
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-24 16:56
 */
@Component
public class PatternFactory {

    @Resource
    private List<PatternProcessor> patternProcessors;

    public boolean canProcess(String pattern) {
        return fetchProcessor(pattern) != null;
    }

    public PatternProcessor fetchProcessor(String pattern) {
        List<PatternProcessor> filteredProcessorList = DefaultUtil.defaultList(patternProcessors).stream()
                .filter(p -> p.match(pattern)).collect(Collectors.toList());
        ParamCheckUtil.isTrue(CollectionUtils.size(filteredProcessorList) <= 1, "匹配到多个 Pattern");
        return CollectionUtils.isEmpty(filteredProcessorList) ? null : filteredProcessorList.get(0);
    }

    public boolean process(PatternParam param) {
        PatternProcessor processor = fetchProcessor(param.getPattern());
        ParamCheckUtil.notNull(processor, String.format("无匹配的 PatternProcessor：%s", param.getPattern()));
        return processor.process(param);
    }

}
