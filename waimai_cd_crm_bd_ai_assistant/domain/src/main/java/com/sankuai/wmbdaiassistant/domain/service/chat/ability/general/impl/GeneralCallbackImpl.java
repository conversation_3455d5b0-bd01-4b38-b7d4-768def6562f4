package com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.impl;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.repository.ChatMsgRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.cache.AnswerCacheService;
import com.sankuai.wmbdaiassistant.domain.service.chat.lock.DistributeLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/11
 **/
@Component
@Slf4j
public class GeneralCallbackImpl implements GeneralCallback {

    @Resource
    private AnswerCacheService answerCacheService;

    @Resource
    private DistributeLockService distributeLockService;

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @Override
    public void answerCallback(GeneralAnswerBo answerBo) {
        ChatAnswerStatusEnum chatAnswerStatus = answerCacheService.fetchStatus(answerBo.getMsgId());

        // 用户主动阻断或回答涉敏被阻断时，不需要再把消息加到待输出队列里面
        if (chatAnswerStatus != ChatAnswerStatusEnum.FINISH) {
            answerCacheService.pushToQueue(answerBo.getMsgId(), answerBo);
        }

        // 正常结束
        if (ChatAnswerStatusEnum.isFinished(answerBo.getStatus())) {
            answerCacheService.cacheStatus(answerBo.getMsgId(), ChatAnswerStatusEnum.FINISH);
            chatAnswerStatus = ChatAnswerStatusEnum.FINISH;
        }

        // 设置状态及释放会话锁
        if (chatAnswerStatus == ChatAnswerStatusEnum.FINISH) {
            ChatMsgModel chatMsgModel = chatMsgRepository.findById(answerBo.getMsgId());
            ParamCheckUtil.notNull(chatMsgModel
                    , "GeneralCallbackImpl answerCallback，but question is null, msgId = : " + answerBo.getMsgId());
            distributeLockService.releaseSessionLock(chatMsgModel.getSessionId());
        }
    }
}
