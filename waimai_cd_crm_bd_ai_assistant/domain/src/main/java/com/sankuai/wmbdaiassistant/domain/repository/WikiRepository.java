package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.enums.WikiStateEnum;
import com.sankuai.wmbdaiassistant.domain.model.WikiModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.WikiQuery;

import java.util.List;
import java.util.function.Consumer;

/**
 * wiki
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 16:56
 */
public interface WikiRepository {

    /**
     * 根据ID查询
     * @param id
     */
    WikiModel findById(String id);

    /**
     * 根据条件查询
     *
     * @param query 查询条件
     * @return
     */
    List<WikiModel> findByQuery(WikiQuery query);

    /**
     * 查询所有wiki
     * @param query
     * @return
     */
    List<WikiModel> findAll(WikiQuery query);

    /**
     * 基于batchId查询wiki集合
     *
     * @param batchId 批次id
     * @return wiki 集合
     */
    List<WikiModel> findByBatchId(String batchId);

    /**
     * 基于batchId查询wiki集合
     *
     * @param batchId       批次id
     * @param stateEnumList 状态list
     * @return wiki 集合
     */
    List<WikiModel> findByBatchId(String batchId, List<WikiStateEnum> stateEnumList);

    /**
     * 查询单个wiki
     *
     * @param batchId
     * @param wikiId
     * @return
     */
    WikiModel find(String batchId, Long wikiId);

    /**
     * 查询单个wiki
     *
     * @param datasetId
     * @param wikiId
     * @return
     */
    WikiModel findEnable(Long datasetId, Long wikiId);

    /**
     * 根据条件查询（滚动查询）
     *
     * @param query 查询条件
     * @param consumer 消费方法
     */
    void scrollQuery(WikiQuery query, Consumer<List<WikiModel>> consumer);

    /**
     * 根据条件统计
     *
     * @param query 查询条件
     * @return
     */
    Long countByQuery(WikiQuery query);

    /**
     * 基于批次统计wiki数量
     *
     * @param batchId 批次id
     * @return 数量
     */
    Long countByBatchId(String batchId);

    /**
     * 基于批次统计wiki数量
     *
     * @param batchId       批次id
     * @param stateEnumList 状态list
     * @return 数量
     */
    Long countByBatchId(String batchId, List<WikiStateEnum> stateEnumList);


    /**
     * 插入或更新
     * @param wikiModel 模型
     * @return
     */
    boolean insertOrUpdate(WikiModel wikiModel);

    /**
     * 批量插入或更新
     * @param wikiModelList 模型列表
     * @return
     */
    boolean batchInsertOrUpdate(List<WikiModel> wikiModelList);

    /**
     * 根据条件查询并按指定字段去重统计数量
     *
     * @param query 查询条件
     * @param distinctFields 需要去重的字段列表
     * @return 去重后的数量
     */
    Long countDistinctByQuery(WikiQuery query, List<String> distinctFields);
}
