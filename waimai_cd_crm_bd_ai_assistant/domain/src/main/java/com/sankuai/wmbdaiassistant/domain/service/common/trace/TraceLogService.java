package com.sankuai.wmbdaiassistant.domain.service.common.trace;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;

import java.util.List;

/**
 * 事件记录服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-28 16:54
 */
public interface TraceLogService {

    /**
     * 事件记录
     *
     * @param eventType 事件类型，例如: click
     * @param entryPoint 入口点
     * @param context 会话上下文
     * @param content 内容
     */
    void traceLog(String eventType, String entryPoint, SessionBo context, String content);

    /**
     * 增加
     *
     * @param traceLogModel 日志事件
     */
    void insert(TraceLogModel traceLogModel);

    /**
     * 批量添加
     *
     * @param traceLogModelList 日志事件
     */
    void batchInsert(List<TraceLogModel> traceLogModelList);
}
