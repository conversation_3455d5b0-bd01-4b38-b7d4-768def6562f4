package com.sankuai.wmbdaiassistant.domain.model;

import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @create 2024/4/26 11:48
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class SceneModel {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 名字
     */
    private String name;

    /**
     *   字段: phrase_id
     *   说明: 触发的标准问ID，对应到 phrase 表的主键ID
     */
    private Long phraseId;

    /**
     *   字段: question_id
     *   说明: 问题ID，对应到 output 表的主键ID
     */
    private Long questionId;

    /**
     *   字段: ctime
     *   说明: 创建时间
     */
    private Date ctime;

    /**
     *   字段: utime
     *   说明: 更新时间
     */
    private Date utime;
}
