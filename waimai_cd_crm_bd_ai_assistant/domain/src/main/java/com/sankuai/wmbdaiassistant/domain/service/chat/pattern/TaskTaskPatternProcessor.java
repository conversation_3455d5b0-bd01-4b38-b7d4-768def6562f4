package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.model.TaskModel;
import com.sankuai.wmbdaiassistant.domain.repository.TaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * 多轮嵌套的多轮
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-26 11:03
 */
@Slf4j
@Component
public class TaskTaskPatternProcessor implements PatternProcessor {

    private static final String PREFIX = "Task:";
    private static final Pattern TASK_PATTERN = Pattern.compile(PREFIX + "\\d+");

    @Resource
    private TaskRepository taskRepository;

    @Override
    public boolean match(String pattern) {
        return TASK_PATTERN.matcher(pattern).matches();
    }

    @Override
    public boolean process(PatternParam param) {

        String pattern = param.getPattern();
        SessionBo sessionBo = param.getSession();
        Long top1QuestionId = sessionBo.fetchTaskTop1QuestionId();
        Double top1QuestionScore = sessionBo.fetchTaskTop1Score();

        String data = pattern.substring(pattern.indexOf(PREFIX) + PREFIX.length()).trim();
        Long taskId = Long.valueOf(data);
        ParamCheckUtil.notNull(taskId, "任务ID为空");

        TaskModel taskModel = taskRepository.findById(taskId);
        sessionBo.pushContext(taskModel, param.getMsgId());
        sessionBo.configTop1(top1QuestionId, top1QuestionScore);

        return true;
    }
}
