package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

/**
 * @description:
 * @author: maningning03
 * @create: 2025/05/13
 **/
public enum PoiRejectTaskAnswerTypeEnum {

    BACKUP(1, "兜底回复"),
    STANDARD_ANSWER(2, "标准问答案"),
    AI_AUDIT_SUGGESTION(3, "ai审核建议"),
    ;
    private int code;
    private String desc;

    PoiRejectTaskAnswerTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PoiRejectTaskAnswerTypeEnum findByCode(Integer code) {
        for (PoiRejectTaskAnswerTypeEnum status : PoiRejectTaskAnswerTypeEnum.values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return null;
    }

    public static boolean isBackUp(Integer code) {
        return Objects.equals(BACKUP.getCode(), code);
    }

}
