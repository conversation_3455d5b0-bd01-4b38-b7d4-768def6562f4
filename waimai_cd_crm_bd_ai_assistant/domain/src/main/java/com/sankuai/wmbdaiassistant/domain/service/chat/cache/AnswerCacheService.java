package com.sankuai.wmbdaiassistant.domain.service.chat.cache;

import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;

/**
 * 回复缓存服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-18 11:23
 */
public interface AnswerCacheService {

    /**
     * 设置问题回复的状态
     *
     * @param questionId     问题ID，对应到 chat_msg 表的 id
     * @param status         回复状态
     */
    void cacheStatus(Long questionId, ChatAnswerStatusEnum status);

    /**
     * 获取问题的回复状态
     *
     * @param questionId 问题ID
     * @return 回复状态
     */
    ChatAnswerStatusEnum fetchStatus(Long questionId);

    /**
     * 向队列中增加回复
     */
    void pushToQueue(Long questionId, GeneralAnswerBo answer);

    /**
     * 从回复队列中拉出一条消息
     *
     * @param questionId 问题ID
     * @return 消息
     */
    GeneralAnswerBo popFromQueue(Long questionId);

    /**
     * 从回复队列中拉出一条消息
     *
     * @param questionId 问题ID
     * @param millMs 等待时间（毫秒）
     * @return
     */
    GeneralAnswerBo popFromQueue(Long questionId, Long millMs);

    /**
     * 缓存前置内容
     *
     * @param questionId       问题ID，对应到 chat_msg 表的 id
     * @param previousContent  前一个回复内容
     */
    void cachePreviousContent(Long questionId, String previousContent);

    /**
     * 获取上一次返回的消息
     *
     * @param questionId 问题ID
     * @return 上一次的消息
     */
    String fetchPreviousContent(Long questionId);
}
