package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.sankuai.wmbdaiassistant.common.*;
import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.enums.*;
import com.sankuai.wmbdaiassistant.domain.model.*;
import com.sankuai.wmbdaiassistant.domain.repository.ChatMsgRepository;
import com.sankuai.wmbdaiassistant.domain.repository.PhraseRepository;
import com.sankuai.wmbdaiassistant.domain.repository.TaskRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.llm.LLMService;
import com.sankuai.wmbdaiassistant.domain.service.chat.pattern.PatternFactory;
import com.sankuai.wmbdaiassistant.domain.service.chat.pattern.PatternParam;
import com.sankuai.wmbdaiassistant.domain.service.chat.pattern.TaskBreakPatternProcessor;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.Workflow;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 任务流默认实现
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-01 17:31
 */
@Slf4j
@Component("defaultWorkflow")
public class DefaultWorkflowImpl implements Workflow {

    @Resource
    private LLMService llmService;

    @Resource
    protected AiChatConfig aiChatConfig;

    @Resource
    private TaskRepository taskRepository;

    @Resource
    private PatternFactory patternFactory;

    @Resource
    private TraceLogService traceLogService;

    @Resource
    private PhraseRepository phraseRepository;

    @Resource
    private ChatRecordService chatRecordService;

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @Resource
    protected ChatContentConverter chatContentConverter;

    @Resource
    private TaskBreakPatternProcessor taskBreakPatternProcessor;

    @Override
    public boolean trigger(SessionBo sessionBo, long id, Long bizId, String input, String entryPoint, GeneralCallback callback, String version) {
        log.info("DefaultWorkflowImpl trigger，id = {}, input = {}, session = {}", id, input, JsonUtil.toJson(sessionBo));

        LLMTypeModel LLMTypeModel = aiChatConfig.LLMTypeMap.get(sessionBo.fetchTaskModel());

        // 设置多轮信息
        boolean isQuestionFromScene = sessionBo.fromScene();
        if (isQuestionFromScene) {
            sessionBo.configId(id);
        }

        // 更新 chatMsg 中的多轮会话ID
        updateInputMsgContext(id, sessionBo);

        // 获取大模型的返回
        List<ChatRecordBo> history = sessionBo.currentContext().isUseHistory() ? fetchSessionHistory(sessionBo)
                : Collections.emptyList();

        String pattern = fetchTaskLlmAnswer(sessionBo, LLMTypeModel, sessionBo.fetchTaskPrompt()
                , history, chatContentConverter.toMarkdownTextFromJson(input));

        // 输出范围校验
        if (!checkOutputScope(sessionBo.fetchTaskId(), pattern)) {
            pattern = "-1";
        }

        // 记录大模型的返回
        insertAiMsg(pattern, LLMTypeModel, sessionBo);

        // 模式识别
        GeneralAnswerBo answer = new GeneralAnswerBo();
        PatternParam patternParam = PatternParam.builder().msgId(id).pattern(pattern).answer(answer)
                .session(sessionBo).callback(callback).input(input).bizId(bizId).version(version).entryPoint(entryPoint).build();
        boolean needBackToNormalMatch = patternFactory.process(patternParam);
        if (needBackToNormalMatch) {
            return true;
        }

        // 增加多轮的曝光日志
        ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel.buildTaskDisplayTraceLog(sessionBo
                , chatContentConverter.extractComponentFromText(answer.getAnswer()))));

        // 返回
        answer.setMsgId(id);
        answer.setTop1FQScore(sessionBo.fetchTaskTop1Score());
        answer.setTop1QuestionId(sessionBo.fetchTaskTop1QuestionId());
        answer.setTopK(Collections.singletonList(VectorBo.builder().id(sessionBo.fetchTaskTop1QuestionId()).score(sessionBo.fetchTaskTop1Score()).build()));
        callback.answerCallback(answer);
        return false;
    }

    private void updateInputMsgContext(Long msgId, SessionBo sessionBo) {
        ChatMsgModel chatMsg = new ChatMsgModel();
        chatMsg.setId(msgId);
        chatMsg.setTaskSessionId(sessionBo.fetchTaskSessionId());
        chatMsg.setContext(JsonUtil.toJson(sessionBo.currentContext()));
        chatMsgRepository.update(chatMsg);
    }

    private List<ChatRecordBo> fetchSessionHistory(SessionBo sessionBo) {
        List<ChatRecordBo> history = chatRecordService.fetchTaskFlowHistory(sessionBo.getSessionId()
                , sessionBo.fetchTaskSessionId());

        if (CollectionUtils.size(history) >= 1) {
            // 第一个问题调整
            if (aiChatConfig.modifyHistory && sessionBo.inTask() && CollectionUtils.size(history) > 1) {
                PhraseModel phraseModel = phraseRepository.findById(sessionBo.fetchTaskTop1QuestionId());
                boolean needModify = phraseModel != null && !phraseModel.isStandardPhrase();
                if (needModify) {
                    history.get(0).setContent(phraseModel.getStandardizedPhrase());
                }
            }

            // truncate 最后一条消息
            history = history.subList(0, history.size() - 1);
        }
        return history;
    }

    private String fetchTaskLlmAnswer(SessionBo sessionBo, LLMTypeModel LLMTypeModel, String prompt, List<ChatRecordBo> history, String input) {
        if (aiChatConfig.modifyLabour) {
            PhraseModel phraseModel = phraseRepository.findById(sessionBo.fetchTaskTop1QuestionId());
            if (phraseModel != null && StringUtils.isNotBlank(phraseModel.getStandardizedPhrase())
                    && StringUtils.equals(phraseModel.getStandardizedPhrase(), aiChatConfig.labourTaskName)) {
                return CollectionUtils.isEmpty(history) ? aiChatConfig.labourApi : "-1";
            }
        }

        if (sessionBo.fetchTaskId() != null && sessionBo.fetchTaskId().equals(aiChatConfig.qualificationRejectedDomainTaskId)) {
            AtomicInteger index = new AtomicInteger(1);
            history = DefaultUtil.defaultList(history).stream().map(record -> {
                ChatRecordBo bo = new ChatRecordBo();
                bo.setType(record.getType());
                if (record.getType() == ChatContentTypeEnum.QUESTION) {
                    bo.setContent(String.format("第%d个问题：%s", index.getAndIncrement(), record.getContent()));
                } else {
                    bo.setContent(record.getContent());
                }
                return bo;
            }).collect(Collectors.toList());
            if (DefaultUtil.defaultBoolean(aiChatConfig.qualificationRejectedDomainOnlyOneInput)) {
                history = new ArrayList<>();
            }
            input = String.format("第%d个问题：%s", index.getAndIncrement(), input);
        }
        if (DefaultUtil.defaultBoolean(aiChatConfig.checkRepeatBreak) && CollectionUtils.isNotEmpty(history)) {
            int counter = 0;
            for (int i = history.size() - 1; i >= 0; i--) {
                ParamCheckUtil.isTrue(counter < 5, "出现连续break");
                ChatRecordBo chatRecordBo = history.get(i);
                if (chatRecordBo != null && chatRecordBo.getType() == ChatContentTypeEnum.ANSWER
                        && taskBreakPatternProcessor.match(chatRecordBo.getContent())) {
                    counter++;
                } else {
                    break;
                }
            }
        }

        String pattern = StringUtil.trim(llmService.chat(LLMTypeModel, prompt, history, input));
        boolean anyMatch = patternFactory.canProcess(pattern);
        if (!anyMatch) {
            log.error("ChatServiceImpl fetchTaskLlmAnswer not match, pattern = {}", pattern);
            return llmService.chat(aiChatConfig.LLMTypeMap.get(aiChatConfig.defaultModel), prompt, history, input);
        }
        return pattern;
    }

    private boolean checkOutputScope(Long taskId, String pattern) {
        TaskModel taskModel = taskRepository.findById(taskId);
        if (taskModel == null || StringUtils.isBlank(taskModel.getResources())
                || !JsonUtil.isJsonText(taskModel.getResources())) {
            log.error("TaskModel resources 字段配置错误,taskModel:{}", taskModel);
            return false;
        }

        List<String> patterns = new ArrayList<>();

        patterns.add("-1");

        TaskResourceModel taskResourceModel = JsonUtil.fromJson(taskModel.getResources(), TaskResourceModel.class);
        ParamCheckUtil.notNull(taskResourceModel, "多轮的资源为空");

        if(CollectionUtils.isNotEmpty(taskResourceModel.getOutputIds())){
            taskResourceModel.getOutputIds().forEach(outputId -> patterns.add("Output:" + outputId));
        }

        if(CollectionUtils.isNotEmpty(taskResourceModel.getApiIds())){
            taskResourceModel.getApiIds().forEach(apiId -> patterns.add("Api:" + apiId));
        }

        if(patterns.stream().anyMatch(p -> p.equalsIgnoreCase(pattern))){
            return true;
        }
        log.error("多轮输出范围不匹配，pattern={},范围={}",pattern,patterns);
        return false;
    }

    private void insertAiMsg(String pattern, LLMTypeModel LLMTypeModel, SessionBo sessionBo) {
        ChatMsgModel aiMsg = new ChatMsgModel();
        aiMsg.setSessionId(sessionBo.getSessionId());
        aiMsg.setUid(sessionBo.getUid());
        aiMsg.setMis(sessionBo.getMis());
        aiMsg.setAbilityType(AbilityTypeEnum.GENERAL);
        aiMsg.setContentType(ChatContentTypeEnum.ANSWER);
        aiMsg.setAnswerType(AnswerTypeEnum.getTaskAnswerTypeByLlmType(LLMTypeModel));
        aiMsg.setContent(pattern);
        aiMsg.setSensitiveStatus(SensitiveStatusEnum.NORMAL);
        aiMsg.setTaskSessionId(sessionBo.fetchTaskSessionId());
        aiMsg.setCreateTime(new Date());
        aiMsg.setContext(JsonUtil.toJson(sessionBo.currentContext()));

        chatMsgRepository.insert(aiMsg);
    }

}
