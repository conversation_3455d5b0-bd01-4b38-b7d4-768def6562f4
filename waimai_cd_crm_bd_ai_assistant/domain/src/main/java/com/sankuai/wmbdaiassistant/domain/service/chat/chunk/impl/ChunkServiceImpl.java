package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.impl;

import java.util.function.Consumer;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.chunk.ChunkPatternReplacer;
import com.sankuai.wmbdaiassistant.domain.service.chat.chunk.ChunkService;

@Service
public class ChunkServiceImpl implements ChunkService {

    @Resource
    private ChunkPatternReplacer chunkPatternReplacer;

    @MdpConfig("max_chunk_length:20")
    private Long MAX_CHUNK_LENGTH;

    @Override
    public void handler(StringBuffer prefixBuffer, String chunk, boolean lastOne, String source,
            Consumer<String> consumer) {
        String content = prefixBuffer.toString() + chunk;
        if (!shouldReturn(content, lastOne)) {
            prefixBuffer.append(chunk);
            return;
        }
        content = chunkPatternReplacer.replace(content, source);
        consumer.accept(content);
        prefixBuffer.delete(0, prefixBuffer.length());
    }

    private boolean shouldReturn(String chunkContent, boolean lastone) {
        // 规则1：如果是最后一片或内容为空，直接返回
        if (lastone || chunkContent == null || chunkContent.isEmpty()) {
            return true;
        }
        // 规则2：超过最大chunk长度，直接返回
        if (chunkContent.length() < MAX_CHUNK_LENGTH) {
            return false;
        }
        // 规则3：检查模板变量{{}}是否配对完整
        if(chunkPatternReplacer.isTemplateVariableNotComplete(chunkContent)){
            return false;
        }
        return true;
    }

}
