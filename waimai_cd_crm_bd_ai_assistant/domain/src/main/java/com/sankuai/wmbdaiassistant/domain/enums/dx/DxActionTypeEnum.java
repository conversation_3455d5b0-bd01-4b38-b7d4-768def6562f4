package com.sankuai.wmbdaiassistant.domain.enums.dx;

import java.util.Arrays;
import lombok.Getter;
import lombok.ToString;

/**
 * @Desc 大象消息业务类型枚举
 * <AUTHOR>
 * @Date 2025/2/28
 **/
@Getter
@ToString
public enum DxActionTypeEnum {
    SUBMIT_QUERY("submitQuery", "提问"),
    LIKE("like", "点赞"),
    DISLIKE("dislike", "点踩");

    private String code;

    private String desc;

    DxActionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DxActionTypeEnum findByCode(String code) {
        return Arrays.stream(DxActionTypeEnum.values()).filter(e -> e.getCode().equals(code)).
                findFirst().orElse(null);
    }
}
