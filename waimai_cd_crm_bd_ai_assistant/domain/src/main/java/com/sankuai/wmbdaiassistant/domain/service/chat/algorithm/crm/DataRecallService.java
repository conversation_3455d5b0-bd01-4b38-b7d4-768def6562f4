package com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm;

import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;

import java.util.List;

/**
 * 数据召回服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-28 10:20
 */
public interface DataRecallService {

    /**
     * 分片召回
     *
     * @param question 问题
     * @param datasetIds 知识库ID列表
     * @param size 召回数量
     * @return
     */
    List<FragmentModel> recall(String question, List<Long> datasetIds, int size);

    /**
     * 分片召回
     *
     * @param question 问题
     * @param datasetIds 知识库ID列表
     * @param history 历史对话
     * @param size 召回数量
     * @return
     */
    List<FragmentModel> recall(String question, List<Long> datasetIds, List<ChatRecordBo> history, int size);
}
