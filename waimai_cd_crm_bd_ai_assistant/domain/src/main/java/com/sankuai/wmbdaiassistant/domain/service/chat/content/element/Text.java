package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 文本
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Text implements ContentElement {
    private final String type = ContentElementTypeEnum.TEXT.getCode();

    public static final String TEXT_FORMAT = "{\"type\":\"text\",\"insert\":\"%s\"}";

    @JsonProperty("insert")
    private String content;

    public static Text build(String content) {
        return Text.builder().content(content).build();
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void merge(ContentElement other) {
        ParamCheckUtil.isTrue((other instanceof Text), "Text merge error");
        setContent(getContent() + ((Text) other).getContent());
    }

    @Override
    public String toMarkdownText() {
        return content;
    }

    @Override
    public Map<String, String> toTtTransferContent() {
        Map<String, String> map = new HashMap<>();
        map.put("type", "text");
        map.put("text", content);
        return map;
    }

    public static boolean judgeIfTextType(String input) {
        if (StringUtils.isBlank(input)) {
            return false;
        }
        return input.contains("\"type\":\"text\"");
    }

}
