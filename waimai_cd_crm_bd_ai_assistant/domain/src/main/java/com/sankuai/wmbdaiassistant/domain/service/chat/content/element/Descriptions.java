package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 描述信息
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR>
 * @date 2025-05-07 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Descriptions implements ContentElement {

    private final String type = ContentElementTypeEnum.DESCRIPTIONS.getCode();

    @JsonProperty("insert")
    private DescriptionsObject insert;

    @Override
    public String toMarkdownText() {
        if (insert == null || insert.getDescriptions() == null || CollectionUtils.isEmpty(insert.getDescriptions().getList())) {
            return null;
        }
        List<LabelValueObject> labelValueObjectList = insert.getDescriptions().getList();
        StringBuilder stringBuilder = new StringBuilder();
        for (LabelValueObject object : labelValueObjectList) {
            stringBuilder.append(object.getLabel());
            stringBuilder.append(":");
            stringBuilder.append(object.getValue());
            stringBuilder.append("\n");
        }
        return stringBuilder.toString();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DescriptionsObject {
        private DescriptionsListObject descriptions;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DescriptionsListObject  {
        private List<LabelValueObject> list;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LabelValueObject {
        private String label;
        private String value;
    }


}