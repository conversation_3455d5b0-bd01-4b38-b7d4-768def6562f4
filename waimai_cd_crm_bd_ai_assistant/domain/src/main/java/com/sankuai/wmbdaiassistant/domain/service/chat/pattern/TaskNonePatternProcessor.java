package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 多轮none模式处理器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-22 19:52
 */
@Slf4j
@Component
public class TaskNonePatternProcessor implements PatternProcessor {

    private static final String PATTERN = "none";

    @Override
    public boolean match(String pattern) {
        return PATTERN.equals(pattern);
    }

    @Override
    public boolean process(PatternParam param) {
        return false;
    }
}
