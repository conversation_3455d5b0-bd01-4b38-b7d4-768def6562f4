package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.SceneModel;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024/4/18 15:02
 */
public interface SceneRepository {

    /**
     * 根据主键查询场景
     *
     * @param sceneId ID
     * @return 场景
     */
    SceneModel findById(Long sceneId);

    /**
     * 通过phraseId批量查找SceneModel
     * 
     * @param phraseIdList phraseId列表
     * @return SceneModel List
     */
    List<SceneModel> findByPhraseIds(List<Long> phraseIdList);

    /**
     * 批量更新
     * @param sceneModels 场景模型列表
     * @return 更新成功的场景模型数量
     */
    int batchUpdateByIdSelective(List<SceneModel> sceneModels);
}
