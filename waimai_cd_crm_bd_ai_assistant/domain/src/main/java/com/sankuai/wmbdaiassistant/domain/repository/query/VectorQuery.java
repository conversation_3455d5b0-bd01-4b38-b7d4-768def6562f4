package com.sankuai.wmbdaiassistant.domain.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 向量搜索条件
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-15 13:29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorQuery {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 向量
     */
    private List<Double> vector;

    /**
     * 域的路径ID列表。例如：A-B，则在数组中应该依次包含域A的ID和域B的ID
     */
    private List<Long> domainPathIdList;

    /**
     * 是否过滤域
     */
    private Boolean filterByDomain;

    /**
     * 返回多少条数据
     */
    private Integer topN;
}
