package com.sankuai.wmbdaiassistant.domain.service.chat.llm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-08 17:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FunctionCallParam {

    /**
     * 名称
     */
    private String name;

    /***
     * 描述
     */
    private String description;

    /**
     * 所有变量
     */
    private List<Variable> variableList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Variable {
        /**
         * 名字
         */
        private String name;

        /**
         * 数据类型。number: 数字；string：字符串。
         */
        private String type;

        /**
         * 描述
         */
        private String desc;

        /**
         * 枚举值（可选）
         */
        private Set<String> enumValues;
    }
}
