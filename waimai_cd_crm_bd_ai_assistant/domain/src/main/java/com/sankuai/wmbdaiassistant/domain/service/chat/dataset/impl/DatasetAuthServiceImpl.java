package com.sankuai.wmbdaiassistant.domain.service.chat.dataset.impl;

import com.sankuai.wmbdaiassistant.domain.model.DatasetModel;
import com.sankuai.wmbdaiassistant.domain.repository.DatasetRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.dataset.DatasetAuthService;
import com.sankuai.wmbdaiassistant.domain.service.chat.org.EmpInfoService;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库权限服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/6 下午2:31
 */
@Slf4j
@Service
public class DatasetAuthServiceImpl implements DatasetAuthService {

    @Resource
    private DatasetRepository datasetRepository;

    @Resource
    private EmpInfoService empInfoService;


    @Override
    public List<Long> queryUserVisibleDataset(Long uid) {
        if(Objects.isNull(uid)){
            return new ArrayList<>();
        }
        List <Long> visibleDatasetIdList = new ArrayList<>();

        try {
            // 获取用户所在的组织链id
            String userOrgPath = empInfoService.queryUserOrgPathByEmpId(uid);
            log.info("DatasetAuthService.queryUserVisibleDataset,uid={},userOrgPath:{}", uid, userOrgPath);
            List<Long> userOrgIds = new ArrayList<>();
            if (StringUtils.isNotBlank(userOrgPath)) {
                userOrgIds = Arrays.stream(userOrgPath.split("-")).map(Long::valueOf)
                        .collect(Collectors.toList());
            }
            List<DatasetModel> datasetModels = datasetRepository.findList();
            for (DatasetModel model : datasetModels) {
                boolean orgAuthFlag = !CollectionUtils.isEmpty(userOrgIds) && userOrgIds.stream().anyMatch(model.getAuthOrgs()::contains);
                boolean uidAuthFlag = model.getAuthUids().contains(uid);
                if (orgAuthFlag || uidAuthFlag) {
                    visibleDatasetIdList.add(model.getId());
                }
            }
        } catch (Exception e) {
            log.error("DatasetAuthService queryUserVisibleDataset error", e);
        }

        log.info("DatasetAuthService.queryUserVisibleDataset,uid={},visibleDatasetIdList:{}", uid, visibleDatasetIdList);
        return visibleDatasetIdList;
    }

    @Override
    public List<Long> queryUserVisibleDataset(Integer uid) {
        if(Objects.isNull(uid)){
            return new ArrayList<>();
        }
        return queryUserVisibleDataset(uid.longValue());
    }

    @Override
    public List<Long> queryUserVisibleDataset(String mis) {
        Long uid = empInfoService.queryUserEmpIdByMis(mis);
        return queryUserVisibleDataset(uid);
    }
}
