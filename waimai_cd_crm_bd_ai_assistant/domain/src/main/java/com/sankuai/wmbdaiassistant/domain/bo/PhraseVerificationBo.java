package com.sankuai.wmbdaiassistant.domain.bo;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import lombok.Data;

@Data
public class PhraseVerificationBo {

    /**
     * 被校验模型
     */
    private PhraseModel phraseModel;

    /**
     * 向量检索结果
     */
    private VectorBo vectorBo;

    /**
     * 校验结果
     */
    private ValidEnum validEnum;

    /**
     * 错误信息
     */
    private String invalidMsg;

    public boolean isInValid() {
        return ValidEnum.INVALID.equals(validEnum);
    }
}
