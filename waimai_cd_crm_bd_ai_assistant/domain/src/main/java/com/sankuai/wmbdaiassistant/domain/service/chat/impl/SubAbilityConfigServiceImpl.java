package com.sankuai.wmbdaiassistant.domain.service.chat.impl;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;
import org.springframework.stereotype.Service;

import com.sankuai.wmbdaiassistant.domain.repository.Page;
import com.sankuai.wmbdaiassistant.domain.repository.SubAbilityConfigRepository;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.service.chat.SubAbilityConfigService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023-12-14
 */
@Service
@Slf4j
public class SubAbilityConfigServiceImpl implements SubAbilityConfigService {

    @Resource
    private SubAbilityConfigRepository subAbilityConfigRepository;

    public List<SubAbilityConfigModel> selectConfig(AbilityTypeEnum abilityType, Page page) {
        List<SubAbilityConfigModel> configs = subAbilityConfigRepository.findConfigByPage(abilityType.getCode(), page);
        return configs;
    }

    @Override
    public Long countPageConfig(AbilityTypeEnum abilityType) {
        return subAbilityConfigRepository.countConfigByPage(abilityType.getCode());
    }

}
