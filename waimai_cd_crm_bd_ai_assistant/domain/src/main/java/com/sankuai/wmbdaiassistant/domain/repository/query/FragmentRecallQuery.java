package com.sankuai.wmbdaiassistant.domain.repository.query;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分片查询参数
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 18:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FragmentRecallQuery {

    /**
     * 问题
     */
    private String query;

    /**
     * 知识库ID
     */
    private List<Long> datasetIdList;

    /**
     * 分页页码
     */
    private Integer pageNum;

    /**
     * 批次分页大小
     */
    private Integer pageSize;
}
