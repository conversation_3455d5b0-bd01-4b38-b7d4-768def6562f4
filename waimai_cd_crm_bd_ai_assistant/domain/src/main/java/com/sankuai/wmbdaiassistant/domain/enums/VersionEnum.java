package com.sankuai.wmbdaiassistant.domain.enums;

/**
 * 版本枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-01-10 14:32
 */
public enum VersionEnum {

    V1,
    V2,
    V3,
    ;

    public static boolean isLatestVersion(String version) {
        return getLatestVersion().equals(version);
    }

    public static String getLatestVersion() {
        return VersionEnum.values()[VersionEnum.values().length - 1].name();
    }
}
