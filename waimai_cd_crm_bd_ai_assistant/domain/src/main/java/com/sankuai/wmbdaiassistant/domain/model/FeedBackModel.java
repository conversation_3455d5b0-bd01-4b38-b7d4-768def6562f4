package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.FeedBackTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 反馈
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-05 15:36
 */
@Data
public class FeedBackModel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 针对哪条消息的反馈(chat_msg)
     */
    private Long chatRecordId;

    /**
     *  用户id
     */
    private Integer uid;

    /**
     * 反馈类型
     */
    private FeedBackTypeEnum type;

    /**
     * 建议内容
     */
    private String suggestionContent;

    /**
     * 反馈时间
     */
    private Date feedbackTime;

    /**
     * 用户misId
     */
    private String mis;

    /**
     * 用户提交的tt id
     */
    private Long ttId;

    /**
     * tt创建时间
     */
    private Date ttCreateTime;

    /**
     * tt解决时间
     */
    private Date ttSolvedTime;

    public boolean isDisplayFeedback() {
        return type != null && (type == FeedBackTypeEnum.LIKE || type == FeedBackTypeEnum.DISLIKE);
    }

}
