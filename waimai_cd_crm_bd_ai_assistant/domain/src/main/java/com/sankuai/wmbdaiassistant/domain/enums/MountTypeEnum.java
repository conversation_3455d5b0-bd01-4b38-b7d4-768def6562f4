package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * 挂载类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/25 13:51
 */
@Getter
public enum MountTypeEnum {
    PHRASE(1, "phrase知识"),
    CATEGORY(2, "目录");

    private int code;
    private String desc;

    MountTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MountTypeEnum findByCode(int code) {
        for (MountTypeEnum type : MountTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
