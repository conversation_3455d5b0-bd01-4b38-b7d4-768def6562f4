package com.sankuai.wmbdaiassistant.domain.service.chat.rag;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;

/**
 * 任务流触发器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-01 17:30
 */
public interface Workflow {

    /**
     * 触发多轮
     * @param sessionBo
     * @param id
     * @param input
     * @param entryPoint
     * @param callback
     * @return 触发是否成功
     */
    boolean trigger(SessionBo sessionBo, long id, Long bizId, String input, String entryPoint, GeneralCallback callback, String version);
}
