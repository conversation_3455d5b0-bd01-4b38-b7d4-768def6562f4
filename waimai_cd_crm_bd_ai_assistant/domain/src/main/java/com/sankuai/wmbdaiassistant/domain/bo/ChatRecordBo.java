package com.sankuai.wmbdaiassistant.domain.bo;

import com.sankuai.wmbdaiassistant.domain.enums.ChatContentTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 聊天记录行
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-15 16:09
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChatRecordBo {

    /**
     * 内容
     */
    private String content;

    /**
     * 类型
     */
    private ChatContentTypeEnum type;

}
