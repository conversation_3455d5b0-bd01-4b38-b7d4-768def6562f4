package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 附件
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR>
 * @date 2025-04-22 16:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Addition implements ContentElement {

    private final String type = ContentElementTypeEnum.ADDITION.getCode();

    private static final String IMAGE_TYPE = "image";
    private static final String FILE_TYPE = "file";


    @JsonProperty("insert")
    private AdditionInfo info;

    @Override
    public String toMarkdownText() {
        if (info == null || info.getAddition() == null || CollectionUtils.isEmpty(info.getAddition().getAdditionList())) {
            return null;
        }
        List<AdditionItem> additionList = info.getAddition().getAdditionList();
        int index = 1;
        StringBuilder stringBuilder = new StringBuilder();
        for (AdditionItem additionItem : additionList) {
            if (IMAGE_TYPE.equals(additionItem.getType())) {
                stringBuilder.append(getImageItemMarkDownText(additionItem, index++));
            } else if (FILE_TYPE.equals(additionItem.getType())) {
                stringBuilder.append(String.format("文件%s链接：%s\n", index++, additionItem.getSrc()));
            }
        }
        return stringBuilder.toString();
    }

    private String getImageItemMarkDownText(AdditionItem additionImageItem, Integer index) {
        if (additionImageItem == null) {
            return "";
        }
        if (additionImageItem.getMetaData() == null) {
            return String.format("图片%s链接：%s \n", index, additionImageItem.getSrc());
        }
        return String.format("图片%s链接：%s，经度：%s，纬度：%s \n", index, additionImageItem.getSrc(), additionImageItem.getMetaData().getLongitude(), additionImageItem.getMetaData().getLatitude());

    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionInfo  {
        private Additions addition;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Additions {
        private List<AdditionItem> additionList;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionItem {
        private String type;
        private String src;
        private AdditionImageMetaData metaData;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionImageMetaData {
        private Double latitude; //纬度
        private Double longitude; //经度
    }


}
