package com.sankuai.wmbdaiassistant.domain.service.chat;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;

import java.util.List;

/**
 * 聊天服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-14 13:29
 */
public interface ChatService {

    /**
     * 会话
     *
     * @param session    当前的上下文信息
     * @param uid        用户ID
     * @param bizId      业务ID
     * @param id         此会话返回的消息的唯一标识
     * @param input      用户的输入
     * @param entryPoint 输入点
     * @param callback   收到消息后，统一的回调
     * @param version    客户端的版本信息
     */
    void chat(SessionBo session, Integer uid, long bizId, long id, String input, String entryPoint
            , GeneralCallback callback, String version);

    /**
     * 关联问
     *
     * @param bizId 业务ID
     * @param input 输入的问题
     * @return 关联的问题
     */
    List<String> relativeQuestion(long bizId, String input);

    /**
     * 获取推荐问题
     * @param input 问题
     * @param uid   用户ID
     * @return 推荐的问题列表
     */
    List<String> fetchRecommendedQuestion(String input, Integer uid);
}
