package com.sankuai.wmbdaiassistant.domain.service.chat.chunk;

import java.util.function.Consumer;

import org.springframework.stereotype.Service;

@Service
public interface ChunkService {

    /**
     * 处理流式chunk
     * @param prefixBuffer
     * @param chunk
     * @param lastOne
     * @param source
     * @param consumer
     */
    void handler(StringBuffer prefixBuffer, String chunk, boolean lastOne, String source,
            Consumer<String> consumer);
}
