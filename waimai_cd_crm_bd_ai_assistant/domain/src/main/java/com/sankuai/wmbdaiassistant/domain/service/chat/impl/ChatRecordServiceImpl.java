package com.sankuai.wmbdaiassistant.domain.service.chat.impl;

import com.google.common.collect.Sets;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.enums.*;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.model.SessionModel;
import com.sankuai.wmbdaiassistant.domain.repository.ChatMsgRepository;
import com.sankuai.wmbdaiassistant.domain.repository.SessionRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import com.sankuai.wmbdaiassistant.domain.transaction.TransactionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/5
 **/
@Service
@Slf4j
public class ChatRecordServiceImpl implements ChatRecordService {

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @Resource
    private SessionRepository sessionRepository;

    @Resource
    private TransactionHelper transactionHelper;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private SessionSourceConfigManager sessionSourceConfigManager;

    @Override
    public ChatMsgModel findById(Long id) {
        return chatMsgRepository.findById(id);
    }

    /**
     * 确认用户是否有聊天记录
     */
    @Override
    public boolean ensureChatRecordExist(int uid) {
        return chatMsgRepository.countByUid(uid) > 0L;
    }

    /**
     * 查询会话的聊天记录
     */
    @Override
    public List<ChatMsgModel> queryChatRecords(long sessionId) {
        return chatMsgRepository.findBySessionId(sessionId);
    }

    @Override
    public ChatMsgModel createQueryMsg(ChatSubmitQueryBo chatSubmitQueryBo) {
        SessionBo sessionBo = chatSubmitQueryBo.getSessionBo();
        ChatMsgModel queryMsg = new ChatMsgModel();
        queryMsg.setUid(sessionBo.getUid());
        queryMsg.setMis(sessionBo.getMis());
        queryMsg.setContentType(ChatContentTypeEnum.QUESTION);
        queryMsg.setContent(chatSubmitQueryBo.getContent());
        queryMsg.setCreateTime(new Date());
        queryMsg.setSessionId(sessionBo.getSessionId());
        queryMsg.setSensitiveStatus(SensitiveStatusEnum.NORMAL);
        queryMsg.setAbilityType(AbilityTypeEnum.findByCode(chatSubmitQueryBo.getAbilityType()));
        queryMsg.setEntryPointType(chatSubmitQueryBo.getEntryPointType());
        chatMsgRepository.insert(queryMsg);
        return queryMsg;
    }

    @Override
    public ChatMsgModel createAnswerMsg(ChatFetchAnswerBo chatFetchAnswerBo) {

        ChatMsgModel questionChatMsg = transactionHelper.doInTransaction(() ->
                chatMsgRepository.findById(chatFetchAnswerBo.getQuestionMsgId()));
        ParamCheckUtil.notNull(questionChatMsg, String.format("QuestionMsg 不存在, id = %d", chatFetchAnswerBo.getQuestionMsgId()));

        ChatMsgModel answerMsg = new ChatMsgModel();
        answerMsg.setUid(questionChatMsg.getUid());
        answerMsg.setMis(questionChatMsg.getMis());
        answerMsg.setContentType(ChatContentTypeEnum.ANSWER);

        answerMsg.setContent(JsonUtil.toJson(chatFetchAnswerBo));
        answerMsg.setSensitiveStatus(SensitiveStatusEnum.NORMAL);
        answerMsg.setViewContent(chatFetchAnswerBo.getViewContent());
        answerMsg.setAnswerType(AnswerTypeEnum.of(chatFetchAnswerBo.getAnswerType()));
        if (chatFetchAnswerBo.isSensitive()) {
            answerMsg.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE);
        }
        answerMsg.setCreateTime(new Date());
        answerMsg.setSessionId(questionChatMsg.getSessionId());
        answerMsg.setAbilityType(AbilityTypeEnum.findByCode(chatFetchAnswerBo.getAbilityType()));
        answerMsg.setTaskSessionId(questionChatMsg.getTaskSessionId());
        answerMsg.setContext(questionChatMsg.getContext());
        chatMsgRepository.insert(answerMsg);
        return answerMsg;
    }

    @Override
    public void updateAnswerMsg(Long msgId, ChatFetchAnswerBo chatFetchAnswerBo) {

        ChatMsgModel questionChatMsg = transactionHelper.doInTransaction(() ->
                chatMsgRepository.findById(chatFetchAnswerBo.getQuestionMsgId()));
        ParamCheckUtil.notNull(questionChatMsg, String.format("QuestionMsg 不存在, id = %d", chatFetchAnswerBo.getQuestionMsgId()));

        ChatMsgModel answerMsg = new ChatMsgModel();
        answerMsg.setId(msgId);
        answerMsg.setContent(JsonUtil.toJson(chatFetchAnswerBo));
        answerMsg.setSensitiveStatus(SensitiveStatusEnum.NORMAL);
        answerMsg.setViewContent(chatFetchAnswerBo.getViewContent());
        answerMsg.setAnswerType(AnswerTypeEnum.of(chatFetchAnswerBo.getAnswerType()));
        if (chatFetchAnswerBo.isSensitive()) {
            answerMsg.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE);
        }
        answerMsg.setTaskSessionId(questionChatMsg.getTaskSessionId());
        answerMsg.setContext(questionChatMsg.getContext());
        chatMsgRepository.update(answerMsg);
    }

    @Override
    public List<ChatMsgModel> queryList(Long sessionId, Integer uid, Long msgId, int limit) {
        return chatMsgRepository.findUserVisibleMsgBySessionId(sessionId, uid, msgId, limit);
    }

    @Override
    public List<ChatMsgModel> fetchSessionHistoryBySinceMsgId(Integer uid, List<Long> sessionIds, Long sinceMsgId,
            Integer size) {
        return chatMsgRepository.findUserVisibleMsg(uid, sessionIds, sinceMsgId, size, true);
    }

    @Override
    public void updateBlockAnswerMsg(Long msgId, String displayContent) {
        ChatMsgModel chatMsg = chatMsgRepository.findById(msgId);
        if (Objects.isNull(chatMsg)) {
            return;
        }
        if (chatMsg.getContentType() == ChatContentTypeEnum.QUESTION) {
            return;
        }
        if (StringUtils.isEmpty(chatMsg.getContent())) {
            return;
        }
        ChatFetchAnswerBo chatFetchAnswerBo = JsonUtil.fromJson(chatMsg.getContent(), ChatFetchAnswerBo.class);
        if (chatFetchAnswerBo == null) {
            return;
        }

        ChatMsgModel questionMsg = transactionHelper.doInTransaction(() -> chatMsgRepository.findById(chatFetchAnswerBo.getQuestionMsgId()));
        ParamCheckUtil.notNull(questionMsg, "消息阻断时，问题不存在");

        chatFetchAnswerBo.setCurrentContent(displayContent);
        chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        chatMsg.setContent(JsonUtil.toJson(chatFetchAnswerBo));
        chatMsg.setTaskSessionId(questionMsg.getTaskSessionId());
        chatMsg.setContext(questionMsg.getContext());

        chatMsgRepository.update(chatMsg);
        if (StringUtils.isBlank(chatMsg.getTaskSessionId()) && StringUtils.isBlank(chatMsg.getContext())) {
            chatMsgRepository.updateTaskSessionIdAndContextNull(chatMsg.getId());
        }
    }

    @Override
    public List<ChatRecordBo> fetchSessionHistory(Long sessionId) {
        List<ChatMsgModel> chatMsgList = chatMsgRepository.findBySessionId(sessionId);
        return toUserChatHistory(chatMsgList, true);
    }

    @Override
    public List<ChatRecordBo> fetchTaskFlowHistory(Long sessionId, String taskSessionId) {
        List<ChatMsgModel> chatMsgList = chatMsgRepository.findByTaskSessionId(sessionId, taskSessionId);
        return DefaultUtil.defaultList(chatMsgList).stream().map(chatMsg -> {
            if (chatMsg.getContentType() == ChatContentTypeEnum.QUESTION) {
                return new ChatRecordBo(chatContentConverter.toMarkdownTextFromJson(chatMsg.getContent()), ChatContentTypeEnum.QUESTION);
            } else {
                if (chatMsg.getAnswerType() != null && Objects.equals(AnswerTypeEnum.AI_TASK.getWay(), chatMsg.getAnswerType().getWay())) {
                    return new ChatRecordBo(chatMsg.getContent(), ChatContentTypeEnum.ANSWER);
                }
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<ChatRecordBo> fetchTaskUserHistory(Long sessionId, String taskSessionId) {
        List<ChatMsgModel> chatMsgList = chatMsgRepository.findByTaskSessionId(sessionId, taskSessionId);
        return toUserChatHistory(chatMsgList, false);
    }

    private List<ChatRecordBo> toUserChatHistory(List<ChatMsgModel> chatMsgList, boolean needModifyQuestion) {
        return DefaultUtil.defaultList(chatMsgList).stream().map(chatMsg -> {

            if (chatMsg.getAnswerType() != null
                    && Objects.equals(AnswerTypeEnum.AI_TASK.getWay(), chatMsg.getAnswerType().getWay())
                    || AnswerTypeEnum.API_EMBEDDING == chatMsg.getAnswerType()) {
                return null;
            }

            if (chatMsg.getContentType() == ChatContentTypeEnum.QUESTION) {
                return new ChatRecordBo(extractQuestion(chatMsg.getContent(), needModifyQuestion), ChatContentTypeEnum.QUESTION);
            } else {
                ChatFetchAnswerBo chatFetchAnswerBo = JsonUtil.fromJson(chatMsg.getContent(), ChatFetchAnswerBo.class);
                if (Objects.isNull(chatFetchAnswerBo)) {
                    return null;
                }
                String content = chatContentConverter.toMarkdownTextFromJson(chatFetchAnswerBo.getCurrentContent());
                if (StringUtils.isBlank(content)) {
                    return null;
                }
                return new ChatRecordBo(content, ChatContentTypeEnum.ANSWER);
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String extractQuestion(String content, boolean needModifyQuestion) {
        List<ContentElement> elements = chatContentConverter.extractComponentFromText(content);
        String question = chatContentConverter.componentToMarkdownText(elements);
        if (!DefaultUtil.defaultBoolean(aiChatConfig.pictureAdditionDataModifySwitch) || !needModifyQuestion) {
            return question;
        }
        if (DefaultUtil.defaultBoolean(aiChatConfig.pictureDataQuestionEmpty)) {
            if (DefaultUtil.defaultList(elements).stream().
                    anyMatch(element -> ContentElementTypeEnum.ADDITION.getCode().equals(element.getType()))) {
                question = StringUtils.EMPTY;
            }
        } else {
            elements = DefaultUtil.defaultList(elements).stream().
                    filter(element -> !ContentElementTypeEnum.ADDITION.getCode().equals(element.getType()))
                    .collect(Collectors.toList());
            question = chatContentConverter.componentToMarkdownText(elements);
        }
        return question;
    }

    @Override
    public long countTTClick(List<Long> sessionIds) {
        return chatMsgRepository.countTTClick(sessionIds);
    }

    @Override
    public String getSourceCodeByMsgId(long msgId) {
        ChatMsgModel chatMsgModel = chatMsgRepository.findById(msgId);
        if (Objects.isNull(chatMsgModel)) {
            return SessionSourceConfigManager.UNKNOWN_SOURCE.getCode();
        }
        SessionModel sessionModel = sessionRepository.findById(chatMsgModel.getSessionId());
        if (Objects.isNull(sessionModel)) {
            return SessionSourceConfigManager.UNKNOWN_SOURCE.getCode();
        }
        return sessionModel.getSource();
    }

    @Override
    public boolean isDxChatMsg(Long msgId) {
        String sourceCode = getSourceCodeByMsgId(msgId);
        return sessionSourceConfigManager.getAllDxSourceCodeSet().contains(sourceCode);
    }
}
