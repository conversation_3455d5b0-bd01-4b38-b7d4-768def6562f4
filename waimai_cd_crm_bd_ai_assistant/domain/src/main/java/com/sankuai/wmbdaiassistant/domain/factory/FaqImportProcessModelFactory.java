package com.sankuai.wmbdaiassistant.domain.factory;

import com.sankuai.wmbdaiassistant.domain.enums.FaqImportStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.FaqImportProcessModel;
import java.util.Date;

/**
 * input description here
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
public class FaqImportProcessModelFactory {

    public static FaqImportProcessModel create(String mis, String name) {
        return create(null, mis, name, FaqImportStatusEnum.INIT);
    }

    public static FaqImportProcessModel create(Long id, String mis, String name, FaqImportStatusEnum status) {
        return FaqImportProcessModel.builder().id(id).mis(mis).name(name).status(status).createTime(new Date())
                .modifyTime(new Date()).build();
    }
}
