package com.sankuai.wmbdaiassistant.domain.service.chat;

import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface ChatRecordService {

    ChatMsgModel findById(Long id);

    boolean ensureChatRecordExist(int uid);

    List<ChatMsgModel> queryChatRecords(long sessionId);

    ChatMsgModel createQueryMsg(ChatSubmitQueryBo chatSubmitQueryBo);

    ChatMsgModel createAnswerMsg(ChatFetchAnswerBo chatFetchAnswerBo);

    List<ChatMsgModel> queryList(Long sessionId, Integer uid, Long msgId, int limit);

    /**
     * 获取用户消息记录
     *
     * @param uid       用户ID
     * @param sessionIds 会话ID列表
     * @param sinceMsgId 起始消息ID
     * @param size 获取数量
     * @return
     */
    List<ChatMsgModel> fetchSessionHistoryBySinceMsgId(Integer uid,List<Long> sessionIds, Long sinceMsgId, Integer size);

    void updateAnswerMsg(Long msgId, ChatFetchAnswerBo chatFetchAnswerBo);

    void updateBlockAnswerMsg(Long msgId, String displayContent);

    /**
     * 获取会话历史
     *
     * @param sessionId 会话ID
     * @return
     */
    List<ChatRecordBo> fetchSessionHistory(Long sessionId);

    /**
     * 获取多轮中的流程历史记录（包含用户输入 + 大模型返回的模式）
     *
     * @param sessionId     会话ID
     * @param taskSessionId 多轮会话ID
     * @return
     */
    List<ChatRecordBo> fetchTaskFlowHistory(Long sessionId, String taskSessionId);

    /**
     * 获取多轮中用户可见的历史会话（包含用户输入 + 用户可见的输出）
     *
     * @param sessionId     会话ID
     * @param taskSessionId 多轮会话ID
     * @return
     */
    List<ChatRecordBo> fetchTaskUserHistory(Long sessionId, String taskSessionId);

    /**
     * 统计会话内TT点击次数
     */
    long countTTClick(List<Long> sessionIds);


    /**
     * 根据消息ID获取会话来源枚举
     *
     * @param msgId 消息ID
     * @return 会话来源枚举
     */
    String getSourceCodeByMsgId(long msgId);

    /**
     * 判断消息是否为大象robot聊天消息
     * @param msgId
     * @return
     */
    boolean isDxChatMsg(Long msgId);
}
