package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.DatasetModel;
import java.util.List;

/**
 * @Desc 数据集仓储层
 * <AUTHOR>
 * @Date 2025/2/14
 **/
public interface DatasetRepository {

    boolean insert(DatasetModel datasetModel);

    /**
     * 根据ID查询目录
     *
     * @param id
     * @return
     */
    DatasetModel findById(Long id);

    /**
     * 更新目录
     *
     * @param DatasetModel
     * @return
     */
    boolean update(DatasetModel DatasetModel);

    /**
     * 查询列表
     *
     * @param pageNum  页码，默认1
     * @param pageSize 每页数量，默认20
     * @return
     */
    List<DatasetModel> findListPage(Integer pageNum, Integer pageSize);

    /**
     * 查询全量知识库
     *
     * @return
     */
    List<DatasetModel> findList();

    /**
     * 查询总数
     */
    long count();
}
