package com.sankuai.wmbdaiassistant.domain.service.chat.rag;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;

/**
 * RAG策略
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-28 10:39
 */
public interface RagStrategy {

    /**
     * 提问
     *
     * @param sessionBo
     * @param uid
     * @param bizId
     * @param questionMsgId
     * @param input
     * @param callback
     */
    void query(SessionBo sessionBo, Integer uid, Long bizId, Long questionMsgId, String input
            , String entryPoint, GeneralCallback callback, String version);

}
