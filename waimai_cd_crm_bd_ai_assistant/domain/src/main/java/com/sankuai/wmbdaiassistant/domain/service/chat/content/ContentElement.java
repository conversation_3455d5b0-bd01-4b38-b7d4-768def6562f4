package com.sankuai.wmbdaiassistant.domain.service.chat.content;

import java.util.Map;

/**
 * 内容的元素
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:04
 */
public interface ContentElement {
    /**
     * 是否支持将多个相同的元素合并成一个元素
     *
     * @return 是否支持
     */
    default boolean canMerge() {
        return false;
    }

    /**
     * 将两个相同的元素合并
     *
     * @param other 待合并的元素
     * @return
     */
    default void merge(ContentElement other) {
        throw new UnsupportedOperationException();
    }

    /**
     * 转换成 markdown 文本
     *
     * @return markdown 文本
     */
    String toMarkdownText();

    /**
     * 获取元素类型
     *
     * @return
     */
    String getType();

    /**
     * 转换成提供给TT侧的格式（不提供返回null）
     * 
     * @return
     */
    default Map<String, String> toTtTransferContent() {
        return null;
    }
}
