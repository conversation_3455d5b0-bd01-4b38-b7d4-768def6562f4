package com.sankuai.wmbdaiassistant.domain.model;

import com.meituan.mtrace.util.StringUtils;
import com.sankuai.wmbdaiassistant.domain.enums.WikiSourceTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.WikiStateEnum;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * wiki
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 17:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WikiModel {

    public static final String ES_ID = "_id";
    public static final String ID = "id";
    public static final String ID_KEYWORD = String.format("%s.keyword", ID);
    public static final String URL = "url";
    public static final String URL_KEYWORD = String.format("%s.keyword", URL);
    public static final String MIS = "mis";
    public static final String MIS_KEYWORD = String.format("%s.keyword", MIS);
    public static final String TAGS = "tags";
    public static final String TAGS_KEYWORD = String.format("%s.keyword", TAGS);
    public static final String WIKI_ID = "wiki_id";
    public static final String TITLE = "title";
    public static final String TITLE_KEYWORD = String.format("%s.keyword", TITLE);
    public static final String STATE = "state";
    public static final String STATE_KEYWORD = String.format("%s.keyword", STATE);
    public static final String SOURCE_TYPE = "type";
    public static final String SOURCE_TYPE_KEYWORD = String.format("%s.keyword", SOURCE_TYPE);
    public static final String BATCH_ID = "batch_id";
    public static final String BATCH_ID_KEYWORD = String.format("%s.keyword", BATCH_ID);
    public static final String DATA_SET_ID = "dataset_id";
    public static final String AUTO_UPDATE = "auto_update";
    public static final String NEED_SUB_WIKI = "need_sub_wiki";
    public static final String NEED_REFER_WIKI = "need_refer_wiki";
    public static final String HAS_FORMAT_PROBLEM = "has_format_problem";
    public static final String SYNC_WIKI_AUTH = "sync_wiki_auth";
    public static final String CREATE_TIME = "ctime";
    public static final String UPDATE_TIME = "utime";

    /**
     * ES 主键,创建规则为 {wikiId}-{batchId}
     */
    private String id;

    /**
     * WIKI ID
     */
    private Long wikiId;

    /**
     * 标题
     */
    private String title;

    /**
     * 链接
     */
    private String url;

    /**
     * 来源类型
     */
    private WikiSourceTypeEnum sourceType;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 更新人
     */
    private String mis;

    /**
     * 状态
     */
    private WikiStateEnum state;

    /**
     * 是否自动更新
     */
    private Boolean autoUpdate;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 是否需要子wiki
     */
    private Boolean needSubWiki;

    /**
     * 是否需要引用wiki
     */
    private Boolean needReferWiki;

    /**
     * 是否有格式问题
     */
    private Boolean hasFormatProblem;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否同步wiki权限
     */
    private Boolean syncWikiAuth;


    /**
     * 主键默认构造方式
     */
    public String getId() {
        if (StringUtils.isNotBlank(this.id) && !Objects.equals(this.id, "null")) {
            return this.id;
        }
        return String.format("%s-%s", wikiId, batchId);
    }


    public Map<String, Object> toMap(){
        Map<String, Object> wikiData = new HashMap<>();
        wikiData.put(WikiModel.ID, this.getId());
        wikiData.put(WikiModel.URL, this.getUrl());
        wikiData.put(WikiModel.TITLE, this.getTitle());
        wikiData.put(WikiModel.WIKI_ID, this.getWikiId());
        wikiData.put(WikiModel.DATA_SET_ID, this.getDatasetId());
        wikiData.put(WikiModel.TAGS, String.join(",", this.getTags()));
        wikiData.put(WikiModel.MIS, this.getMis());
        wikiData.put(WikiModel.STATE, this.getState() != null ? this.getState().getCode() : null);
        wikiData.put(WikiModel.AUTO_UPDATE, this.getAutoUpdate());
        wikiData.put(WikiModel.SOURCE_TYPE, this.getSourceType() != null ? this.getSourceType().getCode() : null);
        wikiData.put(WikiModel.BATCH_ID, this.getBatchId());
        wikiData.put(WikiModel.CREATE_TIME, this.getCreateTime() != null ? this.getCreateTime().getTime() : null);
        wikiData.put(WikiModel.UPDATE_TIME, System.currentTimeMillis());
        wikiData.put(WikiModel.NEED_SUB_WIKI, this.getNeedSubWiki());
        wikiData.put(WikiModel.NEED_REFER_WIKI, this.getNeedReferWiki());
        wikiData.put(WikiModel.HAS_FORMAT_PROBLEM, this.getHasFormatProblem());
        wikiData.put(WikiModel.SYNC_WIKI_AUTH, this.getSyncWikiAuth() != null ? this.getSyncWikiAuth() : Boolean.FALSE);
        return wikiData;
    }
}
