package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.common.TagUtil;
import com.sankuai.wmbdaiassistant.domain.enums.PhraseStateEnum;
import com.sankuai.wmbdaiassistant.domain.enums.TriggerTypeEnum;
import java.util.Objects;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 语料/问法
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-06 10:17
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PhraseModel implements Cloneable, Categorizable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 问法
     */
    private String phrase;

    /**
     * 标准问法
     */
    private String standardizedPhrase;

    /**
     * 触发器ID
     */
    private Long triggerId;

    /**
     * 触发器类型
     */
    private TriggerTypeEnum triggerType;

    /**
     * 修改人 mis
     */
    private String modifierMis;

    /**
     * 修改人名字
     */
    private String modifierName;

    /**
     * 状态
     */
    private PhraseStateEnum state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * vex平台返回的唯一id
     */
    private Long vexId;

    /**
     * 域ID
     */
    private Long domainId;

    /**
     *   挂载的目录ID
     */
    private Long categoryId;

    /**
     *   目录排序顺序
     */
    private Integer sortOrder;

    public boolean isStandardPhrase() {
        return StringUtils.isNotBlank(phrase) && StringUtils.isNotBlank(standardizedPhrase)
                && StringUtils.equals(phrase, standardizedPhrase);
    }

    @Override
    public PhraseModel clone() {
        PhraseModel phraseModel = new PhraseModel();

        phraseModel.setId(this.id);
        phraseModel.setState(this.state);
        phraseModel.setPhrase(this.phrase);
        phraseModel.setStandardizedPhrase(this.standardizedPhrase);
        phraseModel.setTriggerId(this.triggerId);
        phraseModel.setTriggerType(this.triggerType);
        phraseModel.setModifierMis(this.modifierMis);
        phraseModel.setModifierName(this.modifierName);
        phraseModel.setCreateTime(this.createTime);
        phraseModel.setModifyTime(this.modifyTime);
        phraseModel.setVexId(this.vexId);
        phraseModel.setDomainId(this.domainId);

        return phraseModel;
    }

    public String buildTag() {
        return TagUtil.buildTag(this.domainId, this.getPhrase());
    }

    public String buildStandardizedTag() {
        return TagUtil.buildTag(this.domainId, this.getStandardizedPhrase());
    }

    public boolean equals(PhraseModel model) {
        return model == null ? true
                : this.buildTag().equals(model.buildTag()) && Objects.equals(this.getId(), model.getId());
    }

    public boolean isEnable() {
        return PhraseStateEnum.ENABLE.equals(this.state);
    }
}
