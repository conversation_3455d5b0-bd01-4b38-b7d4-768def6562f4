package com.sankuai.wmbdaiassistant.domain.service.chat.vectordb;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问法的向量搜索
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-19 10:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhraseVectorQuery {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 问法
     */
    private String phrase;

    /**
     * 域的路径ID列表。例如：A-B，则在数组中应该依次包含域A的ID和域B的ID
     */
    private List<Long> domainPathIdList;

    /**
     * 是否过滤域
     */
    private Boolean filterByDomain;

    /**
     * 返回多少条数据
     */
    private Integer topN;

    /**
     * 最低分数(可选,范围[-1,1])
     */
    private Double minScore;

}
