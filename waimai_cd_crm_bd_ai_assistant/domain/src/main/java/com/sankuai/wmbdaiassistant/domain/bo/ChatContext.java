package com.sankuai.wmbdaiassistant.domain.bo;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 会话上下文
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-08-23 15:00
 */
@Data
public class ChatContext implements Serializable {

    /**
     * 多轮会话ID
     */
    private String id;

    /**
     * 多轮ID
     */
    private Long taskId;

    /**
     * 多轮使用的大模型
     */
    private String taskModel;

    /**
     * 多轮的 prompt
     */
    private String taskPrompt;

    /**
     * 触发问题ID
     */
    private Long triggerQuestionId;

    /**
     * 当前问题ID
     */
    private Long currentQuestionId;

    /**
     * 最相似问题的相似度，范围[-1,1]
     */
    private Double top1QuestionScore;

    /**
     * 最相似问题的ID（对应到 phrase 表的 ID）
     */
    private Long top1QuestionId;

    /**
     * 所属的域ID
     */
    private Long domainId;

    /**
     * 是否会话粘连
     */
    private Boolean stick;

    /**
     * 意图识别到的问题ID
     */
    private Long intentRecognitionQuestionId;

    /**
     * 是否使用历史会话
     */
    private boolean useHistory = true;

    /**
     * 召回的片段ID列表
     */
    private List<String> fragmentIdList;
}
