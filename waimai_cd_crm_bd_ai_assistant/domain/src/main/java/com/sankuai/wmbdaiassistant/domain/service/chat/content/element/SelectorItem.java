package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 选择项
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelectorItem implements ContentElement {
    private final String type = ContentElementTypeEnum.SELECTOR_ITEM.getCode();

    @JsonProperty("insert")
    private SelectorItemDetail detail;

    @Override
    public String toMarkdownText() {
        if (detail == null || detail.getDesc() == null) {
            return null;
        }
        SelectorItemDesc item = detail.getDesc();
        StringBuilder stringBuilder = new StringBuilder();
        String prefix = getPrefix(item.getType());
        if (item.getTitle() != null) {
            stringBuilder.append(String.format("%s：%s\n", prefix, item.getTitle()));
        }
        DefaultUtil.defaultList(item.getContents()).forEach(content -> {
            stringBuilder.append(String.format("%s的%s：%s\n", prefix, content.getLabel(), content.getValue()));
        });
        return stringBuilder.toString();
    }

    private String getPrefix(String type) {
        if ("reject".equals(type)) {
            return "驳回任务";
        }
        return "";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectorItemDetail {
        @JsonProperty("selectorItem")
        private SelectorItemDesc desc;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectorItemDesc {
        /**
         * 类型
         */
        private String type;
        /**
         * 标题
         */
        private String title;
        /**
         * 内容
         */
        @JsonProperty("content")
        private List<SelectorItemContent> contents;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectorItemContent {
        /**
         * 用于取值，默认值为label
         *  存在含义的key值：
         *  label: 标签，按照 `${label}:${value}` 的格式展示
         *  ID: 商家ID，展示同label，用于获取商家ID进行跳转
         *  avatar: 头像
         *  online: boolean，是否在线，标题右侧展示，在线为绿色背景，下线为灰色背景
         *  tag: 如营业执照已生效
         *
         */
        private String key;
        /**
         * 标签
         */
        private String label;
        /**
         * 值
         */
        private String value;
        /**
         * 是否展示
         */
        private Boolean show;
    }
}


