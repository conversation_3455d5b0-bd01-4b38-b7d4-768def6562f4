package com.sankuai.wmbdaiassistant.domain.service.chat.rag;

import com.google.common.collect.Lists;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.UserBo;

import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl.DefaultFragmentRagStrategy;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl.DxFragmentRagStrategy;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl.StandardQuestionRagStrategy;
import com.sankuai.wmbdaiassistant.domain.service.gray.GrayService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * RAG工厂
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-28 11:31
 */
@Component
public class RagFactory {

    @Resource
    private GrayService grayService;

    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private DxFragmentRagStrategy dxFragmentRagStrategy;

    @Resource
    private DefaultFragmentRagStrategy defaultFragmentRagStrategy;

    @Resource
    private StandardQuestionRagStrategy standardQuestionRagStrategy;


    public RagStrategy getRagStrategy(SessionBo session, UserBo user) {
        ParamCheckUtil.notNull(session, "session不能为空");

        List<String> dxSourceCodeList = Lists.newArrayList(SessionSourceConfigManager.DX_PRIVATE_CHAT_CODE, SessionSourceConfigManager.DX_GROUP_CHAT_CODE);
        if (dxSourceCodeList.contains(session.getSource())) {
            return dxFragmentRagStrategy;
        }

        if (SessionSourceConfigManager.SYSTEM.equals(session.getSource())
                && grayService.isGray(aiChatConfig.systemGrayModel, user)) {
            return standardQuestionRagStrategy;
        }

        return defaultFragmentRagStrategy;
    }

}
