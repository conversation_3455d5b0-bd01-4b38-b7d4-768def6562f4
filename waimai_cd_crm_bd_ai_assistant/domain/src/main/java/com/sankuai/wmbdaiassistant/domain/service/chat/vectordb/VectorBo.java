package com.sankuai.wmbdaiassistant.domain.service.chat.vectordb;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *向量库中的对象
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-12 18:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorBo implements Serializable {

    /**
     * 业务主键ID
     */
    @JsonProperty("_id")
    private Long id;

    /**
     * 向量库主键
     */
    private transient Long vectorId;

    /**
     * 向量值
     */
    private transient List<Double> vector;

    /**
     * 得分，分数越高，匹配度越高，范围在 [-1,1]
     */
    private Double score;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 领域
     */
    private List<Long> domainIdList;
}
