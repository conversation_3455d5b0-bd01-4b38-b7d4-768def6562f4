package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.OutputTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 输出
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-05 16:44
 */
@Getter
@Setter
@ToString
public class OutputModel {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 输出内容
     */
    private String content;

    /**
     * 类型
     */
    private OutputTypeEnum type;

    /**
     * TT链接
     */
    private String ttUrl;

    /**
     * 关联的图片列表
     */
    private List<String> picUrlList;

    /**
     * 标签，例如：poiId：当包含这个标签时，前端需要展示商家选择器
     */
    private List<String> tags;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 是否有效
     */
    private ValidEnum valid;
}
