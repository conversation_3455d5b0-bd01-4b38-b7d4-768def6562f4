package com.sankuai.wmbdaiassistant.domain.service.chat.impl;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.repository.PhraseRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.RecommendedQuestionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.dataset.DatasetAuthService;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricConstant;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricService;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.RagFactory;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.RagStrategy;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.PhraseVectorQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会话服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-12-14 13:34
 */
@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    @Resource
    private RagFactory ragFactory;

    @Resource
    private AiChatConfig aiChatConfig;

    @Resource
    private MetricService metricService;

    @Resource
    private PhraseRepository phraseRepository;

    @Resource
    private DatasetAuthService datasetAuthService;

    @Resource
    private VectorDatabaseService vectorDatabaseService;

    @Resource
    private RecommendedQuestionService recommendedQuestionService;

    @Override
    public void chat(SessionBo sessionBo, Integer uid, long bizId, long id, String input
            , String entryPoint, GeneralCallback callback, String version) {
        log.info("ChatServiceImpl chat，uid = {}, bizId = {}, input = {}, session = {}"
                , uid, bizId, input, JsonUtil.toJson(sessionBo));

        try {
            RagStrategy ragStrategy = ragFactory.getRagStrategy(sessionBo, new UserBo(sessionBo.getUid(), sessionBo.getMis()));

            log.info("ChatServiceImpl chat，uid = {}, bizId = {}, input = {}, session = {}, RagStrategy = {}"
                    , uid, bizId, input, JsonUtil.toJson(sessionBo), ragStrategy.getClass().getName());

            ragStrategy.query(sessionBo, uid, bizId, id, input, entryPoint, callback, version);
        } catch (Exception e) {
            log.error("ChatServiceImpl chat error, msg = {}", e.getMessage(), e);
            GeneralAnswerBo answer = new GeneralAnswerBo();
            answer.setMsgId(id);
            answer.setAnswer(aiChatConfig.aiChatDefaultMsg);
            answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
            answer.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());
            callback.answerCallback(answer);
        }
    }

    @Override
    public List<String> relativeQuestion(long bizId, String input) {
        PhraseVectorQuery query = PhraseVectorQuery.builder().phrase(input).filterByDomain(Boolean.FALSE)
                .topN(aiChatConfig.relativeQuestionFetchSize).build();
        List<VectorBo> phraseVectorList = vectorDatabaseService.searchPhrase(query);
        List<Long> greaterThanK2PhraseIdList = DefaultUtil.defaultList(phraseVectorList).stream().filter(Objects::nonNull)
                .filter(faqVectorBo -> faqVectorBo.getScore() > aiChatConfig.k2).map(VectorBo::getId).distinct()
                .collect(Collectors.toList());

        List<PhraseModel> phraseList = phraseRepository.findByIdList(greaterThanK2PhraseIdList);
        return DefaultUtil.defaultList(phraseList).stream().map(PhraseModel::getStandardizedPhrase)
                .distinct().limit(aiChatConfig.relativeQuestionTopN).collect(Collectors.toList());
    }

    @Override
    public List<String> fetchRecommendedQuestion(String input, Integer uid) {
        List<String> recommendedQuestions = metricService.recordCost(MetricConstant.BIZ, MetricConstant.RECOMMEND_QUESTION
                , () -> recommendedQuestionService.recommendQuestions(input, datasetAuthService.queryUserVisibleDataset(uid)
                , aiChatConfig.recommendedQuestionSize));
        if (CollectionUtils.isNotEmpty(recommendedQuestions)) {
            return CollectionUtils.size(recommendedQuestions) > aiChatConfig.recommendedQuestionSize
                    ? recommendedQuestions.subList(0, aiChatConfig.recommendedQuestionSize) : recommendedQuestions;
        }
        return recommendedQuestions;
    }
}
