package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 描述信息
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR>
 * @date 2025-05-07 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Title implements ContentElement {

    private final String type = ContentElementTypeEnum.TITLE.getCode();

    @JsonProperty("insert")
    private TitleObject insert;

    @Override
    public String toMarkdownText() {
        if (insert == null || insert.getTitle() == null) {
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("主标题：");
        stringBuilder.append(insert.getTitle().getTitle());
        stringBuilder.append("\n");
        stringBuilder.append("副标题：");
        stringBuilder.append(insert.getTitle().getSubTitle());
        return stringBuilder.toString();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TitleObject  {
        private TitleInfo title;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TitleInfo  {
        private String title;
        private String subTitle;

    }


}