package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 *
 *   表名: bd_ai_assistant_archive
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ArchiveModel {
    /**
     *   说明: 主键
     */
    private Long id;

    /**
     *   说明: 目录名称
     */
    private String name;

    /**
     *   说明: 业务线
     */
    private String bizLine;

    /**
     *   说明: 上级目录
     */
    private Long parentId;

    /**
     *   说明: 目录路径
     */
    private String path;

    /**
     *   说明: 同级目录序号
     */
    private Integer order;

    /**
     *   说明: 是否有效 0--无效 1--有效
     */
    private ValidEnum valid;

    /**
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   说明: 更新时间
     */
    private Date updateTime;
}