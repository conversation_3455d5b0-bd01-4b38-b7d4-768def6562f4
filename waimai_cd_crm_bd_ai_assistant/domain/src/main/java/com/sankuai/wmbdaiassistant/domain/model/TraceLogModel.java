package com.sankuai.wmbdaiassistant.domain.model;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatStreamChunkBo;
import com.sankuai.wmbdaiassistant.domain.bo.FragmentReferenceBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.EntryPointTypeEnum;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.element.OptionList;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.PhraseVectorQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 埋点日志
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-28 17:12
 */
@Data
public class TraceLogModel {

    private static final String EVENT_DISPLAY = "display";
    public static final String EVENT_TRIGGER = "trigger";
    private static final String DOMAIN_RECOGNIZE = "domain_recognize";
    private static final String PHRASE_VECTOR_QUERY = "phrase_vector_query";
    private static final String INTENTION_RECOGNIZE = "intention_recognize";
    private static final String REARRANGE = "rearrange";
    private static final String REARRANGE_PROMPT_FAQ_LIST = "rearrange_prompt_faq_list";
    private static final String FLOATING_OPTION_LIST = "floating_option_list";
    private static final String FRAGMENT_RAG = "fragment_rag";
    private static final String FRAGMENT_RECALL = "fragment_recall";

    private static final String PICTURE_QUESTION = "picture_question";
    private static final String POI_REJECT_TASK_BIZ_TYPE = "poi_reject_task_biz_type";
    private static final String POI_REJECT_TASK_ANSWER_TYPE = "poi_reject_task_answer_type";
    /**
     * ID
     */
    private Long id;

    /**
     * 会话ID
     */
    private Long sessionId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 入口点
     */
    private String entryPoint;

    /**
     * 内容
     */
    private String content;

    /**
     * 埋点时间
     */
    private Date date;

    public static TraceLogModel buildGeneralChatTriggerTraceLog(SessionBo sessionBo, Long msgId
            , String input, String entryPoint) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(EVENT_TRIGGER);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildGeneralChatTriggerTraceLogContent(sessionBo.fetchTaskSessionId()
                , sessionBo.fetchTaskId(), msgId, input));
        return traceLogModel;
    }

    public static TraceLogModel buildCategoryChatTriggerTraceLog(SessionBo sessionBo, Long msgId
            , String entryPoint, String categoryName) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(EVENT_TRIGGER);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildCategoryChatTriggerTraceLogContent(msgId, sessionBo.fetchTaskSessionId()
                , sessionBo.fetchTaskId(), categoryName));
        return traceLogModel;
    }

    public static List<TraceLogModel> buildCategoryChatDisplayTraceLog(SessionBo sessionBo, String answerContent) {
        List<OptionList> optionLists = JsonUtil.fromJson(answerContent, new TypeReference<List<OptionList>>() {});
        return buildOptionListDisplayTraceLog(sessionBo, optionLists);
    }

    public static List<TraceLogModel> buildGuidanceFetchItemTraceLog(Long sessionId, String entryPoint
            , String tabName, Integer pageNum, String answerContent, boolean showRefresh) {
        List<OptionList> optionLists = JsonUtil.fromJson(answerContent, new TypeReference<List<OptionList>>() {});

        List<TraceLogModel> traceLogModelList = new ArrayList<>();
        // 增加曝光日志
        traceLogModelList.addAll(TraceLogModel.buildGuidanceFetchItemDisplayTraceLog(sessionId, tabName, pageNum
                , optionLists, showRefresh));
        // 增加点击日志
        traceLogModelList.addAll(TraceLogModel.buildGuidanceFetchItemTriggerTraceLog(sessionId, entryPoint, tabName
                , pageNum, optionLists));
        return traceLogModelList;
    }

    private static List<TraceLogModel> buildGuidanceFetchItemDisplayTraceLog(Long sessionId
            , String tabName, Integer pageNum, List<OptionList> optionLists, boolean showRefresh) {
        return DefaultUtil.defaultList(optionLists).stream()
                .filter(o -> o != null && o.getInfo() != null && o.getInfo().getTabOptionInfo() != null)
                .flatMap(optionList -> {
                    OptionList.TabOptionInfo tabOptionInfo = optionList.getInfo().getTabOptionInfo();
                    List<String> tabList = DefaultUtil.defaultList(tabOptionInfo.getTabs()).stream()
                            .map(OptionList.TabItem::getLabel).collect(Collectors.toList());
                    int index = tabList.indexOf(tabName);
                    ParamCheckUtil.isTrue(index >= 0, "无法找到Tab");

                    int tabIndex = index + 1;

                    // 记录子项的曝光（不再针对 Tab 记录曝光日志）
                    List<OptionList.TabItem> tabItemList = tabOptionInfo.getTabs();
                    tabOptionInfo.setTabs(null);
                    List<TraceLogModel> itemLogList = new ArrayList<>(DefaultUtil.defaultList(
                            buildGuidanceDisplayTraceLog(sessionId, tabIndex, pageNum, tabName, tabOptionInfo)));
                    tabOptionInfo.setTabs(tabItemList);

                    // 增加"换一换"功能的曝光
                    ExecutorUtil.executeIfTrue(showRefresh, () -> itemLogList.add(buildTabRefreshDisplayTraceLog(
                            sessionId, tabIndex, pageNum, tabName)));

                    return itemLogList.stream();
                }).collect(Collectors.toList());
    }

    private static List<TraceLogModel> buildGuidanceFetchItemTriggerTraceLog(Long sessionId, String entryPoint
            , String tabName, Integer pageNum, List<OptionList> optionLists) {
        return DefaultUtil.defaultList(optionLists).stream()
                .filter(o -> o != null && o.getInfo() != null && o.getInfo().getTabOptionInfo() != null)
                .map(optionList -> {
                    OptionList.TabOptionInfo tabOptionInfo = optionList.getInfo().getTabOptionInfo();
                    List<String> tabList = DefaultUtil.defaultList(tabOptionInfo.getTabs()).stream()
                            .map(OptionList.TabItem::getLabel).collect(Collectors.toList());
                    int index = tabList.indexOf(tabName);
                    ParamCheckUtil.isTrue(index >= 0, "无法找到Tab");

                    TraceLogModel traceLogModel = new TraceLogModel();
                    traceLogModel.setSessionId(sessionId);
                    traceLogModel.setEventType(EVENT_TRIGGER);
                    traceLogModel.setEntryPoint(entryPoint);
                    traceLogModel.setContent(buildFetchCategoryItemTraceLogContent(index + 1, pageNum, tabName));

                    return traceLogModel;
                }).collect(Collectors.toList());
    }

    public static List<TraceLogModel> buildGuidanceFirstTabTraceLog(Long sessionId, String answerContent
            , boolean showRefresh) {
        List<OptionList> optionLists = JsonUtil.fromJson(answerContent, new TypeReference<List<OptionList>>() {});

        List<TraceLogModel> traceLogModelList = new ArrayList<>();
        // 增加曝光日志
        traceLogModelList.addAll(TraceLogModel.buildGuidanceFirstTabDisplayTraceLog(sessionId, optionLists, showRefresh));
        // 增加点击日志
        traceLogModelList.addAll(TraceLogModel.buildGuidanceFirstTabTriggerTraceLog(sessionId, optionLists));
        return traceLogModelList;
    }

    private static List<TraceLogModel> buildGuidanceFirstTabDisplayTraceLog(Long sessionId
            , List<OptionList> optionLists, boolean showRefresh) {
        return DefaultUtil.defaultList(optionLists).stream()
                .filter(o -> o != null && o.getInfo() != null && o.getInfo().getTabOptionInfo() != null)
                .flatMap(optionList -> {
                    OptionList.TabOptionInfo tabOptionInfo = optionList.getInfo().getTabOptionInfo();
                    List<String> tabList = DefaultUtil.defaultList(tabOptionInfo.getTabs()).stream()
                            .map(OptionList.TabItem::getLabel).collect(Collectors.toList());

                    // Tab 和 子项的曝光
                    List<TraceLogModel> itemLogList = new ArrayList<>(DefaultUtil.defaultList(
                            buildGuidanceDisplayTraceLog(sessionId, 1, 1, tabList.get(0), tabOptionInfo)));

                    // "换一换"功能的曝光
                    ExecutorUtil.executeIfTrue(showRefresh, () -> itemLogList.add(buildTabRefreshDisplayTraceLog(sessionId
                            , 1, 1, tabList.get(0))));

                    return itemLogList.stream();
                }).collect(Collectors.toList());
    }

    private static List<TraceLogModel> buildGuidanceFirstTabTriggerTraceLog(Long sessionId, List<OptionList> optionLists) {
        return DefaultUtil.defaultList(optionLists).stream()
                .filter(o -> o != null && o.getInfo() != null && o.getInfo().getTabOptionInfo() != null)
                .map(optionList -> {
                    OptionList.TabOptionInfo tabOptionInfo = optionList.getInfo().getTabOptionInfo();
                    List<String> tabList = DefaultUtil.defaultList(tabOptionInfo.getTabs()).stream()
                            .map(OptionList.TabItem::getLabel).collect(Collectors.toList());

                    TraceLogModel traceLogModel = new TraceLogModel();
                    traceLogModel.setSessionId(sessionId);
                    traceLogModel.setEventType(EVENT_TRIGGER);
                    traceLogModel.setEntryPoint(buildTabNEntryPoint(1));
                    traceLogModel.setContent(buildFetchCategoryItemTraceLogContent(1, 1, tabList.get(0)));

                    return traceLogModel;
                }).collect(Collectors.toList());
    }

    private static List<TraceLogModel> buildGuidanceDisplayTraceLog(Long sessionId, int tabIndex
            , Integer pageNum, String tabName, OptionList.TabOptionInfo tabOptionInfo) {
        List<TraceLogModel> traceLogModelList = new ArrayList<>();

        if (tabOptionInfo == null) {
            return traceLogModelList;
        }

        // Tab 曝光
        List<OptionList.TabItem> tabItemList = DefaultUtil.defaultList(tabOptionInfo.getTabs());
        IntStream.range(0, DefaultUtil.defaultList(tabItemList).size()).forEach(index -> {
            OptionList.TabItem tab = tabItemList.get(index);
            TraceLogModel tabTraceLog = new TraceLogModel();
            tabTraceLog.setSessionId(sessionId);
            tabTraceLog.setEntryPoint(buildTabNEntryPoint(index + 1));
            tabTraceLog.setEventType(EVENT_DISPLAY);
            tabTraceLog.setContent(buildCategoryTraceLogContent(index + 1, tab.getLabel()));
            traceLogModelList.add(tabTraceLog);
        });

        // 子项曝光
        List<OptionList.OptionItem> optionItemList = DefaultUtil.defaultList(tabOptionInfo.getOptionItems());
        IntStream.range(0, optionItemList.size()).forEach(index -> {
            OptionList.OptionItem optionItem = optionItemList.get(index);

            TraceLogModel traceLogModel = new TraceLogModel();

            traceLogModel.setSessionId(sessionId);
            traceLogModel.setEntryPoint(buildTabOptionItemNEntryPoint(tabIndex, index + 1));
            traceLogModel.setEventType(EVENT_DISPLAY);

            AbilityTypeEnum abilityTypeEnum = AbilityTypeEnum.findByCode(optionItem.getAbilityType());
            if (abilityTypeEnum == AbilityTypeEnum.CATEGORY) {
                traceLogModel.setContent(buildTabCategoryTraceLogContent(tabIndex, pageNum, tabName, optionItem.getContent()));
            }
            if (abilityTypeEnum == AbilityTypeEnum.GENERAL) {
                traceLogModel.setContent(buildTabQuestionTraceLogContent(tabIndex, pageNum, tabName, optionItem.getContent()));
            }
            if (abilityTypeEnum == AbilityTypeEnum.JUMP) {
                traceLogModel.setContent(buildTabJumpUrlTraceLogContent(tabIndex, pageNum, tabName
                        , optionItem.getContent(), optionItem.getUrl()));
            }

            traceLogModelList.add(traceLogModel);
        });

        return traceLogModelList;
    }

    private static TraceLogModel buildTabRefreshDisplayTraceLog(Long sessionId, Integer tabIndex
            , Integer pageNum, String tabName) {
        TraceLogModel refreshLog = new TraceLogModel();
        refreshLog.setSessionId(sessionId);
        refreshLog.setEntryPoint(buildTabRefreshEntryPoint(tabIndex));
        refreshLog.setEventType(EVENT_DISPLAY);
        refreshLog.setContent(buildTabRefreshTraceLogContent(tabIndex, tabName, pageNum));
        return refreshLog;
    }

    public static List<TraceLogModel> buildRelativeQuestionDisplayTraceLog(Long sessionId
            , String input, List<String> questionList) {
        return IntStream.range(0, DefaultUtil.defaultList(questionList).size()).mapToObj(index -> {
            String relativeQuestion = questionList.get(index);
            TraceLogModel traceLogModel = new TraceLogModel();
            traceLogModel.setSessionId(sessionId);
            traceLogModel.setEntryPoint(EntryPointTypeEnum.INPUT_PREDICTION.name().toLowerCase());
            traceLogModel.setEventType(EVENT_DISPLAY);
            traceLogModel.setContent(buildRelativeQuestionTraceLogContent(input, relativeQuestion, index + 1));
            return traceLogModel;
        }).collect(Collectors.toList());
    }

    public static List<TraceLogModel> buildFloatingOptionDisplayTraceLog(Long sessionId, String input
            , List<String> phraseList) {
        return IntStream.range(0, DefaultUtil.defaultList(phraseList).size()).mapToObj(index -> {
            TraceLogModel traceLogModel = new TraceLogModel();
            traceLogModel.setSessionId(sessionId);
            traceLogModel.setEventType(EVENT_DISPLAY);
            traceLogModel.setEntryPoint(FLOATING_OPTION_LIST);
            traceLogModel.setContent(buildFloatingOptionTraceLogContent(input, phraseList.get(index), index + 1));
            return traceLogModel;
        }).collect(Collectors.toList());
    }

    public static List<TraceLogModel> buildRecommendedQuestionDisplayTraceLog(Long sessionId, String input
            , List<String> recommendedQuestionList) {
        return IntStream.range(0, DefaultUtil.defaultList(recommendedQuestionList).size()).mapToObj(index -> {
            TraceLogModel traceLogModel = new TraceLogModel();
            traceLogModel.setSessionId(sessionId);
            traceLogModel.setEventType(EVENT_DISPLAY);
            traceLogModel.setEntryPoint(FLOATING_OPTION_LIST);
            traceLogModel.setContent(buildFloatingOptionTraceLogContent(input, recommendedQuestionList.get(index), index + 1));
            return traceLogModel;
        }).collect(Collectors.toList());
    }

    public static List<TraceLogModel> buildFragmentRecallTraceLog(Long sessionId, Long id, String input
        , List<FragmentModel> fragmentList, String entryPoint, Map<String, Double> scoreMap) {
        return IntStream.range(0, DefaultUtil.defaultList(fragmentList).size()).mapToObj(index -> {
            TraceLogModel traceLogModel = new TraceLogModel();
            traceLogModel.setSessionId(sessionId);
            traceLogModel.setEventType(FRAGMENT_RECALL);
            traceLogModel.setEntryPoint(entryPoint);
            traceLogModel.setContent(buildFragmentRecallContent(id, input, fragmentList.get(index), index + 1,
                scoreMap.get(fragmentList.get(index).getId())));
            return traceLogModel;
        }).collect(Collectors.toList());
    }

    public static TraceLogModel buildFragmentRag(Long sessionId, Long id, String input, String entryPoint,
            ChatStreamChunkBo chunkBo, List<FragmentModel> fragmentList) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionId);
        traceLogModel.setEventType(FRAGMENT_RAG);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildFragmentRagContent(id, input, chunkBo, fragmentList));
        return traceLogModel;
    }

    public static List<TraceLogModel> buildTaskDisplayTraceLog(SessionBo sessionBo, List<ContentElement> answer) {
        List<OptionList> optionLists = DefaultUtil.defaultList(answer).stream().filter(s -> s instanceof OptionList)
                .map(s -> (OptionList) s)
                .collect(Collectors.toList());

        // 多轮现在只记录选项消息的日志
        return buildOptionListDisplayTraceLog(sessionBo, optionLists);
    }

    private static List<TraceLogModel> buildOptionListDisplayTraceLog(SessionBo sessionBo, List<OptionList> answer) {
        return DefaultUtil.defaultList(answer).stream()
                .filter(o -> o != null && o.getInfo() != null && o.getInfo().getTabOptionInfo() != null)
                .flatMap(optionList -> {
                    List<OptionList.OptionItem> itemList = DefaultUtil.defaultList(optionList.getInfo()
                            .getTabOptionInfo().getOptionItems());
                    return IntStream.range(0, itemList.size()).mapToObj(index -> {
                        OptionList.OptionItem optionItem = itemList.get(index);
                        TraceLogModel traceLogModel = new TraceLogModel();
                        traceLogModel.setSessionId(sessionBo.getSessionId());
                        traceLogModel.setEventType(EVENT_DISPLAY);
                        traceLogModel.setEntryPoint(buildOptionItemNEntryPoint(index + 1));

                        AbilityTypeEnum abilityTypeEnum = AbilityTypeEnum.findByCode(optionItem.getAbilityType());
                        if (abilityTypeEnum == AbilityTypeEnum.CATEGORY) {
                            traceLogModel.setContent(buildCategoryOptionItemTraceLogContent(sessionBo.fetchTaskId()
                                    , sessionBo.fetchTaskSessionId(), index + 1, optionItem.getContent()));
                        }
                        if (abilityTypeEnum == AbilityTypeEnum.GENERAL) {
                            traceLogModel.setContent(buildQuestionOptionItemTraceLogContent(sessionBo.fetchTaskId()
                                    , sessionBo.fetchTaskSessionId(), index + 1, optionItem.getContent()));
                        }
                        if (abilityTypeEnum == AbilityTypeEnum.JUMP) {
                            traceLogModel.setContent(buildJumpUrlOptionItemTraceLogContent(sessionBo.fetchTaskId(),
                                    sessionBo.fetchTaskSessionId(), index + 1
                                    , optionItem.getContent(), optionItem.getUrl()));
                        }
                        return traceLogModel;
                    });
                }).collect(Collectors.toList());
    }

    public static TraceLogModel buildDomainRecognizeTraceLog(SessionBo sessionBo, Long msgId, String entryPoint,
            String input, Long domainId) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(DOMAIN_RECOGNIZE);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildDomainRecognizeContent(msgId, input, domainId));
        return traceLogModel;
    }

    public static TraceLogModel buildPhraseVectorQueryTraceLog(SessionBo sessionBo, Long msgId, String entryPoint,
            PhraseVectorQuery phraseVectorQuery, List<VectorBo> phraseVectorList) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(PHRASE_VECTOR_QUERY);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildPhraseVectorQueryContent(msgId, phraseVectorQuery, phraseVectorList));
        return traceLogModel;
    }

    public static TraceLogModel buildIntentionRecognizeTraceLog(SessionBo sessionBo, Long msgId, String entryPoint,
            String input, List<Long> phraseIdList) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(INTENTION_RECOGNIZE);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildIntentionRecognizeContent(msgId, input, phraseIdList));
        return traceLogModel;
    }

    public static TraceLogModel buildRearrangeTraceLog(SessionBo sessionBo, Long msgId, String entryPoint, String input,
            List<String> embeddingTopQuestions, int topK, List<Long> phraseIdList) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(REARRANGE);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel
                .setContent(buildRearrangeContent(msgId, input, embeddingTopQuestions, topK, phraseIdList));
        return traceLogModel;
    }

    public static TraceLogModel buildRearrangePromptFaqTraceLog(SessionBo sessionBo, Long msgId, String entryPoint,
            String input, List<PhraseModel> faqList) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(REARRANGE_PROMPT_FAQ_LIST);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent(buildRearrangePromptFaqListContent(msgId, input, faqList));
        return traceLogModel;
    }

    public static TraceLogModel buildPictureQuestionTraceLog(SessionBo sessionBo, String entryPoint) {
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionBo.getSessionId());
        traceLogModel.setEventType(PICTURE_QUESTION);
        traceLogModel.setEntryPoint(entryPoint);
        traceLogModel.setContent("");
        return traceLogModel;
    }

    public static TraceLogModel buildPoiRejectTaskTypeTraceLog(Long sessionId, List<String> bizTypeStrList) {
        if (CollectionUtils.isEmpty(bizTypeStrList)) {
            return null;
        }
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionId);
        traceLogModel.setEventType(POI_REJECT_TASK_BIZ_TYPE);
        traceLogModel.setEntryPoint("");
        traceLogModel.setContent(String.join(",", bizTypeStrList));
        return traceLogModel;
    }

    public static TraceLogModel buildPoiRejectTaskAnswerTypeTraceLog(Long sessionId, List<Integer> answerTypeList) {
        if (CollectionUtils.isEmpty(answerTypeList)) {
            return null;
        }
        TraceLogModel traceLogModel = new TraceLogModel();
        traceLogModel.setSessionId(sessionId);
        traceLogModel.setEventType(POI_REJECT_TASK_ANSWER_TYPE);
        traceLogModel.setEntryPoint("");
        traceLogModel.setContent(answerTypeList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        return traceLogModel;
    }



    private static String buildTabNEntryPoint(int nLevel) {
        return String.format("tab%d", nLevel);
    }

    private static String buildTabOptionItemNEntryPoint(int nLevel, int nOptionItem) {
        return String.format("%s_%s", buildTabNEntryPoint(nLevel), buildOptionItemNEntryPoint(nOptionItem));
    }

    private static String buildOptionItemNEntryPoint(int nOptionItem) {
        return String.format("option_list%d", nOptionItem);
    }

    private static String buildTabRefreshEntryPoint(int nLevel) {
        return String.format("tab%d-refresh", nLevel);
    }

    private static String buildCategoryTraceLogContent(Integer categoryIndex, String categoryName) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("categoryIndex", categoryIndex).put("categoryName", categoryName).build());
    }

    private static String buildTabCategoryTraceLogContent(Integer tabIndex, Integer pageNum
            , String tabName, String categoryName) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("parentTabIndex", tabIndex).put("parentTabName", tabName).put("type", "category")
                .put("pageNum", pageNum).put("categoryName", categoryName).build());
    }

    private static String buildTabQuestionTraceLogContent(Integer tabIndex, Integer pageNum
            , String tabName, String question) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("parentTabIndex", tabIndex).put("parentTabName", tabName).put("pageNum", pageNum)
                .put("type", "question").put("question", question).build());
    }

    private static String buildTabJumpUrlTraceLogContent(Integer tabIndex, Integer pageNum
            , String tabName, String name, String url) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("parentTabIndex", tabIndex).put("parentTabName", tabName).put("pageNum", pageNum)
                .put("type", "url").put("name", name).put("url", DefaultUtil.defaultValue(url, "")).build());
    }

    private static String buildTabRefreshTraceLogContent(Integer tabIndex, String tabName, Integer pageNum) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("parentTabIndex", tabIndex).put("parentTabName", tabName)
                .put("type", "refresh").put("pageNum", pageNum).build());
    }

    private static String buildRelativeQuestionTraceLogContent(String input, String relativeQuestion, int index) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("input", input).put("relativeQuestion", relativeQuestion).put("index", index).build());
    }

    private static String buildFloatingOptionTraceLogContent(String input, String phrase, int index) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("input", input).put("question", phrase)
                .put("index", index).build());
    }

    private static String buildCategoryOptionItemTraceLogContent(Long taskId
            , String taskSessionId, Integer index, String option) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("taskSessionId", DefaultUtil.defaultValue(taskSessionId, ""))
                .put("type", "category").put("index", index).put("taskId", DefaultUtil.defaultValue(taskId, -1L))
                .put("categoryName", option).build());
    }

    private static String buildQuestionOptionItemTraceLogContent(Long taskId
            , String taskSessionId, Integer index, String question) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("taskSessionId", DefaultUtil.defaultValue(taskSessionId, ""))
                .put("index", index).put("taskId", DefaultUtil.defaultValue(taskId, -1L))
                .put("type", "question").put("question", question).build());
    }

    private static String buildJumpUrlOptionItemTraceLogContent(Long taskId, String taskSessionId
            , Integer index, String name, String url) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("taskSessionId", DefaultUtil.defaultValue(taskSessionId, "")).put("index", index)
                .put("taskId", DefaultUtil.defaultValue(taskId, -1L))
                .put("type", "url").put("name", name).put("url", url).build());
    }

    private static String buildGeneralChatTriggerTraceLogContent(String taskSessionId, Long taskId
            , Long msgId, String input) {

        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("taskSessionId", DefaultUtil.defaultValue(taskSessionId, ""))
                .put("msgId", msgId).put("input", input)
                .put("taskId", DefaultUtil.defaultValue(taskId, -1L)).build());
    }

    private static String buildCategoryChatTriggerTraceLogContent(Long msgId, String taskSessionId
            , Long taskId, String categoryName) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("taskSessionId", DefaultUtil.defaultValue(taskSessionId, ""))
                .put("taskId", DefaultUtil.defaultValue(taskId, -1L)).put("msgId", msgId)
                .put("categoryName", categoryName).build());
    }

    private static String buildFetchCategoryItemTraceLogContent(Integer tabIndex, Integer pageNum, String tabName) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
                .put("tabIndex", tabIndex).put("tabName", tabName).put("pageNum", pageNum).build());
    }

    private static String buildDomainRecognizeContent(Long msgId, String input, Long domainId) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder().put("msgId", msgId).put("input", input)
                .put("domainId", DefaultUtil.defaultValue(domainId, ""))
                .build());
    }

    private static String buildPhraseVectorQueryContent(Long msgId, PhraseVectorQuery phraseVectorQuery,
            List<VectorBo> phraseVectorList) {
        return JsonUtil.toJson(
                ImmutableMap.<String, Object>builder().put("msgId", msgId).put("phraseVectorQuery", phraseVectorQuery)
                .put("phraseIdList",
                        DefaultUtil.defaultList(phraseVectorList).stream().map(VectorBo::getId)
                                .collect(Collectors.toList()))
                .put("phraseVectorList", DefaultUtil.defaultList(phraseVectorList).stream()
                        .map(item -> ImmutableMap.<String, Object>builder().put("id", item.getId())
                                .put("score", item.getScore()).put("domainIdList", item.getDomainIdList()).build())
                        .collect(Collectors.toList()))
                .build());
    }

    private static String buildIntentionRecognizeContent(Long msgId, String input,
            List<Long> phraseIdList) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder().put("msgId", msgId).put("input", input)
                .put("phraseIdList", DefaultUtil.defaultList(phraseIdList))
                .build());
    }

    private static String buildRearrangeContent(Long msgId, String input,
            List<String> embeddingTopQuestions, int topK, List<Long> phraseIdList) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder().put("msgId", msgId).put("input", input)
                .put("embeddingTopQuestions", DefaultUtil.defaultList(embeddingTopQuestions)).put("topK", topK)
                .put("phraseIdList", DefaultUtil.defaultList(phraseIdList)).build());
    }

    private static String buildRearrangePromptFaqListContent(Long msgId, String input, List<PhraseModel> faqList) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder().put("msgId", msgId).put("input", input)
            .put("phraseList", DefaultUtil.defaultList(faqList).stream().map(faq ->
                    ImmutableMap.<String, Object>builder().put("phraseId", faq.getId()).put("phrase", faq.getPhrase())
                        .put("standardizedPhrase", faq.getStandardizedPhrase()).build())
                .collect(Collectors.toList()))
            .build());
    }

    private static String buildFragmentRecallContent(Long msgId, String input, FragmentModel fragmentModel, int index,
        Double score) {
        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
            .put("msgId", msgId).put("input", input).put("fragmentId", fragmentModel.getId())
            .put("datasetId", fragmentModel.getDatasetId()).put("batchId", fragmentModel.getBatchId())
            .put("wikiId", fragmentModel.getWikiId()).put("index", index)
            .put("fourth_archive", getFourLevelTag(fragmentModel.getTags()))
            .put("score", score != null ? score : 0.0).build());
    }

    private static String buildFragmentRagContent(Long msgId, String input, ChatStreamChunkBo streamChunkBo,
            List<FragmentModel> fragmentList) {
        if (streamChunkBo == null) {
            return "{}";
        }
        List<FragmentReferenceBo> referenceBoList = DefaultUtil.defaultList(streamChunkBo.getReferences()).stream()
                .sorted(Comparator.comparing(FragmentReferenceBo::getScore).reversed()).collect(Collectors.toList());
        Map<String, Map<String, Object>> recallDataMap = new HashMap<>();
        for (int i = 0; i < DefaultUtil.defaultList(referenceBoList).size(); i++) {
            FragmentReferenceBo referenceBo = referenceBoList.get(i);
            Map<String,Object> dataMap = new HashMap<>();
            dataMap.put("index", i);
            dataMap.put("score", referenceBo.getScore());
            dataMap.put("fragmentId", referenceBo.getId());
            recallDataMap.put(DefaultUtil.defaultString(referenceBo.getId()), dataMap);
        }
        for (FragmentModel model : DefaultUtil.defaultList(fragmentList)) {
            if (MapUtils.isEmpty(recallDataMap) || MapUtils.isEmpty(recallDataMap.get(model.getId()))) {
                continue;
            }
            recallDataMap.get(model.getId()).put("datasetId", model.getDatasetId());
            recallDataMap.get(model.getId()).put("batchId", model.getBatchId());
            recallDataMap.get(model.getId()).put("wikiId", model.getWikiId());
            recallDataMap.get(model.getId()).put("fourth_archive", getFourLevelTag(model.getTags()));
        }

        List<Map<String,Object>> recallDataList = recallDataMap.values().stream().collect(Collectors.toList());

        return JsonUtil.toJson(ImmutableMap.<String, Object>builder()
            .put("msgId", DefaultUtil.defaultValue(msgId, ""))
            .put("input", DefaultUtil.defaultValue(input, ""))
            .put("tags", DefaultUtil.defaultMap(streamChunkBo.getTags())   )
            .put("recallSize", DefaultUtil.defaultValue(recallDataMap.size(), 0))
            .put("recallData", DefaultUtil.defaultList(recallDataList))
            .put("fullAnswerContent", DefaultUtil.defaultValue(streamChunkBo.getFullContent(), "")).build());
    }

    private static String getFourLevelTag(List<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return "";
        }
        String firstTag = tags.get(0);
        if (StringUtils.isBlank(firstTag) || Objects.equals(firstTag, "null")) {
            return "";
        }
        String[] splitTags = firstTag.split("-");
        return splitTags.length > 0 ? splitTags[splitTags.length - 1] : "";
    }
}
