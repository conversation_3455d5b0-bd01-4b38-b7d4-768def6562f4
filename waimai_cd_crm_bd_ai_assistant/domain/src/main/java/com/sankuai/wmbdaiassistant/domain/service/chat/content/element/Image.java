package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片
 * 格式参照：https://km.sankuai.com/collabpage/2263992064#b-e419a052726244b3ad10a7d781906a06
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-20 16:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Image implements ContentElement {
    private final String type = ContentElementTypeEnum.IMAGE.getCode();

    @JsonProperty("insert")
    private ImageInfo info;

    public static Image build(String url) {
        return Image.builder().info(ImageInfo.builder().url(url).build()).build();
    }

    @Override
    public String toString() {
        return info == null ? "" : info.getUrl();
    }

    @Override
    public String toMarkdownText() {
        return String.format("![图片](%s)", info == null ? "" : DefaultUtil.defaultValue(info.getUrl(), ""));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageInfo {
        @JsonProperty("image")
        private String url;
    }

    @Override
    public Map<String, String> toTtTransferContent() {
        Map<String, String> map = new HashMap<>();
        map.put("type", "image");
        map.put("url", info.getUrl());
        return map;
    }
}
