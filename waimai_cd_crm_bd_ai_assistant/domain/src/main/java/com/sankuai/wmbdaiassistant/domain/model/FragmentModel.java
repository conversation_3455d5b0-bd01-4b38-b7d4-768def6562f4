package com.sankuai.wmbdaiassistant.domain.model;

import com.meituan.mtrace.util.StringUtils;
import com.sankuai.wmbdaiassistant.domain.enums.FragmentStateEnum;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 分片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 17:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FragmentModel {

    public static final String ID = "id";
    public static final String ID_KEYWORD = "id.keyword";
    public static final String TITLE = "title";
    public static final String TITLE_KEYWORD = "title.keyword";
    public static final String TAGS = "tags";
    public static final String TAGS_KEYWORD = "tags.keyword";
    public static final String STATE = "state";
    public static final String CONTENT = "content";
    public static final String CONTENT_KEYWORD = "content.keyword";
    public static final String CHUNK_ID = "chunk_id";
    public static final String WIKI_ID = "wiki_id";
    public static final String BATCH_ID = "batch_id";
    public static final String BATCH_ID_KEYWORD = "batch_id.keyword";
    public static final String STATE_KEYWORD = "state.keyword";
    public static final String DATA_SET_ID = "dataset_id";
    public static final String CREATE_TIME = "ctime";
    public static final String UPDATE_TIME = "utime";

    /**
     * ES 主键 规则为: {wikiId}-{batchId}-{chunkId}
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * wiki ID
     */
    private Long wikiId;

    /**
     * 分片ID
     */
    private Integer chunkId;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 状态
     */
    private FragmentStateEnum state;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 相似度评分
     */
    private String score;

    /**
     * 主键默认构造方式
     */
    public String getId(){
        if(StringUtils.isNotBlank(this.id) && !Objects.equals(this.id, "null")){
            return this.id;
        }
        return this.wikiId + "-" + this.batchId + "-" + this.chunkId;
    }

    public String getWikiModelId() {
        return String.format("%s-%s", wikiId, batchId);
    }

    /**
     * 转换为ES数据MAP
     * @return
     */
    public Map<String, Object> toMap() {
        Map<String, Object> itemData = new HashMap<>();
        itemData.put(FragmentModel.ID, this.getId());
        itemData.put(FragmentModel.CONTENT, this.getContent());
        itemData.put(FragmentModel.WIKI_ID, this.getWikiId());
        itemData.put(FragmentModel.CHUNK_ID, this.getChunkId());
        itemData.put(FragmentModel.STATE, this.getState() != null ? this.getState().getCode() : null);
        itemData.put(FragmentModel.DATA_SET_ID, this.getDatasetId());
        itemData.put(FragmentModel.BATCH_ID, this.getBatchId());
        itemData.put(FragmentModel.CREATE_TIME, this.getCreateTime() != null ? this.getCreateTime().getTime() : null);
        itemData.put(FragmentModel.UPDATE_TIME, System.currentTimeMillis());
        itemData.put(FragmentModel.TITLE, this.getTitle());
        return itemData;
    }
}
