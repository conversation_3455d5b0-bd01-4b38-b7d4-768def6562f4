package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.FaqImportStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 标准问导入过程
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-24 10:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaqImportProcessModel {

    /**
     *  主键
     */
    private Long id;

    /**
     *  创建时间
     */
    private Date createTime;

    /**
     *  更新时间
     */
    private Date modifyTime;

    /**
     * 发起人mis
     */
    private String mis;

    /**
     * 发起人名称
     */
    private String name;

    /**
     * 状态
     */
    private FaqImportStatusEnum status;

}
