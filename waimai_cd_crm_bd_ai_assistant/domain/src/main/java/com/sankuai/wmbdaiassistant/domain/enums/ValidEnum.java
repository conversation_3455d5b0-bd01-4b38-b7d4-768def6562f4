package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 有效枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-25 17:04
 */
@Getter
public enum ValidEnum {

    VALID((byte)1, "有效"),
    INVALID((byte)0, "无效"),
    ;

    private Byte code;
    private String desc;

    ValidEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ValidEnum getByCode(Byte code) {
        for (ValidEnum validEnum : values()) {
            if (validEnum.getCode().equals(code)) {
                return validEnum;
            }
        }
        return null;
    }
}
