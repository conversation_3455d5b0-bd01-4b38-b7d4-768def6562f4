package com.sankuai.wmbdaiassistant.domain.transaction;

import java.util.function.Supplier;

/**
 * 事务提供器
 */
public interface TransactionHelper {

    /**
     * 在事务中执行（有返回结果）
     *
     * @param supplier 需要在事务中执行的逻辑
     * @return 返回事务中执行返回的数据
     */
    <T> T doInTransaction(Supplier<T> supplier);

    /**
     * doInTransactionWithOutResult
     *
     * @param runnable Runnable
     */
    void doInTransactionWithOutResult(Runnable runnable);
}
