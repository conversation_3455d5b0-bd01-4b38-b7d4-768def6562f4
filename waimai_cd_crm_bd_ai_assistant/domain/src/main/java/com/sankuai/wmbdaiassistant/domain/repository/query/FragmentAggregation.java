package com.sankuai.wmbdaiassistant.domain.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc 聚合查询条件
 * <AUTHOR>
 * @Date 2025/3/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FragmentAggregation {

    /**
     * 聚合字段
     */
    private String field;

    /**
     * 分组最少文档数量
     */
    private Integer minDocCount ;

    /**
     * 数据量
     */
    private Integer size;

    /**
     * 子聚合
     */
    private FragmentAggregation subAggregation;
}
