package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.enums.ReferenceDocTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.WikiStateEnum;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.model.WikiModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.WikiQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.element.ReferenceDoc;
import com.sankuai.wmbdaiassistant.domain.service.dataset.WikiSourceService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 默认的基于分片的大语言模型rag
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-04 15:16
 */
@Slf4j
@Component("defaultFragmentLlmRag")
public class DefaultFragmentLlmRag extends AbsFragmentLlmRag {

    @Resource
    private WikiSourceService wikiSourceService;

    @MdpConfig("refer_prefix:\n\n【参考来源】")
    public String referPrefix;

    @MdpConfig("markdown.reference.source.list:[\"dx_edi_private_chat\",\"dx_edi_group_chat\",\"dx_private_chat\",\"dx_group_chat\",\"dove_app\"]")
    private ArrayList<String> markdownReferenceSourceList;

    @Override
    public String buildResponseMessage(String message, Boolean lastOne, List<FragmentModel> fragmentModelList, String source) {
        List<Object> contentObjList = new ArrayList<>();
        contentObjList.add(chatContentConverter.buildMarkdown(message));
        if (lastOne) {
            if (CollectionUtils.isNotEmpty(fragmentModelList)) {
                if (CollectionUtils.isNotEmpty(markdownReferenceSourceList) && markdownReferenceSourceList.contains(source)) {
                    contentObjList.add(chatContentConverter.buildMarkdown(buildReferLinks(fragmentModelList)));
                }
                else {
                    contentObjList.add(buildReferDocs(fragmentModelList));
                }
            }
        }
        return JsonUtil.toJson(contentObjList);
    }

    public String buildReferLinks(List<FragmentModel> fragmentModelList) {
        if (CollectionUtils.isEmpty(fragmentModelList)) {
            return "";
        }
        // SOP来源不展示
        if (DefaultUtil.defaultBoolean(aiChatConfig.sopFilterSwitch)) {
            fragmentModelList = fragmentModelList.stream()
                    .filter(f -> !wikiSourceService.isSop(f.getWikiId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(fragmentModelList)) {
            return "";
        }
        String referLinks = fragmentModelList.stream().flatMap(f -> {
            WikiQuery query = WikiQuery.builder().state(WikiStateEnum.ENABLE).datasetId(f.getDatasetId()).wikiId(f.getWikiId()).build();
            return DefaultUtil.defaultList(wikiRepository.findAll(query)).stream();
        }).map(w -> String.format("[%s](%s)", w.getTitle(), w.getUrl())).distinct().collect(Collectors.joining("、"));
        return referPrefix + referLinks;
    }

    public ReferenceDoc buildReferDocs(List<FragmentModel> fragmentModelList) {
        if (CollectionUtils.isEmpty(fragmentModelList)) {
            return new ReferenceDoc();
        }
        // SOP来源不展示
        if (DefaultUtil.defaultBoolean(aiChatConfig.sopFilterSwitch)) {
            fragmentModelList = fragmentModelList.stream()
                    .filter(f -> !wikiSourceService.isSop(f.getWikiId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(fragmentModelList)) {
            return new ReferenceDoc();
        }

        List<WikiModel> wikiModels = fragmentModelList.stream().flatMap(f -> {
            WikiQuery query = WikiQuery.builder().state(WikiStateEnum.ENABLE).datasetId(f.getDatasetId()).wikiId(f.getWikiId()).build();
            return DefaultUtil.defaultList(wikiRepository.findAll(query)).stream();
        }).collect(Collectors.collectingAndThen(
                Collectors.toMap(
                        wiki -> wiki.getTitle() + ":" + wiki.getUrl(),  // 组合键
                        wiki -> wiki,
                        (existing, replacement) -> existing  // 根据title和url去重，如果有重复，保留第一个
                ),
                map -> new ArrayList<>(map.values())
        ));


        return ReferenceDoc.builder()
                .info(ReferenceDoc.ReferenceDocInfo.builder()
                        .referenceDoc(ReferenceDoc.ReferenceDocs.builder()
                                .title(wikiModels.isEmpty() ? null : "为你找到 " + wikiModels.size() + " 篇参考资料")
                                .list(wikiModels.stream()
                                        .map(wiki -> ReferenceDoc.Doc.builder()
                                                .type(ReferenceDocTypeEnum.KM.getCode())
                                                .text(wiki.getTitle())
                                                .link(wiki.getUrl())
                                                .build())
                                        .collect(Collectors.toList()))
                                .build())
                        .build())
                .build();
    }

}
