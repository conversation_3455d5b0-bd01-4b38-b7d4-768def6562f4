package com.sankuai.wmbdaiassistant.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自定义配置模型
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Data
public class CustomConfigModel {

    /**
     *   说明: 主键，自增
     */
    private Long id;

    /**
     *   说明: 类型
     */
    private String type;

    /**
     *   说明: 配置
     */
    private ConfigModel config;

    /**
     *   说明: 用户id
     */
    private Integer uid;

    /**
     *   说明: 用户misId
     */
    private String mis;

    /**
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   说明: 更新时间
     */
    private Date modifyTime;

    /**
     *   说明: 租户id
     */
    private Long tenantId;

    public ConfigModel getConfig() {
        if (config == null) {
            config = ConfigModel.builder().build();
        }
        return config;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfigModel {

        /**
         * 工具栏名称顺序（除金刚位top置顶）
         */
        private List<String> toolNameOrder = new ArrayList<>();
    }

}
