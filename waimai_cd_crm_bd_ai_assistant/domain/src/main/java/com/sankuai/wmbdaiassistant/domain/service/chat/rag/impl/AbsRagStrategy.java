package com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.wmbdaiassistant.common.*;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.common.exception.BizException;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.bo.UserBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import com.sankuai.wmbdaiassistant.domain.enums.*;
import com.sankuai.wmbdaiassistant.domain.model.*;
import com.sankuai.wmbdaiassistant.domain.model.TaskModel.TaskConfigModel;
import com.sankuai.wmbdaiassistant.domain.repository.*;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatSessionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.OutputService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.DomainRecognitionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.IntentRecognitionService;
import com.sankuai.wmbdaiassistant.domain.service.chat.authenticate.AuthenticateRoleQueryService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.RagStrategy;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.PhraseVectorQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorDatabaseService;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import com.sankuai.wmbdaiassistant.domain.service.gray.GrayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * RAG策略抽象类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-01 16:58
 */
@Slf4j
public abstract class AbsRagStrategy implements RagStrategy {

    @Resource
    protected GrayService grayService;

    @Resource
    protected AiChatConfig aiChatConfig;

    @Resource
    protected OutputService outputService;

    @Resource
    protected FaqRepository faqRepository;

    @Resource
    protected TaskRepository taskRepository;

    @Resource
    protected TraceLogService traceLogService;

    @Resource
    protected PhraseRepository phraseRepository;

    @Resource
    protected DomainRepository domainRepository;

    @Resource
    protected OutputRepository outputRepository;

    @Resource
    protected ChatMsgRepository chatMsgRepository;

    @Resource
    protected ChatSessionService chatSessionService;

    @Resource
    protected ChatContentConverter chatContentConverter;

    @Resource
    protected VectorDatabaseService vectorDatabaseService;

    @Resource
    protected DomainRecognitionService domainRecognitionService;

    @Resource
    protected IntentRecognitionService intentRecognitionService;

    @Resource
    private AuthenticateRoleQueryService authenticateRoleQueryService;

    @Resource
    private SessionSourceConfigManager sessionSourceConfigManager;

    @MdpConfig("standard.faq.allow.list:[]")
    protected ArrayList<Long> standardFaqAllowList;

    @MdpConfig("picture.question.task.id:38")
    protected Long pictureQuestionTask;

    //本身支持图片交互的工作流，无需走专门的图片task
    @MdpConfig("support.picture.task.id.list:[]")
    protected ArrayList<Long> supportPictureTaskIdList;

    /**
     * 触发大模型重组回答
     *
     * @param sessionBo  会话上下文
     * @param id         消息ID
     * @param input      消息内容
     * @param entryPoint 入口点
     * @param callback   回调
     */
    public abstract void triggerQuery(SessionBo sessionBo, long id, String input
        , String entryPoint, GeneralCallback callback);

    /**
     * 触发任务流回答
     *
     * @param sessionBo  会话上下文
     * @param id         消息ID
     * @param bizId      业务ID
     * @param input      消息内容
     * @param entryPoint 入口点
     * @param callback   回调
     * @param version    版本
     */
    public abstract void triggerTask(SessionBo sessionBo, long id, Long bizId, String input
        , String entryPoint, GeneralCallback callback, String version);

    @Override
    public void query(SessionBo sessionBo, Integer uid, Long bizId, Long id, String input
        , String entryPoint, GeneralCallback callback, String version) {
        try {
            // 增加触发日志
            ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildGeneralChatTriggerTraceLog(
                sessionBo, id, input, entryPoint)));
            trigger(sessionBo, uid, bizId, id, input, entryPoint, wrapCallbackWithTtTransfer(sessionBo, callback), version);
            updateInputMsgContext(id, sessionBo);
            chatSessionService.updateSessionBo(sessionBo, null);
        } catch (Exception e) {
            log.error("AbsRagStrategy chat error, msg = {}", e.getMessage(), e);
            GeneralAnswerBo answer = new GeneralAnswerBo();
            answer.setMsgId(id);
            answer.setAnswer(aiChatConfig.aiChatDefaultMsg);
            answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
            answer.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());
            callback.answerCallback(answer);
        }
    }

    private void trigger(SessionBo sessionBo, Integer uid, long bizId, long id, String input, String entryPoint,
        GeneralCallback callback, String version) {

        sessionBo.refresh(id);
        log.info("AbsRagStrategy trigger，uid = {}, bizId = {}, input = {}, session = {}"
            , uid, bizId, input, JsonUtil.toJson(sessionBo));
        List<String> roles = authenticateRoleQueryService.getUserRoleListByUid(uid);

        //输入中包含图片字段 && 当前所处工作流本身不支持图片交互
        if (isPictureQuestion(input) && !supportPictureTaskIdList.contains(sessionBo.fetchTaskId())) {
            triggerPictureQuestion(sessionBo, bizId, id, input, entryPoint, callback, version);
            return;
        }

        // 当前已经处于多轮中
        if (sessionBo.inTask()) {
            triggerTaskWithPermission(sessionBo, id, bizId, input, entryPoint, callback, version, roles);
            return;
        }

        // 域识别
        Long domainId = domainRecognitionService.recognizeDomain(input);
        ExecutorUtil.safeExecute(() -> traceLogService
            .insert(TraceLogModel.buildDomainRecognizeTraceLog(sessionBo, id, entryPoint, input, domainId)));

        if (domainId != null) {
            sessionBo.configDomainId(domainId);
        }

        // 向量检索
        PhraseVectorQuery query = PhraseVectorQuery.builder().phrase(input).filterByDomain(Boolean.TRUE)
            .topN(aiChatConfig.aiChatFetchSize)
            .domainPathIdList(domainId == null ? null : Collections.singletonList(domainId)).build();
        List<VectorBo> phraseVectorList = vectorDatabaseService.searchPhrase(query);

        // TODO TEST环境脏数据太多，向量召回失效，兜底全文匹配，未来优化
        if (MdpContextUtils.getHostEnv() == HostEnv.TEST) {
            PhraseModel phraseModel = phraseRepository.findByPhrase(input, domainId);
            if (phraseModel != null) {
                VectorBo vectorBo = new VectorBo();
                vectorBo.setId(phraseModel.getId());
                vectorBo.setVectorId(phraseModel.getVexId());
                vectorBo.setDomainIdList(domainId == null ? new ArrayList<>() : Collections.singletonList(domainId));
                vectorBo.setTenantId(0L);
                vectorBo.setScore(1.0);
                phraseVectorList.add(0,vectorBo);
            }
        }

        if (CollectionUtils.isNotEmpty(phraseVectorList)) {
            sessionBo.configTop1(phraseVectorList.get(0).getId(), phraseVectorList.get(0).getScore());
        }
        ExecutorUtil.safeExecute(() -> traceLogService.insert(
            TraceLogModel.buildPhraseVectorQueryTraceLog(sessionBo, id, entryPoint, query, phraseVectorList)));

        // case1: 精确匹配
        List<VectorBo> faqList = DefaultUtil.defaultList(phraseVectorList).stream()
            .filter(faqVectorBo -> faqVectorBo.getScore() >= aiChatConfig.k1).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(faqList)) {
            greaterAndEqualK1(sessionBo, id, bizId, input, faqList.get(0), entryPoint, callback, version, roles);
            return;
        }

        // case : 小于 k1 的场景，现在有三种场景
        // 场景一：域多轮
        if (domainId != null) {
            DomainModel domainModel = domainRepository.findById(domainId);
            TaskModel taskModel = taskRepository.findById(domainModel.getTaskId());
            sessionBo.configFromTask(taskModel, id);
            triggerTaskWithPermission(sessionBo, id, bizId, input, entryPoint, callback, version, roles);
            return;
        }

        // 场景二：意图识别
        Long phraseId = intentRecognitionService.recognizeIntention(input);
        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildIntentionRecognizeTraceLog(sessionBo,
            id, entryPoint, input, Collections.singletonList(phraseId))));
        if (phraseId != null) {
            triggerIntention(sessionBo, uid, bizId, id, input, entryPoint, phraseId, callback, version, roles);
            return;
        }

        // 场景三：新交互模型（大模型重组）
        triggerQuery(sessionBo, id, input, entryPoint, callback);
    }

    private boolean isPictureQuestion(String question) {
        // todo，json 化并判断是否包含图片组件
        return StringUtils.isNotBlank(question) && question.contains("addition") && question.contains("image");
    }

    private void triggerPictureQuestion(SessionBo sessionBo, long bizId, long id, String input, String entryPoint,
                                        GeneralCallback callback, String version) {
        traceLogService.insert(TraceLogModel.buildPictureQuestionTraceLog(sessionBo, entryPoint));
        TaskModel taskModel = taskRepository.findById(pictureQuestionTask);
        sessionBo.configFromTask(taskModel, id);
        triggerTask(sessionBo, id, bizId, input, entryPoint, callback, version);
    }

    private void greaterAndEqualK1(SessionBo sessionBo, Long msgId, Long bizId, String input, VectorBo vectorBo,
            String entryPoint, GeneralCallback callback, String version, List<String> roles) {

        PhraseModel phrase = phraseRepository.findById(vectorBo.getId());
        if (phrase == null) {
            log.error("Phrase 不存在， id = {}", vectorBo.getId());
            throw new BizException(BizErrorEnum.PHRASE_NOT_EXIST);
        }

        Long triggerId = phrase.getTriggerId();
        TriggerTypeEnum triggerTypeEnum = phrase.getTriggerType();

        log.info("AbsRagStrategy.greaterAndEqualK1,vectorBo.getId()={},phrase={},version={},mis={}", vectorBo.getId(), phrase, version, sessionBo.getMis());
        if (VersionEnum.isLatestVersion(version)) {
            AiChatConfig.PhraseGrayModel phraseGrayModel = grayService.getPhraseGrayModel(phrase, new UserBo(sessionBo.getUid(), sessionBo.getMis()));
            if (phraseGrayModel != null) {
                triggerId = phraseGrayModel.getTriggerId();
                triggerTypeEnum = TriggerTypeEnum.getByCode(phraseGrayModel.getTriggerType());
            }
        }

        if (triggerTypeEnum == null) {
            log.error("TriggerTypeEnum 不存在， code = {}", phrase.getTriggerType());
            throw new BizException(BizErrorEnum.ENUM_NOT_EXIST);
        }

        if (triggerTypeEnum == TriggerTypeEnum.FAQ) {
            if (CollectionUtils.isNotEmpty(standardFaqAllowList) && standardFaqAllowList.contains(triggerId)) {
                GeneralAnswerBo answerBo = generalFaqAnswerBo(msgId, triggerId, vectorBo);
                callback.answerCallback(answerBo);
            } else {
                triggerQuery(sessionBo, msgId, input, entryPoint, callback);
            }
            return;
        }

        if (triggerTypeEnum == TriggerTypeEnum.TASK) {
            TaskModel taskModel = taskRepository.findById(triggerId);
            ParamCheckUtil.notNull(taskModel, String.format("触发的多轮不存在, triggerId = %d", triggerId));

            sessionBo.configFromTask(taskModel, msgId);
            triggerTaskWithPermission(sessionBo, msgId, bizId, phrase.getStandardizedPhrase(), entryPoint, callback,
                    version, roles);
        }
    }

    private void triggerIntention(SessionBo sessionBo, Integer uid, long bizId, long questionMsgId, String input,
            String entryPoint, long recognizePhraseId, GeneralCallback callback, String version, List<String> roles) {
        log.info(
            "AbsRagStrategy triggerIntention，id = {}, uid = {}, bizId = {}, input = {},recognizePhraseId={},session = {}",
            questionMsgId, uid, bizId, input, recognizePhraseId, JsonUtil.toJson(sessionBo));
        PhraseModel phraseModel = phraseRepository.findById(recognizePhraseId);
        ParamCheckUtil.isTrue(phraseModel != null && phraseModel.isEnable(),
            "意图识别配置的phraseModel不合法，phraseId=" + recognizePhraseId);

        sessionBo.configIntentRecognitionQuestionId(recognizePhraseId);
        if (TriggerTypeEnum.TASK.equals(phraseModel.getTriggerType())) {
            TaskModel taskModel = taskRepository.findById(phraseModel.getTriggerId());
            sessionBo.configFromTask(taskModel, questionMsgId);
            triggerTaskWithPermission(sessionBo, questionMsgId, bizId, phraseModel.getPhrase(), entryPoint, callback,
                    version, roles);
            return;
        }

        if(TriggerTypeEnum.FAQ.equals(phraseModel.getTriggerType())){
            triggerQuery(sessionBo, questionMsgId, input, entryPoint, callback);
            return;
        }
    }

    private void updateInputMsgContext(Long msgId, SessionBo sessionBo) {
        ChatMsgModel chatMsg = new ChatMsgModel();
        chatMsg.setId(msgId);
        chatMsg.setTaskSessionId(sessionBo.fetchTaskSessionId());
        chatMsg.setContext(JsonUtil.toJson(sessionBo.currentContext()));
        chatMsgRepository.update(chatMsg);
    }

    private GeneralCallback wrapCallbackWithTtTransfer(SessionBo session, GeneralCallback callback) {
        return answerBo -> {
            String answer = ttUrlTransfer(session, answerBo.getAnswer());
            answerBo.setAnswer(answer);
            callback.answerCallback(answerBo);
        };
    }

    private String ttUrlTransfer(SessionBo sessionBo, String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        SessionSourceEntity sessionSourceEntity = sessionSourceConfigManager.getByCode(sessionBo.getSource());
        //tt链接 pc转bee
        if (sessionSourceConfigManager.getAllAppSourceCodeSet().contains(sessionSourceEntity.getCode())) {
            content = StringUtil.replaceByMap(content,
                UrlUtil.findAllPcUrl(content).stream()
                    .filter(url -> UrlUtil.isTTUrl(url, false))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toMap(url -> url, UrlUtil::ttUrlToBeeFormat)));
        }
        //tt链接 bee端转pc
        else {
            content = StringUtil.replaceByMap(content,
                UrlUtil.findAllBeeUrl(content).stream()
                    .filter(url -> UrlUtil.isTTUrl(url, true))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toMap(url -> url, UrlUtil::ttUrlToPcFormat)));
        }
        return content;
    }

    private GeneralAnswerBo generalFaqAnswerBo(Long msgId, Long faqId, VectorBo top1) {
        FaqModel faq = faqRepository.findById(faqId);
        ParamCheckUtil.notNull(faq, String.format("faq不存在：%d", faqId));
        OutputModel output = outputRepository.findById(faq.getAnswerId());
        ParamCheckUtil.notNull(output, String.format("output不存在：%d", faq.getAnswerId()));

        GeneralAnswerBo answer = new GeneralAnswerBo();
        outputService.processOutputToAnswer(output, answer, null);

        answer.setMsgId(msgId);
        answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        answer.setAnswerType(AnswerTypeEnum.STANDARD_FAQ.getCode());
        answer.setTop1FQScore(top1.getScore());
        answer.setTop1QuestionId(top1.getId());
        answer.setTopK(Collections.singletonList(top1));
        return answer;
    }

    private void triggerTaskWithPermission(SessionBo sessionBo, long id, Long bizId, String input, String entryPoint,
            GeneralCallback callback, String version, List<String> roles) {
        Long taskId = sessionBo.currentContext().getTaskId();
        if (!checkTaskPermission(taskId, roles)) {
            triggerQuery(sessionBo, id, input, entryPoint, callback);
            return;
        }
        triggerTask(sessionBo, id, bizId, input, entryPoint, callback, version);
    }

    private boolean checkTaskPermission(Long taskId, List<String> roles) {
        TaskModel taskModel = taskRepository.findById(taskId);
        // 多轮不存在则没有权限
        if (taskModel == null) {
            return false;
        }
        TaskConfigModel taskConfigModel = taskModel.getConfig();
        // 多轮无配置默认有权限
        if (taskConfigModel == null || StringUtils.isBlank(taskConfigModel.getRole())) {
            return true;
        }
        // 多轮角色配置匹配
        return DefaultUtil.defaultList(roles).contains(taskConfigModel.getRole());
    }
}
