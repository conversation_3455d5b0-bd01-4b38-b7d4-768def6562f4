package com.sankuai.wmbdaiassistant.domain.repository.query;

import com.sankuai.wmbdaiassistant.domain.enums.FragmentStateEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分片查询参数
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 18:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FragmentQuery {

    /**
     * ES ID
     */
    private String id;

    /**
     * ES ID
     */
    private List<String> ids;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * wikiID
     */
    private Long wikiId;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * 状态
     */
    private FragmentStateEnum state;

    /**
     * 状态列表
     */
    private List<FragmentStateEnum> stateList;

    /**
     * 分页参数，默认1
     */
    private Integer pageNum;

    /**
     * 分页参数，默认20
     */
    private Integer pageSize;

    /**
     * 所选的目录名称树列表
     */
    private List<String> categories;

    /**
     * utime 开始时间
     */
    private Date updateStartTime;

    /**
     * utime 结束时间
     */
    private Date updateEndTime;
}
