package com.sankuai.wmbdaiassistant.domain.service.chat.chunk;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy.ChunkPatternReplaceFactory;
import com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy.ChunkPatternReplaceStrategy;

import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;

/**
 * 文本块模式替换器
 * 用于处理文本中的特定模式替换，如 {{模式#对象}} 格式
 */
@Component
@Slf4j
public class ChunkPatternReplacer {

    @Resource
    private SessionSourceConfigManager sessionSourceConfigManager;

    @Resource
    private ChunkPatternReplaceFactory chunkPatternReplaceFactory;

    /**
     * 替换文本中的模式
     *
     * @param content 原始内容
     * @param source  来源
     * @return 替换后的内容
     */
    public String replace(String content, String source) {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(source)) {
            return content;
        }
        SessionSourceEntity sessionSourceEntity = sessionSourceConfigManager.getByCode(source);

        // 正则表达式匹配 {{模式#对象}} 格式
        Pattern pattern = Pattern.compile("\\{\\{(.*?)#(.*?)\\}\\}");
        Matcher matcher = pattern.matcher(content);

        while (matcher.find()) {
            String mode = matcher.group(1); // 提取模式
            String value = matcher.group(2); // 提取对象

            // 获取对应的替换策略并执行替换
            if (StringUtils.isNotBlank(mode)) {
                ChunkPatternReplaceStrategy strategy = chunkPatternReplaceFactory.getStrategy(mode);
                String newContent = strategy.replace(mode, value, sessionSourceEntity);
                content = content.replace(matcher.group(0), newContent);
                continue;
            }
            break;
        }

        return content;
    }

    /**
     * 检查模板变量是否完整
     *
     * @param content 内容
     * @return 是否完整
     */
    public boolean isTemplateVariableNotComplete(String content) {
        int openCount = 0;
        int i = 0;

        while (i < content.length() - 1) {
            if (content.charAt(i) == '{' && content.charAt(i + 1) == '{') {
                openCount++;
                i += 2; // 跳过{{
            } else if (content.charAt(i) == '}' && content.charAt(i + 1) == '}') {
                openCount--;
                i += 2; // 跳过}}
            } else {
                i++;
            }
        }

        // 如果openCount > 0，说明有未闭合的{{
        return openCount > 0;
    }
}
