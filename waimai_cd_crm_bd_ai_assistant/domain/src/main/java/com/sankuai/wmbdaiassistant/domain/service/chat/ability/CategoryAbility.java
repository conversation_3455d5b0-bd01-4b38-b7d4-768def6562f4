package com.sankuai.wmbdaiassistant.domain.service.chat.ability;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.exception.BizException;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatSubmitQueryBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.*;
import com.sankuai.wmbdaiassistant.domain.repository.CategoryRepository;
import com.sankuai.wmbdaiassistant.domain.repository.PhraseRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentBuilder;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.hightlight.HighlightService;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 目录能力
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/29 10:03
 */
@Component
@Slf4j
public class CategoryAbility implements BaseAbility {
    @Resource
    private ChatRecordService chatRecordService;

    @Resource
    private ChatContentConverter chatContentConverter;

    @Resource
    private CategoryRepository categoryRepository;

    @Resource
    private PhraseRepository phraseRepository;

    @MdpConfig("chat.category.page.size:5")
    private Integer chatCategoryPageSize;

    @MdpConfig("open.session.category.page.size:5")
    public Integer openSessionCategoryPageSize;

    @MdpConfig("chat.category.pre.content:小蜜猜你想问以下问题，你也可以再详细描述一下遇到的问题哦~")
    public String chatCategoryPreContent;

    @MdpConfig("fetchCategorySubConfigs.high.light.switch:false")
    public Boolean fetchCategorySubConfigsHighLightSwitch;

    @Resource
    private TraceLogService traceLogService;

    @Resource
    private HighlightService highlightService;

    @Resource
    private SessionSourceConfigManager sessionSourceConfigManager;

    @Override
    public AbilityTypeEnum getAbilityType() {
        return AbilityTypeEnum.CATEGORY;
    }

    @Override
    public Long submitQuery(ChatSubmitQueryBo chatSubmitQueryBo) {
        ChatMsgModel msg = chatRecordService.createQueryMsg(chatSubmitQueryBo);
        ParamCheckUtil.notNull(msg, "提问时，创建消息失败");

        // 增加点击日志
        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildCategoryChatTriggerTraceLog(
                chatSubmitQueryBo.getSessionBo(), msg.getId(), chatSubmitQueryBo.getEntryPointType()
                , chatSubmitQueryBo.getContent())));
        return msg.getId();
    }

    @Override
    public ChatFetchAnswerBo fetchAnswer(ChatFetchAnswerRequestBo requestBo) {
        ChatMsgModel questionMsg = chatRecordService.findById(requestBo.getQuestionMsgId());
        ParamCheckUtil.notNull(questionMsg, "问题消息不存在,requestBo=" + requestBo);

        //产品要求在对话过程中，只展示前N个目录子项
        int tenantId = sessionSourceConfigManager.getTenantIdByCode(requestBo.getSessionBo().getSource());
        ChatFetchAnswerBo answerBo = fetchCategorySubConfigs(requestBo.getSessionBo().getUid(),
                questionMsg.getContent(), 1, chatCategoryPageSize, tenantId);

        // 增加曝光日志
        ExecutorUtil.safeExecute(() -> traceLogService.batchInsert(TraceLogModel.buildCategoryChatDisplayTraceLog(
                requestBo.getSessionBo(), answerBo.getCurrentContent())));

        return answerBo;
    }

    /**
     * 获取挂在当前目录的配置数据 单层不带TAB
     *
     * @param name
     * @param pageNum
     * @param pageSize
     * @return
     */
    public ChatFetchAnswerBo fetchCategorySubConfigs(Integer uid, String name, Integer pageNum, Integer pageSize, Integer tenantId) {
        ParamCheckUtil.notBlank(name, "目录名称不能为空");
        pageNum = pageNum == null ? 1 : pageNum;
        pageSize = pageSize == null ? this.chatCategoryPageSize : pageSize;

        List<CategoryConfigModel> configs = this.selectConfigs(name, pageNum, pageSize, uid, tenantId);
        while (configs.size() == 1 && configs.get(0).getAbilityType() == AbilityTypeEnum.CATEGORY.getCode()) {
            log.info(String.format("name=%s 目录配置只有一个子目录,重新获取配置信息,configs=%s", name, JsonUtil.toJson(configs)));
            configs = this.selectConfigs(configs.get(0).getContent(), pageNum, pageSize, uid, tenantId);
        }
        if (fetchCategorySubConfigsHighLightSwitch) {
            configHighlight(uid, configs, tenantId);
        }

        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        chatFetchAnswerBo.setAbilityType(AbilityTypeEnum.CATEGORY.getCode());
        chatFetchAnswerBo.setHasNext(false);
        chatFetchAnswerBo.setPageNum(1);


        String content = new ChatContentBuilder().add(chatContentConverter.buildText(chatCategoryPreContent)).add(chatContentConverter.buildOptions(configs)).build();
        chatFetchAnswerBo.setCurrentContent(content);

        return chatFetchAnswerBo;
    }

    /**
     * 获取首页引导问配置信息
     * @param tabName 首页引导问tab
     * @param pageNum 分页号
     * @return
     */
    public ChatFetchAnswerBo selectGuidanceQuestion(Integer uid, String tabName, Integer pageNum, Integer tenantId) {
        ParamCheckUtil.isTrue(StringUtils.isNotBlank(tabName), "tab名称不能为空");
        ParamCheckUtil.isTrue(pageNum != null && pageNum > 0, "分页号不合法,pageNum=" + pageNum);

        List<CategoryConfigModel> tabConfigs = getOpenSessionTabConfigs(uid, tenantId);
        long count = this.countConfigs(tabName, tenantId);
        pageNum = (long)openSessionCategoryPageSize * (pageNum - 1) < count ? pageNum : 1;
        int nextPageNum =  (long)pageNum * openSessionCategoryPageSize < count ? pageNum + 1 : 1;
        boolean hasNext = openSessionCategoryPageSize < count;

        List<CategoryConfigModel> configs = this.selectConfigs(tabName, pageNum, openSessionCategoryPageSize, uid, tenantId);
        configHighlight(uid, configs, tenantId);
        String content = new ChatContentBuilder().add(chatContentConverter.buildOptions(configs,tabConfigs)).build();

        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        chatFetchAnswerBo.setAbilityType(AbilityTypeEnum.CATEGORY.getCode());
        chatFetchAnswerBo.setHasNext(hasNext);
        chatFetchAnswerBo.setPageNum(nextPageNum);
        chatFetchAnswerBo.setCurrentContent(content);

        return chatFetchAnswerBo;
    }


    /**
     * 获取当前目录下挂载的配置信息（目录+问法）
     * @param name 当前目录名称
     * @param pageNum 分页号
     * @param pageSize 分页大小
     * @return 配置信息列表
     */
    private List<CategoryConfigModel> selectConfigs(String name, Integer pageNum, Integer pageSize, Integer uid, Integer tenantId) {

        if(StringUtils.isBlank(name) || pageNum == null || pageSize == null){
            return Collections.emptyList();
        }
        List<String> hotQuestionTabNames = Lion.getConfigRepository().getList("open.session.hot.questions.tab.name.all_" + tenantId, String.class);
        if (hotQuestionTabNames.contains(name)) {
            return getOpenSessionHotQuestionConfig(name, pageNum, pageSize, tenantId);
        }
        return getCategoryConfig(name, pageNum, pageSize);
    }

    private List<CategoryConfigModel> getCategoryConfig(String name, int pageNum, int pageSize) {
        Integer minSortOrder = (pageNum - 1) * pageSize + 1;
        Integer maxSortOrder = minSortOrder + pageSize - 1;

        CategoryModel parentNode = categoryRepository.findByName(name);
        ParamCheckUtil.notNull(parentNode, "目录不存在:" + name);
        List<Categorizable> childrenNodes = new ArrayList<>();
        childrenNodes.addAll(categoryRepository.findByParentId(parentNode.getId(), minSortOrder, maxSortOrder));
        childrenNodes.addAll(phraseRepository.findByCategoryId(parentNode.getId(), minSortOrder, maxSortOrder));
        childrenNodes.sort(Comparator.comparing(Categorizable::getSortOrder));
        List<CategoryConfigModel> configs = childrenNodes.stream().map(this::convertModel2Config)
                .collect(Collectors.toList());
        ParamCheckUtil.notEmpty(configs, String.format("当前目录下挂载的配置信息为空,parentNode=%s", parentNode));
        return configs;
    }

    private long countConfigs(String name, int tenantId) {
        if (StringUtils.isBlank(name)) {
            return 0;
        }

        Map<String, ArrayList> hotQuestionTabConfigMap = Lion.getConfigRepository().getMap("open.session.hot.questions.configs_"+tenantId, ArrayList.class);

        for (Map.Entry<String, ArrayList> entry : hotQuestionTabConfigMap.entrySet()) {
            if (entry.getKey().equals(name)) {
                return entry.getValue().size();
            }
        }

        long count = 0;
        CategoryModel parentNode = categoryRepository.findByName(name);
        ParamCheckUtil.notNull(parentNode, "目录不存在:" + name);
        count += categoryRepository.countByParentId(parentNode.getId());
        count += phraseRepository.countByCategoryIdList(Collections.singletonList(parentNode.getId()));
        return count;
    }

    /**
     * 获取热门问题配置
     * @param pageNum
     * @param pageSize
     * @return
     */
    private List<CategoryConfigModel> getOpenSessionHotQuestionConfig(String name, int pageNum, int pageSize, int tenantId) {


        int minSortOrder = (pageNum - 1) * pageSize + 1;
        int maxSortOrder = minSortOrder + pageSize - 1;

        Map<String, List> hotQuestionTabConfigMap = Lion.getConfigRepository().getMap("open.session.hot.questions.configs_"+tenantId, List.class);
        List<?> rawConfigList = hotQuestionTabConfigMap.get(name);

        if (CollectionUtils.isEmpty(rawConfigList)) {
            return Collections.emptyList();
        }

        // 需要进行下面的操作，如果直接强转，会报错
        List<CategoryConfigModel> openSessionHotQuestionConfig = rawConfigList.stream()
                .filter(item -> item instanceof LinkedHashMap) // 过滤出 LinkedHashMap 类型的元素
                .map(item -> {
                    LinkedHashMap<?, ?> map = (LinkedHashMap<?, ?>) item;
                    return CategoryConfigModel.builder()
                            .content((String) map.get("content"))
                            .abilityType((Integer) map.get("abilityType"))
                            .domainId(map.get("domainId") != null ? Long.valueOf(map.get("domainId").toString()) : null)
                            .highlight(map.get("highlight") instanceof Boolean ? (Boolean) map.get("highlight") : false)
                            .build();
                })
                .collect(Collectors.toList());

        maxSortOrder = Math.max(1, Math.min(maxSortOrder, openSessionHotQuestionConfig.size()));
        minSortOrder = Math.max(1, Math.min(minSortOrder, maxSortOrder));

        List<CategoryConfigModel> subConfigs = openSessionHotQuestionConfig.subList(minSortOrder - 1, maxSortOrder);

        return subConfigs;
    }

    /**
     * 获取首页推荐问 Tab配置信息
     * @return
     */
    private List<CategoryConfigModel> getOpenSessionTabConfigs(Integer uid, Integer tenantId) {
        List<CategoryConfigModel> tabConfigs = new ArrayList<>();
        List<String> hotQuestionTabNames = Lion.getConfigRepository().getList("open.session.hot.questions.tab.name.all_" + tenantId, String.class);
        for (String hotQuestionTabName : hotQuestionTabNames) {
            tabConfigs.add(CategoryConfigModel.builder().content(hotQuestionTabName).highlight(false).build());
        }
        List<String> openSessionTabConfigs = Lion.getConfigRepository().getList("open.session.tab.configs_" + tenantId, String.class);
        if (CollectionUtils.isEmpty(openSessionTabConfigs)) {
            return tabConfigs;
        }
        for (String tab : openSessionTabConfigs) {
            tabConfigs.add(CategoryConfigModel.builder().content(tab).domainId(null)
                    .abilityType(AbilityTypeEnum.CATEGORY.getCode()).highlight(false).build());
        }
        configHighlight(uid, tabConfigs, tenantId);
        return tabConfigs;
    }

    /**
     * 目录挂载子项模型 转化为 目录配置模型
     * @param model
     * @return
     */
    private CategoryConfigModel convertModel2Config(Categorizable model) {
        if (model instanceof CategoryModel) {
            CategoryModel categoryModel = (CategoryModel)model;
            return CategoryConfigModel.builder().abilityType(AbilityTypeEnum.CATEGORY.getCode())
                    .content(categoryModel.getName()).build();
        }
        if (model instanceof PhraseModel) {
            PhraseModel phraseModel = (PhraseModel)model;
            return CategoryConfigModel.builder().abilityType(AbilityTypeEnum.GENERAL.getCode())
                    .content(phraseModel.getPhrase()).build();
        }
        throw new BizException("目录配置类型错误");
    }

    private void configHighlight(Integer uid, List<CategoryConfigModel> configs, Integer tenantId) {
        if (CollectionUtils.isEmpty(configs) || uid == null) {
            return;
        }
        for (CategoryConfigModel config : configs) {
            config.setHighlight(highlightService.isHighlight(uid, config, tenantId));
        }
    }
}
