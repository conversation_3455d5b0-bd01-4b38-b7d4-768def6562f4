package com.sankuai.wmbdaiassistant.domain.repository.query;

import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import com.sankuai.wmbdaiassistant.domain.enums.SessionStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 会话查询
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-05-24 16:05
 */
@Data
public class SessionQuery {

    /**
     * 用户UID(SSO)
     */
    private Integer uid;

    /**
     * mis
     */
    private String mis;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 会话ID
     */
    private Long sessionId;

    /**
     * 会话标签（需要包含的标签）
     */
    private Set<String> tags = new HashSet<>();

    /**
     * 会话标签（不包含的标签）
     */
    private Set<String> noTags = new HashSet<>();

    /**
     * extra的key-value对(存在)
     */
    private Set<String> extraItems = new HashSet<>();

    /**
     * extra的key-value对(不存在)
     */
    private Set<String> noExtraItems = new HashSet<>();

    /**
     * 会话状态列表
     */
    private List<SessionStatusEnum> statusList;

    /**
     * 来源列表
     */
    private List<SessionSourceEntity> sourceList;

    /**
     * 会话ID列表
     */
    private List<Long> sessionIdList;

    /**
     * 分页参数：第几页，从1开始
     */
    private Integer pageNum;

    /**
     * 分页参数：每页的条数
     */
    private Integer pageSize;

    /**
     * 排序逻辑
     */
    private String orderBy;

    public void addTag(String tag) {
        if (StringUtils.isNotBlank(tag)) {
            tags.add(String.format("%%;%s;%%", tag));
        }
    }

    public void noContainTag(String tag) {
        if (StringUtils.isNotBlank(tag)) {
            noTags.add(String.format("%%;%s;%%", tag));
        }
    }

    public void addExtraItem(String key, Object value) {
        if (StringUtils.isNotBlank(key) && Objects.nonNull(value)) {
            extraItems.add(String.format("%%\"%s\":%s%%", key, JsonUtil.toJson(value)));
        }
    }

    public void noContainExtraItem(String key, Object value) {
        if (StringUtils.isNotBlank(key) && Objects.nonNull(value)) {
            noExtraItems.add(String.format("%%\"%s\":%s%%", key, JsonUtil.toJson(value)));
        }
    }
}
