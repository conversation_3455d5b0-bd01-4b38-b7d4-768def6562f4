package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatContentTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.SensitiveStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.ApiModel;
import com.sankuai.wmbdaiassistant.domain.model.ChatMsgModel;
import com.sankuai.wmbdaiassistant.domain.repository.ApiRepository;
import com.sankuai.wmbdaiassistant.domain.repository.ChatMsgRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.apiarrange.ApiArrangeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 多轮接口调用的模式处理器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 11:09
 */
@Slf4j
@Component
public class TaskApiPatternProcessor implements PatternProcessor {

    private static final String PREFIX = "Api:";
    private static Pattern API_PATTERN = Pattern.compile(PREFIX + "\\d+");

    @Resource
    private ApiRepository apiRepository;

    @Resource
    private ApiArrangeService apiArrangeService;

    @Lazy
    @Resource
    private PatternFactory patternFactory;

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @MdpConfig("task.api.dsl.replace.service.name.map:{\"com.sankuai.wmbdaiassistant.server.service.dsl.SessionDslService\":\"SessionDslService\",\"com.sankuai.wmbdaiassistant.server.service.dsl.ParamExtractDslService\":\"ParamExtractDslService\"}")
    private HashMap<String, String> needReplaceDslServiceNameMap;

    @Override
    public boolean match(String pattern) {
        return API_PATTERN.matcher(pattern).matches();
    }

    @Override
    public boolean process(PatternParam param) {
        String pattern = param.getPattern();

        Long apiId = Long.valueOf(pattern.substring(pattern.indexOf(PREFIX) + PREFIX.length()).trim());
        ApiModel apiModel = apiRepository.findById(apiId);
        ParamCheckUtil.notNull(apiModel, "TaskApiPatternProcessor API 不存在， pattern = " + pattern);

        String dsl = fetchDsl(apiModel);

        String response = apiArrangeService.arrange(dsl, JsonUtil.toJson(param.getSession()));
        String result = StringUtil.unescape(StringUtil.trimQuotation(StringUtil.trim(response)));

        log.info("TaskApiPatternProcessor result = {}, session = {}", result, JsonUtil.toJson(param.getSession()));

        if (patternFactory.canProcess(result)) {
            param.setPattern(result);
            insertAiMsg(result, param.getSession());
            return patternFactory.process(param);
        }

        return false;
    }
    
    private String fetchDsl(ApiModel apiModel) {
        String dsl = apiModel.getDsl();
        if (StringUtils.isBlank(dsl)) {
            return dsl;
        }
        for (Map.Entry<String, String> entry : DefaultUtil.defaultMap(needReplaceDslServiceNameMap).entrySet()) {

            String replaceServiceName = entry.getKey();
            String replaceValue = entry.getValue();

            if (!dsl.contains(replaceServiceName)) {
                continue;
            }
            dsl = dsl.replace(replaceServiceName, replaceValue);
        }
        return dsl;
    }

    private void insertAiMsg(String pattern, SessionBo sessionBo) {
        ChatMsgModel aiMsg = new ChatMsgModel();
        aiMsg.setSessionId(sessionBo.getSessionId());
        aiMsg.setUid(sessionBo.getUid());
        aiMsg.setMis(sessionBo.getMis());
        aiMsg.setAbilityType(AbilityTypeEnum.GENERAL);
        aiMsg.setContentType(ChatContentTypeEnum.ANSWER);
        aiMsg.setAnswerType(AnswerTypeEnum.API_EMBEDDING);
        aiMsg.setContent(pattern);
        aiMsg.setSensitiveStatus(SensitiveStatusEnum.NORMAL);
        aiMsg.setTaskSessionId(sessionBo.fetchTaskSessionId());
        aiMsg.setCreateTime(new Date());
        aiMsg.setContext(JsonUtil.toJson(sessionBo.currentContext()));

        chatMsgRepository.insert(aiMsg);
    }

}
