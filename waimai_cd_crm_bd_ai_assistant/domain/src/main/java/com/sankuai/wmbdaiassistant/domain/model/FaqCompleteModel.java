package com.sankuai.wmbdaiassistant.domain.model;

import java.util.List;

import com.sankuai.wmbdaiassistant.domain.enums.OutputTypeEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * FAQ 的完整模型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-19 10:59
 */
@Getter
@Setter
@ToString
public class FaqCompleteModel {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 域
     */
    private Long domainId;

    /**
     * 问题
     */
    private String question;

    /**
     * 输出内容
     */
    private String content;

    /**
     * 类型
     */
    private OutputTypeEnum type;

    /**
     * TT链接
     */
    private String ttUrl;

    /**
     * 关联的图片列表
     */
    private List<String> picUrlList;


}
