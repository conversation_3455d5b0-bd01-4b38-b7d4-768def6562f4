package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.repository.ChatMsgRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 多轮跳出的模式处理器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 11:07
 */
@Slf4j
@Component
public class TaskBreakPatternProcessor implements PatternProcessor {

    @Resource
    private ChatMsgRepository chatMsgRepository;

    @Override
    public boolean match(String pattern) {
        return "-1".equals(pattern);
    }

    @Override
    public boolean process(PatternParam param) {
        SessionBo session = param.getSession();
        session.resetTaskState();

        // 将当前消息的上下文重置一下
        chatMsgRepository.updateTaskSessionIdAndContextNull(param.getMsgId());
        return true;
    }
}
