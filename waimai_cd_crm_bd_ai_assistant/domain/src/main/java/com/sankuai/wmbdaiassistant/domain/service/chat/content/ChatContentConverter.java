package com.sankuai.wmbdaiassistant.domain.service.chat.content;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatFetchAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SubAbilityConfigDataBo;
import com.sankuai.wmbdaiassistant.domain.enums.AbilityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.OperationTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ServiceScoreSubAbilityEnum;
import com.sankuai.wmbdaiassistant.domain.model.CategoryConfigModel;
import com.sankuai.wmbdaiassistant.domain.model.SubAbilityConfigModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.element.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 会话内容的转换（兼容逻辑）
 * wiki : https://km.sankuai.com/collabpage/2263992064
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-11 16:05
 */
@Slf4j
@Component
public class ChatContentConverter {

    private final Pattern IMAGE_OR_VIDEO_PATTERN = Pattern.compile("!\\[(.*?)\\|.*?]\\((.*?)\\)");
    private final Pattern LINK_PATTERN = Pattern.compile("(?<!\\!)\\[(.*?)\\|(.*?)\\]");
    private final Pattern MARKDOWN_IMAGE_OR_LINK_PATTERN = Pattern.compile("(!?\\[[^\\]]+\\]\\([^\\)]+\\))");

    private final String IMAGE_PREFIX = "pic";
    private final String VIDEO_PREFIX = "video";

    @MdpConfig("default.error.msg:服务异常，请刷新会话后重试")
    private String defaultErrorMsg;

    /**
     * TT工单格式
     */
    @MdpConfig("faq.suffix.tt.format:TT工单")
    public String faq_suffix_tt_format;

    public String mergeList(String previous, String current) {
        List<ContentElement> objList = new ArrayList<>();

        try {
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(previous), objList::addAll);
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(current), objList::addAll);
        } catch (Exception e) {
            log.error("ChatContentConverter merge 失败，msg = {}", e.getMessage(), e);
            return null;
        }
        return JsonUtil.toJson(objList);
    }

    public String merge(String previous, String current) {
        List<ContentElement> objList = new ArrayList<>();

        try {
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(previous), objList::addAll);
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(current), objList::addAll);
        } catch (Exception e) {
            log.error("ChatContentConverter merge 失败，msg = {}", e.getMessage(), e);
            return null;
        }
        if (CollectionUtils.isEmpty(objList)) {
            return "";
        }

        List<ContentElement> mergedList = new ArrayList<>();
        ContentElement previousObj = objList.get(0);
        mergedList.add(previousObj);
        for (int i = 1; i < objList.size(); i++) {
            ContentElement cur = objList.get(i);
            if (previousObj.canMerge() && previousObj.getClass() == cur.getClass()) {
                previousObj.merge(cur);
            } else {
                mergedList.add(cur);
                previousObj = cur;
            }
        }

        return JsonUtil.toJson(mergedList);
    }

    public String toMarkdownTextFromJson(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        List<ContentElement> objectList = extractComponentFromText(content);
        StringBuilder stringBuilder = new StringBuilder();
        DefaultUtil.defaultList(objectList).forEach(obj -> stringBuilder.append(obj.toMarkdownText()));
        return stringBuilder.toString();
    }

    public boolean containsNotCompleteMarkdownLinkOrImage(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        text = text.replaceAll(MARKDOWN_IMAGE_OR_LINK_PATTERN.pattern(), "");
        return text.contains("!") || text.contains("[");
    }

    public String toNew(ChatFetchAnswerBo chatFetchAnswerBo) {
        if (chatFetchAnswerBo == null) {
            return null;
        }

        List<ContentElement> objList = new ArrayList<>();

        try {
            ExecutorUtil.executeIfNotEmpty(toNewComponents(chatFetchAnswerBo), objList::addAll);
        } catch (Exception e) {
            log.info("ChatContentConverter toNew 失败，msg = {}", e.getMessage(), e);
            objList.add(Text.build(defaultErrorMsg));
        }
        return CollectionUtils.isNotEmpty(objList) ? JsonUtil.toJson(objList) : "";
    }

    public String toNew(String content) {
        ChatFetchAnswerBo chatFetchAnswerBo = new ChatFetchAnswerBo();
        chatFetchAnswerBo.setCurrentContent(content);
        return toNew(chatFetchAnswerBo);
    }

    private List<ContentElement> toNewComponents(ChatFetchAnswerBo chatFetchAnswerBo) {
        if (chatFetchAnswerBo == null) {
            return null;
        }

        List<ContentElement> data = new ArrayList<>();
        if (StringUtils.isNotBlank(chatFetchAnswerBo.getPreviousContent())) {
            ChatFetchAnswerBo previous = new ChatFetchAnswerBo();
            previous.setCurrentContent(chatFetchAnswerBo.getPreviousContent());
            ExecutorUtil.executeIfNotEmpty(toNewComponents(previous), data::addAll);
        }
        if (StringUtils.isNotBlank(chatFetchAnswerBo.getPrefixTextContent())) {
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(chatFetchAnswerBo.getPrefixTextContent()), data::addAll);
        }
        if (StringUtils.isNotBlank(chatFetchAnswerBo.getCurrentContent())) {
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(chatFetchAnswerBo.getCurrentContent()), data::addAll);
        }
        if (CollectionUtils.isNotEmpty(chatFetchAnswerBo.getItems())) {
            data.add(OptionList.build(chatFetchAnswerBo.getItems().stream()
                    .map(item -> OptionList.OptionItem.builder().content(item.getContent()).url(item.getUrl())
                            .abilityType(item.getAbilityType()).subAbilityType(item.getSubAbilityType())
                            .operationType(item.getOperationType()).build())
                    .collect(Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(chatFetchAnswerBo.getImageList())) {
            chatFetchAnswerBo.getImageList().forEach(url ->
                    data.add(Image.builder().info(Image.ImageInfo.builder().url(url).build()).build()));
        }
        if (StringUtils.isNotBlank(chatFetchAnswerBo.getPostTextContent())) {
            ExecutorUtil.executeIfNotEmpty(extractComponentFromText(chatFetchAnswerBo.getPostTextContent()), data::addAll);
        }
        return data;
    }

    public Object buildText(String content) {
        return Text.build(content);
    }

    public Object buildMarkdown(String content) {
        return Markdown.build(content);
    }

    public Object buildSuffixOptions(String prefix, List<String> questionList) {
        return SuffixOptions.builder().data(SuffixOptions.SuffixOptionsData.builder()
                .dataDesc(SuffixOptions.SuffixOptionDataDesc.builder()
                        .descriptions(prefix)
                        .options(DefaultUtil.defaultList(questionList).stream()
                                .map(question -> SuffixOptions.OptionItem.builder()
                                        .operationType(OperationTypeEnum.CONTINUE_QUERY.getCode())
                                        .abilityType(AbilityTypeEnum.GENERAL.getCode()).content(question).build())
                                .collect(Collectors.toList()))
                        .build()).build()).build();
    }

    public Object buildQuestionOptions(List<String> questionList) {
        ParamCheckUtil.notEmpty(questionList, "ChatContentConverter buildQuestionOptions empty");
        List<OptionList.OptionItem> optionItems = questionList.stream()
                .map(question -> OptionList.OptionItem.builder().content(question)
                        .abilityType(AbilityTypeEnum.GENERAL.getCode())
                        .operationType(OperationTypeEnum.CONTINUE_QUERY.getCode()).build())
                .collect(Collectors.toList());
        return OptionList.build(optionItems);
    }

    public Object buildServiceOptions() {
        List<OptionList.OptionItem> optionItems = Arrays.stream(ServiceScoreSubAbilityEnum.values())
                .map(serviceScoreEnum -> OptionList.OptionItem.builder().content(serviceScoreEnum.getDesc())
                        .abilityType(AbilityTypeEnum.SERVICE_SCORE.getCode())
                        .subAbilityType(serviceScoreEnum.getCode())
                        .operationType(OperationTypeEnum.CONTINUE_QUERY.getCode()).build())
                .collect(Collectors.toList());
        return OptionList.build(optionItems);
    }

    public Object buildOptionsBySubAbilityConfig(List<SubAbilityConfigModel> options) {
        ParamCheckUtil.notEmpty(options, "ChatContentConverter buildOptions empty");
        List<OptionList.OptionItem> optionItems = options.stream().map(option -> {
            SubAbilityConfigDataBo subAbilityConfigDataBo = JsonUtil.fromJson(option.getConfig(),
                    SubAbilityConfigDataBo.class);
            if (subAbilityConfigDataBo == null) {
                return null;
            }
            return OptionList.OptionItem.builder().content(option.getName())
                    .abilityType(subAbilityConfigDataBo.getAbilityType())
                    .subAbilityType(subAbilityConfigDataBo.getSubAbilityType())
                    .operationType(subAbilityConfigDataBo.getOperationType()).url(subAbilityConfigDataBo.getLink())
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return OptionList.build(optionItems);
    }

    public Object buildOptions(List<CategoryConfigModel> optionConfigs) {
        ParamCheckUtil.notEmpty(optionConfigs, "ChatContentConverter buildOptions empty");
        return buildOptions(optionConfigs, Collections.emptyList());
    }

    public Object buildOptions(List<CategoryConfigModel> optionConfigs, List<CategoryConfigModel> tabConfigs) {
        ParamCheckUtil.notEmpty(optionConfigs, "ChatContentConverter buildOptions empty");
        List<OptionList.OptionItem> optionItems = optionConfigs.stream()
                .map(option -> OptionList.OptionItem.builder().content(option.getContent())
                        .abilityType(option.getAbilityType()).operationType(OperationTypeEnum.CONTINUE_QUERY.getCode())
                        .isNew(option.isHighlight()).build())
                .collect(Collectors.toList());
        List<OptionList.TabItem> tabItemList = DefaultUtil.defaultList(tabConfigs).stream()
                .map(tab -> OptionList.TabItem.builder().label(tab.getContent()).value(tab.getContent())
                        .isNew(tab.isHighlight()).build())
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(tabItemList) ? OptionList.build(optionItems)
                : OptionList.build(optionItems, tabItemList);
    }

    public Object buildLink(String text, String link) {
        return Link.build(text, link);
    }

    public String buildTtUrl(String ttUrl) {
        if (StringUtils.isBlank(ttUrl)) {
            return "";
        }
        return new ChatContentBuilder()
                .add(buildText("\n【"))
                .add(buildLink(faq_suffix_tt_format, ttUrl))
                .add(buildText("】")).build();
    }

    public Object buildThinkContent(String content) {
        return ThinkContent.build(content, ThinkContent.Status.THINKING);
    }

    public Object buildThinkDoneContent(String content) {
        return ThinkContent.build(content, ThinkContent.Status.DONE);
    }

    public List<ContentElement> extractComponentFromText(String text) {
        if (StringUtils.isBlank(text)) {
            return Collections.emptyList();
        }

        try {
            List<Map<String, Object>> objList = JsonUtil.fromJson(text, new TypeReference<List<Map<String, Object>>>() {
            });
            return extractComponentFromJson(objList);
        } catch (Exception e) {
            // 忽略异常
        }

        Matcher imageOrVideoMatcher = IMAGE_OR_VIDEO_PATTERN.matcher(text);
        Matcher linkMatcher = LINK_PATTERN.matcher(text);

        int preIndex = 0;
        boolean imageOrVideoFound = imageOrVideoMatcher.find();
        boolean linkFound = linkMatcher.find();

        List<ContentElement> components = new ArrayList<>();
        while (imageOrVideoFound || linkFound) {
            int imageOrVideoStart = imageOrVideoFound ? imageOrVideoMatcher.start() : Integer.MAX_VALUE;
            int linkStart = linkFound ? linkMatcher.start() : Integer.MAX_VALUE;
            if (imageOrVideoStart < linkStart) {
                components.add(Text.builder().content(text.substring(preIndex, imageOrVideoMatcher.start())).build());

                String type = imageOrVideoMatcher.group(1);
                String url = imageOrVideoMatcher.group(2);

                if (StringUtils.equals(type, VIDEO_PREFIX)) {
                    components.add(Video.build(url));
                }
                if (StringUtils.equals(type, IMAGE_PREFIX)) {
                    components.add(Image.build(url));
                }

                preIndex = imageOrVideoMatcher.end();
                imageOrVideoFound = imageOrVideoMatcher.find();
            }

            if (linkStart < imageOrVideoStart) {
                components.add(Text.build(text.substring(preIndex, linkMatcher.start())));
                components.add(Link.build(linkMatcher.group(1), linkMatcher.group(2)));

                preIndex = linkMatcher.end();
                linkFound = linkMatcher.find();
            }
        }

        if (preIndex <= text.length() - 1) {
            components.add(Text.builder().content(text.substring(preIndex)).build());
        }

        return components;
    }

    public String componentToMarkdownText(List<ContentElement> components) {
        if (CollectionUtils.isEmpty(components)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        components.forEach(component -> stringBuilder.append(component.toMarkdownText()));
        return stringBuilder.toString();
    }

    public String jsonToMarkdownText(String json) {
        return componentToMarkdownText(extractComponentFromText(json));
    }

    private List<ContentElement> extractComponentFromJson(List<Map<String, Object>> objList) {
        return DefaultUtil.defaultList(objList).stream().map(obj -> {
            ContentElementTypeEnum typeEnum = ContentElementTypeEnum.getByCode(String.valueOf(obj.get("type")));
            if (typeEnum == null) {
                return null;
            }
            return JsonUtil.fromJson(JsonUtil.toJson(obj), typeEnum.getClazz());
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
