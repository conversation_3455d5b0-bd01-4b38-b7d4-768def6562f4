package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配置元素
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-03-28 14:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Config implements ContentElement {

    private final String type = ContentElementTypeEnum.CONFIG.getCode();

    @JsonProperty("insert")
    private ConfigInfo info;

    @Override
    public String toMarkdownText() {
        return "";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfigInfo {
        private ConfigData config;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfigData {
        private StyleConfig style;
        private String tableTitle;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StyleConfig {
        private String backgroundColor;
    }
}