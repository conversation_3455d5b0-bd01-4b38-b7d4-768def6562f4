package com.sankuai.wmbdaiassistant.domain.service.common.lock;

/**
 * 分布式锁服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/11 上午9:49
 */
public interface DistributedLockService {

    /**
     * 尝试获取分布式锁
     * @param key 锁的键
     * @param timeout 锁超时时间（秒）
     * @return
     */
    boolean tryLock(String key,int timeout);

    /**
     * 尝试获取分布式锁
     * @param key 锁的键
     * @param timeout 锁超时时间（秒）
     * @param retries 最大重试次数
     * @param retryInterval 重试间隔（毫秒）
     * @return 是否获取成功
     */
    boolean tryLock(String key, int timeout, int retries, long retryInterval);

    /**
     * 释放分布式锁
     * @param key 锁的键
     */
    boolean unlock(String key);

}
