package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文本块模式替换策略工厂
 * 负责管理和获取不同的替换策略实现
 */
@Slf4j
@Service
public class ChunkPatternReplaceFactory {
    
    @Resource
    private List<ChunkPatternReplaceStrategy> strategies;
    
    @Resource
    private DefaultChunkPatternReplaceStrategy defaultStrategy;

    /**
     * 获取替换策略
     * 遍历所有策略，使用match方法判断是否匹配
     * @param mode 模式
     * @return 替换策略
     */
    public ChunkPatternReplaceStrategy getStrategy(String mode) {
        if (StringUtils.isBlank(mode)) {
            return defaultStrategy;
        }

        // 遍历所有策略，找到第一个匹配的实现
        return strategies.stream()
                .filter(strategy -> strategy.match(mode))
                .findFirst()
                .orElse(defaultStrategy);
    }
} 