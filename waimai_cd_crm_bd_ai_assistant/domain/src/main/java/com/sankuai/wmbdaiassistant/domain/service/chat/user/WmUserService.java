package com.sankuai.wmbdaiassistant.domain.service.chat.user;

import com.sankuai.wmbdaiassistant.domain.model.crm.platform.CrmPlatformUser;
import java.util.List;
import java.util.Set;

/**
 * 外卖用户服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-09 13:54
 */
public interface WmUserService {

    /**
     * 判断一个用户是否是BD
     *
     * @param uid UID
     * @return 是否是BD
     */
    boolean isBd(Integer uid);

    /**
     * 根据UID查询 mis
     *
     * @param uid UID
     * @return mis
     */
    String getMisByUid(Integer uid);

    /**
     * 根据uid查询用户名
     *
     * @param uid UID
     * @return 中文名
     */
    String getNameByUid(Integer uid);

    /**
     * 根据mis查询uid
     *
     * @param mis
     * @return
     */
    Integer getUidByMis(String mis);

    /**
     * 根据UID查询当前用户所负责的所有的组织架构
     *
     * @param uid UID
     * @return 组织ID列表
     */
    Set<Integer> getOwnerOrgListByUid(Integer uid);

    /**
     * 根据 uid 获取用户的所有的所有组织节点
     *
     * @param uid UID
     * @return 负责的组织架构的完整路径（可能有多个，例如：外卖总部-中南大区-成都）
     */
    List<String> getFullOrgNameListByUid(Integer uid);

    /**
     * 根据mis查询用户CRM平台信息
     * @param mis
     * @return
     */
    CrmPlatformUser getCrmPlatformUser(String mis);
}
