package com.sankuai.wmbdaiassistant.domain.repository.query;

import com.sankuai.wmbdaiassistant.domain.enums.WikiStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * wiki查询
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 16:58
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WikiQuery {

    /**
     * ES ID
     */
    private String id;

    /**
     * ES ID列表
     */
    private List<String> idList;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * wiki id
     */
    private Long wikiId;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 状态
     */
    private WikiStateEnum state;

    /**
     * 状态列表
     */
    private List<WikiStateEnum> stateList;

    /**
     * 分页参数，默认1
     */
    private Integer pageNum;

    /**
     * 分页参数，默认20
     */
    private Integer pageSize;

    /**
     * 文档名称/标题/url
     */
    private String query;

    /**
     * 修改人mis
     */
    private String modifyMis;

    /**
     * 所选的目录名称树列表
     *
     */
    private List<String> categories;

    /**
     * 是否自动更新
     */
    private Boolean autoUpdate;

    /**
     * 是否需要子wiki
     */
    private Boolean needSubWiki;

    /**
     * 是否需要引用wiki
     */
    private Boolean needReferWiki;

    /**
     * 是否有格式问题
     */
    private Boolean hasFormatProblem;
    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * desc or asc
     */
    private String order;

    /**
     * 时间戳字符串（毫秒）起始时间
     */
    private String startTime;

    /**
     * 时间戳字符串（毫秒）结束时间
     */
    private String endTime;
    /**
     * 授权方式，知识库权限：dataset，学城权限：wiki
     */
    private String authType;
}
