package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/11
 **/
public enum ChatAnswerStatusEnum {
    FINISH(1, "回答完成"),
    ANSWERING(0, "回答中");
    private int code;
    private String desc;

    ChatAnswerStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChatAnswerStatusEnum findByCode(Integer code) {
        for (ChatAnswerStatusEnum status : ChatAnswerStatusEnum.values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return null;
    }

    public static boolean isFinished(Integer code) {
        return Objects.equals(FINISH.getCode(), code);
    }
}
