package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.CategoryModel;
import java.util.List;

/**
 * 目录仓储层
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/23 19:19
 */
public interface CategoryRepository {

    /**
     * 根据ID查询目录
     * @param id
     * @return
     */
    CategoryModel findById(Long id);

    /**
     * 根据父ID查询子目录
     * @param id
     * @return
     */
    List<CategoryModel> findByParentId(Long id);

    /**
     * 根据父ID查询子目录并限制排序位次
     *
     * @param id
     * @return
     */
    List<CategoryModel> findByParentId(Long id, Integer minSortOrder, Integer maxSortOrder);


    /**
     * 根据路径查询路径下所有目录
     * @param id 目录id
     * @return
     */
    List<CategoryModel> findByPathDepends(Long id);

    /**
     * 根据父ID统计子目录数量
     *
     * @param id
     * @return
     */
    long countByParentId(Long id);

    /**
     * 根据名称查询目录
     * @param name
     * @return
     */
    CategoryModel findByName(String name);


    /**
     * 新增目录
     * @param categoryModel
     * @return
     */
    Long insert(CategoryModel categoryModel);

    /**
     * 更新目录
     * @param categoryModel
     * @return
     */
    boolean update(CategoryModel categoryModel);

}
