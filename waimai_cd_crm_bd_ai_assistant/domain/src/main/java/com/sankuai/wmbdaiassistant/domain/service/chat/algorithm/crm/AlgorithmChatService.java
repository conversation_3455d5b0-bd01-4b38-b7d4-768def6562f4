package com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm;

import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.ChatStreamChunkBo;
import java.util.List;
import java.util.function.Consumer;

/**
 * @description: 算法提供的聊天客户端
 * @author: fengxin21
 * @create: 2025/4/15
 **/
public interface AlgorithmChatService {

    /**
     * 算法提供的流式聊天接口
     * 
     * @param mis
     * @param query
     * @param history
     */
    void chatWithStream(String mis, String query,Long msgId,List<Long> datasetIds, List<ChatRecordBo> history, Consumer<ChatStreamChunkBo> onMessage);
}
