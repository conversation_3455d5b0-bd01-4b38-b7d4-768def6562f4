package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.OutputModel;
import com.sankuai.wmbdaiassistant.domain.repository.OutputRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.OutputService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * 多轮的输出模式处理器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 11:08
 */
@Slf4j
@Component
public class TaskOutputPatternProcessor implements PatternProcessor {

    private static final String PREFIX = "Output:";
    private static Pattern OUTPUT_PATTERN = Pattern.compile(PREFIX + "\\d+[\\s\\S]*");

    @Resource
    private OutputRepository outputRepository;

    @Resource
    private OutputService outputService;

    @Override
    public boolean match(String pattern) {
        return OUTPUT_PATTERN.matcher(pattern).matches();
    }

    @Override
    public boolean process(PatternParam param) {

        String pattern = param.getPattern();
        GeneralAnswerBo answer = param.getAnswer();

        String data = pattern.substring(pattern.indexOf(PREFIX) + PREFIX.length()).trim();
        int index = data.indexOf(":");
        Long outputId = index > 0 ? Long.valueOf(data.substring(0, index)) : Long.valueOf(data);
        String params = index > 0 ? data.substring(index + 1) : null;

        OutputModel outputModel = outputRepository.findById(outputId);
        ParamCheckUtil.notNull(outputModel, "TaskOutputPatternProcessor Output 不存在， pattern = " + pattern);
        outputService.processOutputToAnswer(outputModel, answer, params);

        answer.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
        answer.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());

        return false;
    }
}
