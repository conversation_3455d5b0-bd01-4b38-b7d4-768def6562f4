package com.sankuai.wmbdaiassistant.domain.repository.query;

import java.util.Date;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * 埋点日志查询参数
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/27 13:14
 */
@Data
@Builder
public class TraceLogQuery {

    /**
     * 用户ID（必选）
     */
    private Integer uid;

    /**
     * 筛选事件类型
     */
    private List<String> eventTypes;

    /**
     * 筛选会话范围
     */
    private List<Long> sessionIds;

    /**
     * 筛选入口点(模糊匹配关键字,例如 "%tab%")
     */
    private String entryPointLikeFormat;

    /**
     * 筛选内容（模糊匹配关键字,例如 "%问题%"）
     */
    private String contentLikeFormat;

    /**
     * 筛选开始时间
     */
    private Date startDate;

    /**
     * 筛选结束时间
     */
    private Date endDate;
}
