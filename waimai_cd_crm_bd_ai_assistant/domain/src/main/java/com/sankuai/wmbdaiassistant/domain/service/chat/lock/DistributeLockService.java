package com.sankuai.wmbdaiassistant.domain.service.chat.lock;

/**
 * 分布式锁
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-09-03 10:47
 */
public interface DistributeLockService {

    /**
     * 获取会话（阻塞至超时）
     *
     * @param sessionId    会话ID
     * @param timeoutMills 超时时间（单位毫秒）
     * @return 是否成功获取
     */
    boolean acquireSessionLock(Long sessionId, long timeoutMills);

    /**
     * 释放会话锁
     * @param sessionId 会话ID
     */
    void releaseSessionLock(Long sessionId);
}
