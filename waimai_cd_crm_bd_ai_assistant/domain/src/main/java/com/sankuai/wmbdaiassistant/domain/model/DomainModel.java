package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.ValidEnum;
import java.util.Date;
import lombok.Data;

/**
 * 知识域模型
 * 
 * <AUTHOR>
 * @date 2024/8/23
 */
@Data
public class DomainModel {
    /**
     * 说明: 主键，域id
     */
    private Long id;

    /**
     * 说明: 领域名
     */
    private String domain;

    /**
     * 说明: 域的多轮ID，对应到 bd_ai_assistant_task 表
     */
    private Long taskId;

    /**
     * 说明: 是否有效，1：有效，0:无效
     */
    private ValidEnum valid;

    /**
     * 说明: 创建时间
     */
    private Date createTime;

    /**
     * 说明: 更新时间
     */
    private Date modifyTime;
}
