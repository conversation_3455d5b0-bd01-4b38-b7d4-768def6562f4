package com.sankuai.wmbdaiassistant.domain.config;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.domain.enums.PlatformEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 可配置的会话来源管理器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023/6/6 10:16
 */
@Component
@Slf4j
public class SessionSourceConfigManager  {

    public static final String SESSION_SOURCE_CONFIG_LION_KEY = "session.sources.config";

    // 配置的会话来源
    @MdpConfig("session.sources.config:{}")
    private HashMap<String, SessionSourceEntity> sessionSourcesMap;

    // 定义默认未知来源
    public static final SessionSourceEntity UNKNOWN_SOURCE = new SessionSourceEntity("UNKNOWN","未知来源", PlatformEnum.UNKNOWN.getCode(),false,1,false);
    public static final String BD_AI_ASSISTANT_BEE_CODE = "bdaiassistant_bee";
    public static final String DX_PRIVATE_CHAT_CODE = "dx_private_chat";
    public static final String DX_GROUP_CHAT_CODE = "dx_group_chat";
    public static final String SYSTEM = "system";

    public SessionSourceEntity getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return UNKNOWN_SOURCE;
        }
        return sessionSourcesMap.getOrDefault(code, UNKNOWN_SOURCE);
    }

    public Integer getTenantIdByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return UNKNOWN_SOURCE.getTenantId();
        }
        SessionSourceEntity dynamicSource = sessionSourcesMap.get(code);
        return dynamicSource != null ? dynamicSource.getTenantId() : UNKNOWN_SOURCE.getTenantId();
    }

    public Set<String> getAllAppSourceCodeSet() {
        Set<String> result = new HashSet<>();
        for (SessionSourceEntity source : sessionSourcesMap.values()) {
            if (StringUtils.equals(PlatformEnum.APP.getCode(), source.getPlatformCode())) {
                result.add(source.getCode());
            }
        }
        return Collections.unmodifiableSet(result);
    }

    public List<SessionSourceEntity> getAllDisplaySources() {
        List<SessionSourceEntity> result = new ArrayList<>();
        for (SessionSourceEntity source : sessionSourcesMap.values()) {
            if (source.isDisplay()) {
                result.add(source);
            }
        }
        return Collections.unmodifiableList(result);
    }

    public Set<String> getAllDxSourceCodeSet() {
        Set<String> result = new HashSet<>();
        for (SessionSourceEntity source : sessionSourcesMap.values()) {
            if (source.isDxSource()) {
                result.add(source.getCode());
            }
        }
        return Collections.unmodifiableSet(result);
    }


    public static SessionSourceEntity getByCodeWithLionConfigValue(String code) {
        if (StringUtils.isBlank(code)) {
            return UNKNOWN_SOURCE;
        }
        ConfigRepository config = Lion.getConfigRepository();
        Map<String, SessionSourceEntity> sessionSourceEntityMap = config.getMap(SessionSourceConfigManager.SESSION_SOURCE_CONFIG_LION_KEY, SessionSourceEntity.class);
        return sessionSourceEntityMap.getOrDefault(code, UNKNOWN_SOURCE);
    }

}