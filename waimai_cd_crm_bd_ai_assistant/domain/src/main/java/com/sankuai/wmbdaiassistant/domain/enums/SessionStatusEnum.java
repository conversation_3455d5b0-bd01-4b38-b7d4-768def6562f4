package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/4
 **/
public enum SessionStatusEnum {

    ACTIVE(1, "有效"),
    CLOSED_BY_REFRESHING(2, "被刷新关闭"),
    CLOSED_BY_EXITING(3, "用户主动关闭"),
    TIMEOUT(4, "超时自动关闭");


    private int code;
    private String desc;

    SessionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isActive(int code) {
        return Objects.equals(ACTIVE.getCode(), code);
    }

    public static SessionStatusEnum getByCode(Integer code) {
        for (SessionStatusEnum statusEnum : values()) {
            if (Objects.equals(statusEnum.code, code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
