package com.sankuai.wmbdaiassistant.domain.service.chat.dataset.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.domain.model.wiki.WikiPictureUrlInfoModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.dataset.ContentWikiPictureService;
import com.sankuai.wmbdaiassistant.domain.service.dataset.WikiPictureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 回答内容学城图片服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/5/8 下午14:48
 */
@Slf4j
@Service
public class ContentWikiPictureServiceImpl implements ContentWikiPictureService {

    @Resource
    private WikiPictureService wikiPictureService;

    @MdpConfig("match.http.wiki.picture.rule:\\((https?://.*?)\\)")
    public String matchHttpWikiPictureRule;

    @MdpConfig("match.wikiId.attachmentId.picture.rule:.*/(?:api/file|cdn)/(\\d+)/(\\d+)(?:\\?.*)?")
    public String matchWikiIdAndAttachmentIdPictureRule;

    @Override
    public String replaceContentWikiPictureUrlWithS3Url(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        // 匹配出content中需要转换的学城图片信息
        List<WikiPictureUrlInfoModel> needReplaceWikiPictureUrlInfoList = getNeedReplaceWikiPictureUrl(content);
        // 获取学城图片链接对应的S3url
        setWikiPictureS3Url(needReplaceWikiPictureUrlInfoList);
        // 将content中的原始学城图片url替换为S3url
        return replaceWikiPictureUrlWithS3Url(content, needReplaceWikiPictureUrlInfoList);
    }

    public List<WikiPictureUrlInfoModel> getNeedReplaceWikiPictureUrl(String content) {
        if (StringUtils.isBlank(content)) {
            return new ArrayList<>();
        }
        List<WikiPictureUrlInfoModel> result = new ArrayList<>();

        String markdownPattern = matchHttpWikiPictureRule;
        Pattern mdPattern = Pattern.compile(markdownPattern);
        Matcher mdMatcher = mdPattern.matcher(content);
        while (mdMatcher.find()) {
            String url = mdMatcher.group(1);
            WikiPictureUrlInfoModel linkInfo = parseSingleLink(url);
            if (linkInfo != null) {
                result.add(linkInfo);
            }
        }

        return result;

    }

    public void setWikiPictureS3Url(List<WikiPictureUrlInfoModel> wikiPictureUrlInfoModelList) {

        for (WikiPictureUrlInfoModel wikiPictureUrlInfoModel : wikiPictureUrlInfoModelList) {
            String wikiPictureS3Url = wikiPictureService.getWikiPictureS3Url(wikiPictureUrlInfoModel.getWikiId(),
                    wikiPictureUrlInfoModel.getAttachmentId());
            wikiPictureUrlInfoModel.setWikiPictureS3Url(wikiPictureS3Url);
        }
    }

    public String replaceWikiPictureUrlWithS3Url(String content, List<WikiPictureUrlInfoModel> wikiPictureUrlInfoModelList) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        for (WikiPictureUrlInfoModel wikiPictureUrlInfoModel : wikiPictureUrlInfoModelList) {
            String originUrl = wikiPictureUrlInfoModel.getWikiPictureOriginUrl();
            String s3Url = wikiPictureUrlInfoModel.getWikiPictureS3Url();
            if (StringUtils.isBlank(originUrl) || StringUtils.isBlank(s3Url)) {
                continue;
            }
            content = content.replace(originUrl, s3Url);
        }
        return content;
    }

    public WikiPictureUrlInfoModel parseSingleLink(String url) {
        Pattern pattern = Pattern.compile(matchWikiIdAndAttachmentIdPictureRule);
        Matcher matcher = pattern.matcher(url);
        try {
            if (matcher.matches()) {
                Long wikiId = Long.parseLong(matcher.group(1));
                Long attachmentId = Long.parseLong(matcher.group(2));
                return new WikiPictureUrlInfoModel(url, null, wikiId, attachmentId);
            } else {
                return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }
}