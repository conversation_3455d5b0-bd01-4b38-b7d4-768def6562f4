package com.sankuai.wmbdaiassistant.domain.service.chat.pattern;

import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.domain.bo.GeneralAnswerBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 模式参数
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-02-20 14:46
 */
@Getter
@Builder
@ToString
public class PatternParam {

    /**
     * 当前的消息ID
     */
    private Long msgId;

    /**
     * 模式
     */
    private String pattern;

    /**
     * 回调的结果
     */
    private GeneralAnswerBo answer;

    /**
     * 上下文信息
     */
    private SessionBo session;

    /**
     * 输入
     */
    private String input;

    /**
     * 回调
     */
    private GeneralCallback callback;

    /**
     * 入口点类型
     */
    private String entryPoint;

    private long bizId;

    private String version;


    public void setPattern(String pattern) {
        this.pattern = StringUtil.trim(pattern);
    }
}
